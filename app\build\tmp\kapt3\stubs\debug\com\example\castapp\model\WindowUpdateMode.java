package com.example.castapp.model;

/**
 * 窗口更新模式枚举
 * 用于区分不同的窗口信息更新场景
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0004\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004\u00a8\u0006\u0005"}, d2 = {"Lcom/example/castapp/model/WindowUpdateMode;", "", "(Ljava/lang/String;I)V", "PARTIAL", "FULL", "app_debug"})
public enum WindowUpdateMode {
    /*public static final*/ PARTIAL /* = new PARTIAL() */,
    /*public static final*/ FULL /* = new FULL() */;
    
    WindowUpdateMode() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.example.castapp.model.WindowUpdateMode> getEntries() {
        return null;
    }
}