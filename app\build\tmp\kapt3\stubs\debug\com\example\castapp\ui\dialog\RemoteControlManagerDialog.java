package com.example.castapp.ui.dialog;

/**
 * 🐾 遥控管理对话框 - 精简版
 *
 * 重构后的职责：
 * - UI初始化和Fragment管理
 * - 事件分发到对应的管理器
 * - 对话框生命周期管理
 *
 * 业务逻辑已分离到：
 * - RemoteConnectionManager: 全局连接状态管理
 * - RemoteSenderManager: 发送端业务逻辑
 * - RemoteReceiverManager: 接收端业务逻辑
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0080\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000e\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\tH\u0002J\u0010\u0010\u001b\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\tH\u0002J\u0010\u0010\u001c\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\tH\u0002J\u0010\u0010\u001d\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\tH\u0002J\u0010\u0010\u001e\u001a\u00020\u00192\u0006\u0010\u001f\u001a\u00020\u000fH\u0002J\u0010\u0010 \u001a\u00020\u00192\u0006\u0010\u001f\u001a\u00020\u000fH\u0002J\u0010\u0010!\u001a\u00020\u00192\u0006\u0010\u001f\u001a\u00020\u000fH\u0002J\u0010\u0010\"\u001a\u00020\u00192\u0006\u0010\u001f\u001a\u00020\u000fH\u0002J\u0010\u0010#\u001a\u00020\u00192\u0006\u0010$\u001a\u00020%H\u0002J\b\u0010&\u001a\u00020\u0019H\u0002J\b\u0010\'\u001a\u00020\u0019H\u0002J\u0012\u0010(\u001a\u00020\u00192\b\u0010)\u001a\u0004\u0018\u00010*H\u0016J&\u0010+\u001a\u0004\u0018\u00010%2\u0006\u0010,\u001a\u00020-2\b\u0010.\u001a\u0004\u0018\u00010/2\b\u0010)\u001a\u0004\u0018\u00010*H\u0016J\b\u00100\u001a\u00020\u0019H\u0016J\u001a\u00101\u001a\u00020\u00192\u0006\u0010$\u001a\u00020%2\b\u0010)\u001a\u0004\u0018\u00010*H\u0016J\u0016\u00102\u001a\u00020\u00192\u0006\u00103\u001a\u0002042\u0006\u00105\u001a\u000206J\b\u00107\u001a\u00020\u0019H\u0002J\b\u00108\u001a\u00020\u0019H\u0002J\b\u00109\u001a\u00020\u0019H\u0002J\b\u0010:\u001a\u00020\u0019H\u0002J\b\u0010;\u001a\u00020\u0019H\u0002J\b\u0010<\u001a\u00020\u0019H\u0002J\b\u0010=\u001a\u00020\u0019H\u0002J\u0010\u0010>\u001a\u00020\u00192\u0006\u0010\u001f\u001a\u00020\u000fH\u0002J\u000e\u0010?\u001a\u00020\u00192\u0006\u00103\u001a\u000204J\u0010\u0010@\u001a\u00020\u00192\u0006\u0010A\u001a\u00020\tH\u0002J\u0010\u0010B\u001a\u00020\u00192\u0006\u0010C\u001a\u00020\u000fH\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0013X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0015X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0017X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006D"}, d2 = {"Lcom/example/castapp/ui/dialog/RemoteControlManagerDialog;", "Landroidx/fragment/app/DialogFragment;", "()V", "closeButton", "Landroid/widget/ImageButton;", "connectionManager", "Lcom/example/castapp/manager/RemoteConnectionManager;", "connections", "", "Lcom/example/castapp/model/RemoteSenderConnection;", "gson", "Lcom/google/gson/Gson;", "receiverManager", "Lcom/example/castapp/manager/RemoteReceiverManager;", "receivers", "Lcom/example/castapp/model/RemoteReceiverConnection;", "senderManager", "Lcom/example/castapp/manager/RemoteSenderManager;", "tabLayout", "Lcom/google/android/material/tabs/TabLayout;", "tabPagerAdapter", "Lcom/example/castapp/ui/adapter/RemoteTabPagerAdapter;", "viewPager", "Landroidx/viewpager2/widget/ViewPager2;", "handleConnectClick", "", "connection", "handleControlClick", "handleDeleteClick", "handleEditClick", "handleReceiverConnectClick", "receiver", "handleReceiverControlClick", "handleReceiverDeleteClick", "handleReceiverEditClick", "initViews", "view", "Landroid/view/View;", "loadConnections", "loadReceivers", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onCreateView", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "onDestroy", "onViewCreated", "registerReceiverControlDialog", "receiverId", "", "dialog", "Lcom/example/castapp/ui/dialog/RemoteReceiverControlDialog;", "saveConnections", "saveReceivers", "setupClickListeners", "setupFragmentCallbacks", "setupTabLayout", "showAddConnectionDialog", "showAddReceiverDialog", "showReceiverControlDialog", "unregisterReceiverControlDialog", "updateConnectionInUI", "updatedConnection", "updateReceiverInUI", "updatedReceiver", "app_debug"})
public final class RemoteControlManagerDialog extends androidx.fragment.app.DialogFragment {
    private android.widget.ImageButton closeButton;
    private com.google.android.material.tabs.TabLayout tabLayout;
    private androidx.viewpager2.widget.ViewPager2 viewPager;
    private com.example.castapp.ui.adapter.RemoteTabPagerAdapter tabPagerAdapter;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.RemoteConnectionManager connectionManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.RemoteSenderManager senderManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.RemoteReceiverManager receiverManager = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.castapp.model.RemoteSenderConnection> connections = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.castapp.model.RemoteReceiverConnection> receivers = null;
    @org.jetbrains.annotations.NotNull()
    private final com.google.gson.Gson gson = null;
    
    public RemoteControlManagerDialog() {
        super();
    }
    
    @java.lang.Override()
    public void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void initViews(android.view.View view) {
    }
    
    private final void setupTabLayout() {
    }
    
    private final void setupFragmentCallbacks() {
    }
    
    private final void setupClickListeners() {
    }
    
    private final void handleConnectClick(com.example.castapp.model.RemoteSenderConnection connection) {
    }
    
    private final void handleControlClick(com.example.castapp.model.RemoteSenderConnection connection) {
    }
    
    private final void handleEditClick(com.example.castapp.model.RemoteSenderConnection connection) {
    }
    
    private final void handleDeleteClick(com.example.castapp.model.RemoteSenderConnection connection) {
    }
    
    private final void showAddConnectionDialog() {
    }
    
    /**
     * 注册接收端控制对话框，用于接收连接状态变化通知
     */
    public final void registerReceiverControlDialog(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId, @org.jetbrains.annotations.NotNull()
    com.example.castapp.ui.dialog.RemoteReceiverControlDialog dialog) {
    }
    
    /**
     * 注销接收端控制对话框
     */
    public final void unregisterReceiverControlDialog(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId) {
    }
    
    private final void handleReceiverConnectClick(com.example.castapp.model.RemoteReceiverConnection receiver) {
    }
    
    private final void handleReceiverControlClick(com.example.castapp.model.RemoteReceiverConnection receiver) {
    }
    
    /**
     * 📐 直接显示远程控制对话框
     */
    private final void showReceiverControlDialog(com.example.castapp.model.RemoteReceiverConnection receiver) {
    }
    
    private final void handleReceiverEditClick(com.example.castapp.model.RemoteReceiverConnection receiver) {
    }
    
    private final void handleReceiverDeleteClick(com.example.castapp.model.RemoteReceiverConnection receiver) {
    }
    
    private final void showAddReceiverDialog() {
    }
    
    private final void loadConnections() {
    }
    
    private final void loadReceivers() {
    }
    
    private final void saveConnections() {
    }
    
    private final void saveReceivers() {
    }
    
    /**
     * 更新连接在UI中的显示
     */
    private final void updateConnectionInUI(com.example.castapp.model.RemoteSenderConnection updatedConnection) {
    }
    
    /**
     * 更新接收端在UI中的显示
     */
    private final void updateReceiverInUI(com.example.castapp.model.RemoteReceiverConnection updatedReceiver) {
    }
    
    @java.lang.Override()
    public void onDestroy() {
    }
}