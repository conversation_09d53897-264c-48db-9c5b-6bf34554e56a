package com.example.castapp.remote;

/**
 * 远程被控服务器
 * 固定监听9999端口，等待远程控制发送端连接
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000T\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\b\u000f\u0018\u0000 \'2\u00020\u0001:\u0001\'B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001a\u0010\u0017\u001a\u00020\b2\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u00010\u0019J\u0006\u0010\u001a\u001a\u00020\bJ\u0006\u0010\u001b\u001a\u00020\bJ\u0010\u0010\u001c\u001a\u00020\b2\u0006\u0010\u001d\u001a\u00020\u0011H\u0002J\u0006\u0010\u001e\u001a\u00020\rJ\u0010\u0010\u001f\u001a\u00020\b2\u0006\u0010 \u001a\u00020\u000bH\u0002J\u000e\u0010!\u001a\u00020\r2\u0006\u0010\u001d\u001a\u00020\u0011J\u001a\u0010\"\u001a\u00020\b2\u0012\u0010#\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\b0\u0006J\u00a6\u0001\u0010$\u001a\u00020\b2\u0016\b\u0002\u0010\u0005\u001a\u0010\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\b\u0018\u00010\u00062\u0016\b\u0002\u0010\u0012\u001a\u0010\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\b\u0018\u00010\u00062\u001c\b\u0002\u0010\u0013\u001a\u0016\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\b\u0018\u00010\u00142$\b\u0002\u0010\u000e\u001a\u001e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\r\u0012\u0006\u0012\u0004\u0018\u00010\u000b\u0012\u0004\u0012\u00020\b\u0018\u00010\u000f2*\b\u0002\u0010\t\u001a$\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u0007\u0012\u0006\u0012\u0004\u0018\u00010\u000b\u0012\u0004\u0012\u00020\b\u0018\u00010\nJ\u0006\u0010%\u001a\u00020\rJ\u0006\u0010&\u001a\u00020\bR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u0005\u001a\u0010\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\b\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R0\u0010\t\u001a$\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u0007\u0012\u0006\u0012\u0004\u0018\u00010\u000b\u0012\u0004\u0012\u00020\b\u0018\u00010\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\f\u001a\u0010\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\b\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R*\u0010\u000e\u001a\u001e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\r\u0012\u0006\u0012\u0004\u0018\u00010\u000b\u0012\u0004\u0012\u00020\b\u0018\u00010\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u0010\u001a\u0010\u0012\u0004\u0012\u00020\u0011\u0012\u0004\u0012\u00020\b\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u0012\u001a\u0010\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\b\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\"\u0010\u0013\u001a\u0016\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\b\u0018\u00010\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0015\u001a\u0004\u0018\u00010\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006("}, d2 = {"Lcom/example/castapp/remote/RemoteSenderServer;", "", "()V", "isRunning", "Ljava/util/concurrent/atomic/AtomicBoolean;", "onBitrateUpdate", "Lkotlin/Function1;", "", "", "onConnectionManagementRequest", "Lkotlin/Function4;", "", "onConnectionStateChanged", "", "onConnectionToggle", "Lkotlin/Function3;", "onRemoteControlMessage", "Lcom/example/castapp/websocket/ControlMessage;", "onResolutionUpdate", "onVolumeUpdate", "Lkotlin/Function2;", "webSocketServer", "Lcom/example/castapp/websocket/WebSocketServer;", "broadcastSettingsUpdate", "settings", "", "cleanup", "clearListeners", "handleRemoteControlMessage", "message", "isServerRunning", "sendCurrentSettings", "connectionId", "sendMessageToAllClients", "setOnConnectionStateChangedListener", "listener", "setUIUpdateCallbacks", "startServer", "stopServer", "Companion", "app_debug"})
public final class RemoteSenderServer {
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.example.castapp.remote.RemoteSenderServer INSTANCE;
    public static final int REMOTE_CONTROL_PORT = 9999;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.websocket.WebSocketServer webSocketServer;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.atomic.AtomicBoolean isRunning = null;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onConnectionStateChanged;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super com.example.castapp.websocket.ControlMessage, kotlin.Unit> onRemoteControlMessage;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onBitrateUpdate;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onResolutionUpdate;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.Integer, kotlin.Unit> onVolumeUpdate;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function3<? super java.lang.String, ? super java.lang.Boolean, ? super java.lang.String, kotlin.Unit> onConnectionToggle;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function4<? super java.lang.String, ? super java.lang.String, ? super java.lang.Integer, ? super java.lang.String, kotlin.Unit> onConnectionManagementRequest;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.remote.RemoteSenderServer.Companion Companion = null;
    
    private RemoteSenderServer() {
        super();
    }
    
    /**
     * 启动远程控制服务器
     */
    public final boolean startServer() {
        return false;
    }
    
    /**
     * 停止远程控制服务器
     */
    public final void stopServer() {
    }
    
    /**
     * 检查服务器是否运行中
     */
    public final boolean isServerRunning() {
        return false;
    }
    
    /**
     * 设置连接状态变化监听器
     */
    public final void setOnConnectionStateChangedListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> listener) {
    }
    
    /**
     * 设置UI更新回调
     */
    public final void setUIUpdateCallbacks(@org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onBitrateUpdate, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onResolutionUpdate, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.Integer, kotlin.Unit> onVolumeUpdate, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function3<? super java.lang.String, ? super java.lang.Boolean, ? super java.lang.String, kotlin.Unit> onConnectionToggle, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function4<? super java.lang.String, ? super java.lang.String, ? super java.lang.Integer, ? super java.lang.String, kotlin.Unit> onConnectionManagementRequest) {
    }
    
    /**
     * 发送消息到所有连接的远程控制客户端
     */
    public final boolean sendMessageToAllClients(@org.jetbrains.annotations.NotNull()
    com.example.castapp.websocket.ControlMessage message) {
        return false;
    }
    
    /**
     * 处理远程控制消息
     */
    private final void handleRemoteControlMessage(com.example.castapp.websocket.ControlMessage message) {
    }
    
    /**
     * 发送当前设置状态
     */
    private final void sendCurrentSettings(java.lang.String connectionId) {
    }
    
    /**
     * 广播设置更新到所有连接的控制端
     */
    public final void broadcastSettingsUpdate(@org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.String, ? extends java.lang.Object> settings) {
    }
    
    /**
     * 清理监听器（但保持服务器运行）
     * 用于UI组件销毁时清理监听器，但不停止服务器
     */
    public final void clearListeners() {
    }
    
    /**
     * 完全清理资源（停止服务器）
     * 只应在应用退出或用户明确关闭远程被控功能时调用
     */
    public final void cleanup() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0007\u001a\u00020\u0004R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\b"}, d2 = {"Lcom/example/castapp/remote/RemoteSenderServer$Companion;", "", "()V", "INSTANCE", "Lcom/example/castapp/remote/RemoteSenderServer;", "REMOTE_CONTROL_PORT", "", "getInstance", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.remote.RemoteSenderServer getInstance() {
            return null;
        }
    }
}