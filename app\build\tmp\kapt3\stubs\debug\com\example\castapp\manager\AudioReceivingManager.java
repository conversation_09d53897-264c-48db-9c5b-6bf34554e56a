package com.example.castapp.manager;

/**
 * 音频接收管理器
 * 负责管理音频接收、解码和播放的所有逻辑
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000~\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u0012\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\r\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0014\u0018\u0000 T2\u00020\u0001:\u0002STB\u001d\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u0006\u0010&\u001a\u00020\'J\u000e\u0010(\u001a\u00020\'2\u0006\u0010)\u001a\u00020\u0013JH\u0010*\u001a\u00020\f2\u0006\u0010)\u001a\u00020\u00132\u0006\u0010+\u001a\u00020\u00162\u0012\u0010,\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00180\u00122\u0012\u0010-\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00140\u00122\u0006\u0010.\u001a\u00020\u0013H\u0002J\u0010\u0010/\u001a\u00020\f2\u0006\u0010)\u001a\u00020\u0013H\u0002J\u0010\u00100\u001a\u00020\f2\u0006\u0010)\u001a\u00020\u0013H\u0002J\u000e\u00101\u001a\u00020\u00132\u0006\u00102\u001a\u00020#JT\u00103\u001a\u00020\'2\u0006\u0010)\u001a\u00020\u00132\u0006\u0010.\u001a\u00020\u00132\u0012\u0010,\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00180\u00122\u0012\u0010-\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00140\u00122\u0012\u00104\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\f05H\u0002J\u0010\u00106\u001a\u00020\'2\u0006\u0010)\u001a\u00020\u0013H\u0002J\u0010\u00107\u001a\u00020\'2\u0006\u0010)\u001a\u00020\u0013H\u0002J\\\u00108\u001a\u00020\'2\u0006\u0010)\u001a\u00020\u00132\u0006\u00109\u001a\u00020\f2\u0006\u0010.\u001a\u00020\u00132\u0012\u0010:\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\f0\u00122\u0012\u0010,\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00180\u00122\u0012\u0010;\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\'05H\u0002J~\u0010<\u001a\u00020\'2\u0006\u0010=\u001a\u00020>2\u0006\u0010?\u001a\u00020\u000e2\u0006\u0010.\u001a\u00020\u00132\u0012\u0010:\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\f0\u00122\u0012\u0010,\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00180\u00122\u0012\u0010-\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00140\u00122\u0012\u00104\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\f052\f\u0010@\u001a\b\u0012\u0004\u0012\u00020\'0AH\u0002J\u0016\u0010B\u001a\u00020\'2\u0006\u0010)\u001a\u00020\u00132\u0006\u00109\u001a\u00020\fJ\u0018\u0010C\u001a\u00020\'2\u0006\u0010=\u001a\u00020>2\u0006\u0010?\u001a\u00020\u000eH\u0002J\u0016\u0010D\u001a\u00020\'2\u0006\u0010)\u001a\u00020\u00132\u0006\u00109\u001a\u00020\fJ\u0018\u0010E\u001a\u00020\'2\u0006\u0010=\u001a\u00020>2\u0006\u0010?\u001a\u00020\u000eH\u0002J\u0006\u0010F\u001a\u00020\'J\b\u0010G\u001a\u00020\'H\u0002J\b\u0010H\u001a\u00020\'H\u0002J\\\u0010I\u001a\u00020\'2\u0006\u0010)\u001a\u00020\u00132\u0006\u00102\u001a\u00020#2\u0006\u0010.\u001a\u00020\u00132\u0012\u0010:\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\f0\u00122\u0012\u0010-\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00140\u00122\u0012\u00104\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\f05H\u0002J\u0016\u0010J\u001a\u00020\'2\u0006\u0010)\u001a\u00020\u00132\u0006\u00102\u001a\u00020#J\u0016\u0010K\u001a\u00020\'2\u0006\u0010)\u001a\u00020\u00132\u0006\u00102\u001a\u00020#J\u0016\u0010L\u001a\u00020\'2\u0006\u0010)\u001a\u00020\u00132\u0006\u0010?\u001a\u00020\u000eJ\u000e\u0010M\u001a\u00020\'2\u0006\u0010\u000b\u001a\u00020\fJ\u000e\u0010N\u001a\u00020\'2\u0006\u0010O\u001a\u00020\u0016J\u000e\u0010P\u001a\u00020\f2\u0006\u0010Q\u001a\u00020\u0016J\u0006\u0010R\u001a\u00020\'R\u000e\u0010\t\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00140\u0012X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0017\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00180\u0012X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0019\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\f0\u0012X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001a\u001a\u0004\u0018\u00010\u001bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u001d\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00140\u0012X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001e\u001a\u00020\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u001f\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00180\u0012X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010 \u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\f0\u0012X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010!\u001a\u0004\u0018\u00010\u001bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\"\u001a\u000e\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020#0\u0012X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010$\u001a\u000e\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020#0\u0012X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010%\u001a\u00020\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006U"}, d2 = {"Lcom/example/castapp/manager/AudioReceivingManager;", "", "context", "Landroid/content/Context;", "stateManager", "Lcom/example/castapp/manager/StateManager;", "callback", "Lcom/example/castapp/manager/AudioReceivingManager$AudioReceivingCallback;", "(Landroid/content/Context;Lcom/example/castapp/manager/StateManager;Lcom/example/castapp/manager/AudioReceivingManager$AudioReceivingCallback;)V", "audioSyncManager", "Lcom/example/castapp/audio/AudioSyncManager;", "isSpeakerMode", "", "lastMediaAudioLogTime", "", "lastMicAudioLogTime", "mediaAudioBytesProcessed", "mediaAudioDecoders", "Ljava/util/concurrent/ConcurrentHashMap;", "", "Lcom/example/castapp/audio/AudioDecoder;", "mediaAudioPacketsProcessed", "", "mediaAudioPlayers", "Lcom/example/castapp/audio/AudioPlayer;", "mediaAudioPlayingStates", "mediaAudioReceiver", "Lcom/example/castapp/audio/AudioRtpReceiver;", "micAudioBytesProcessed", "micAudioDecoders", "micAudioPacketsProcessed", "micAudioPlayers", "micAudioPlayingStates", "micAudioReceiver", "pendingMediaAudioConfigs", "", "pendingMicAudioConfigs", "receiverVolume", "cleanupAllAudioComponents", "", "cleanupAudioComponents", "connectionId", "createAudioPlayer", "channelCount", "players", "decoders", "audioType", "createMediaAudioPlayer", "createMicAudioPlayer", "determineAudioTypeFromConfig", "configData", "ensureAudioPlayerReady", "createPlayerFunc", "Lkotlin/Function1;", "ensureMediaAudioPlayerReady", "ensureMicAudioPlayerReady", "handleAudioControl", "isEnabled", "playingStates", "ensurePlayerReadyFunc", "handleAudioDataView", "aacDataView", "Lcom/example/castapp/network/DataView;", "ssrc", "updateStats", "Lkotlin/Function0;", "handleMediaAudioControl", "handleMediaAudioData", "handleMicAudioControl", "handleMicAudioData", "loadAudioOutputMode", "logMediaAudioStatisticsIfNeeded", "logMicAudioStatisticsIfNeeded", "processAudioConfig", "processMediaAudioConfig", "processMicAudioConfig", "processPendingAudioConfigs", "setAudioOutputMode", "setReceiverVolume", "volume", "startAudioReceivers", "basePort", "stopAudioReceivers", "AudioReceivingCallback", "Companion", "app_debug"})
public final class AudioReceivingManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.StateManager stateManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.AudioReceivingManager.AudioReceivingCallback callback = null;
    private static final int AUDIO_SAMPLE_RATE = 48000;
    private static final int MEDIA_AUDIO_CHANNELS = 2;
    private static final int MIC_AUDIO_CHANNELS = 1;
    private static final int MEDIA_AUDIO_PORT_OFFSET = 2;
    private static final int MIC_AUDIO_PORT_OFFSET = 3;
    private static final long LOG_INTERVAL_MS = 120000L;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.audio.AudioRtpReceiver mediaAudioReceiver;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.audio.AudioRtpReceiver micAudioReceiver;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.audio.AudioSyncManager audioSyncManager = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, com.example.castapp.audio.AudioDecoder> mediaAudioDecoders = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, com.example.castapp.audio.AudioDecoder> micAudioDecoders = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, com.example.castapp.audio.AudioPlayer> mediaAudioPlayers = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, com.example.castapp.audio.AudioPlayer> micAudioPlayers = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, java.lang.Boolean> mediaAudioPlayingStates = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, java.lang.Boolean> micAudioPlayingStates = null;
    @kotlin.jvm.Volatile()
    private volatile boolean isSpeakerMode = true;
    @kotlin.jvm.Volatile()
    private volatile int receiverVolume = 80;
    private long lastMediaAudioLogTime = 0L;
    private long lastMicAudioLogTime = 0L;
    private int mediaAudioPacketsProcessed = 0;
    private int micAudioPacketsProcessed = 0;
    private long mediaAudioBytesProcessed = 0L;
    private long micAudioBytesProcessed = 0L;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.Long, byte[]> pendingMediaAudioConfigs = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.Long, byte[]> pendingMicAudioConfigs = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.manager.AudioReceivingManager.Companion Companion = null;
    
    public AudioReceivingManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.example.castapp.manager.StateManager stateManager, @org.jetbrains.annotations.NotNull()
    com.example.castapp.manager.AudioReceivingManager.AudioReceivingCallback callback) {
        super();
    }
    
    /**
     * 启动音频接收器
     */
    public final boolean startAudioReceivers(int basePort) {
        return false;
    }
    
    /**
     * 停止音频接收器
     */
    public final void stopAudioReceivers() {
    }
    
    /**
     * 设置音频输出模式
     */
    public final void setAudioOutputMode(boolean isSpeakerMode) {
    }
    
    /**
     * 设置接收端音量
     */
    public final void setReceiverVolume(int volume) {
    }
    
    /**
     * 加载音频输出模式设置
     */
    public final void loadAudioOutputMode() {
    }
    
    /**
     * 处理媒体音频控制
     */
    public final void handleMediaAudioControl(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean isEnabled) {
    }
    
    /**
     * 处理麦克风音频控制
     */
    public final void handleMicAudioControl(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean isEnabled) {
    }
    
    /**
     * 处理媒体音频配置数据
     */
    public final void processMediaAudioConfig(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    byte[] configData) {
    }
    
    /**
     * 处理麦克风音频配置数据
     */
    public final void processMicAudioConfig(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    byte[] configData) {
    }
    
    /**
     * 清理指定连接的音频组件
     */
    public final void cleanupAudioComponents(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 清理所有音频组件
     */
    public final void cleanupAllAudioComponents() {
    }
    
    /**
     * 根据AAC配置数据确定音频类型
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String determineAudioTypeFromConfig(@org.jetbrains.annotations.NotNull()
    byte[] configData) {
        return null;
    }
    
    /**
     * 处理缓存的音频配置数据
     */
    public final void processPendingAudioConfigs(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, long ssrc) {
    }
    
    /**
     * 🚀 零拷贝优化：通用音频数据处理方法 - 支持DataView
     */
    private final void handleAudioDataView(com.example.castapp.network.DataView aacDataView, long ssrc, java.lang.String audioType, java.util.concurrent.ConcurrentHashMap<java.lang.String, java.lang.Boolean> playingStates, java.util.concurrent.ConcurrentHashMap<java.lang.String, com.example.castapp.audio.AudioPlayer> players, java.util.concurrent.ConcurrentHashMap<java.lang.String, com.example.castapp.audio.AudioDecoder> decoders, kotlin.jvm.functions.Function1<? super java.lang.String, java.lang.Boolean> createPlayerFunc, kotlin.jvm.functions.Function0<kotlin.Unit> updateStats) {
    }
    
    private final void handleMediaAudioData(com.example.castapp.network.DataView aacDataView, long ssrc) {
    }
    
    private final void handleMicAudioData(com.example.castapp.network.DataView aacDataView, long ssrc) {
    }
    
    /**
     * 通用音频播放器创建方法
     */
    private final boolean createAudioPlayer(java.lang.String connectionId, int channelCount, java.util.concurrent.ConcurrentHashMap<java.lang.String, com.example.castapp.audio.AudioPlayer> players, java.util.concurrent.ConcurrentHashMap<java.lang.String, com.example.castapp.audio.AudioDecoder> decoders, java.lang.String audioType) {
        return false;
    }
    
    private final boolean createMediaAudioPlayer(java.lang.String connectionId) {
        return false;
    }
    
    private final boolean createMicAudioPlayer(java.lang.String connectionId) {
        return false;
    }
    
    /**
     * 通用音频控制处理方法
     */
    private final void handleAudioControl(java.lang.String connectionId, boolean isEnabled, java.lang.String audioType, java.util.concurrent.ConcurrentHashMap<java.lang.String, java.lang.Boolean> playingStates, java.util.concurrent.ConcurrentHashMap<java.lang.String, com.example.castapp.audio.AudioPlayer> players, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> ensurePlayerReadyFunc) {
    }
    
    /**
     * 通用确保播放器就绪方法
     */
    private final void ensureAudioPlayerReady(java.lang.String connectionId, java.lang.String audioType, java.util.concurrent.ConcurrentHashMap<java.lang.String, com.example.castapp.audio.AudioPlayer> players, java.util.concurrent.ConcurrentHashMap<java.lang.String, com.example.castapp.audio.AudioDecoder> decoders, kotlin.jvm.functions.Function1<? super java.lang.String, java.lang.Boolean> createPlayerFunc) {
    }
    
    private final void ensureMediaAudioPlayerReady(java.lang.String connectionId) {
    }
    
    private final void ensureMicAudioPlayerReady(java.lang.String connectionId) {
    }
    
    /**
     * 通用音频配置处理核心逻辑
     */
    private final void processAudioConfig(java.lang.String connectionId, byte[] configData, java.lang.String audioType, java.util.concurrent.ConcurrentHashMap<java.lang.String, java.lang.Boolean> playingStates, java.util.concurrent.ConcurrentHashMap<java.lang.String, com.example.castapp.audio.AudioDecoder> decoders, kotlin.jvm.functions.Function1<? super java.lang.String, java.lang.Boolean> createPlayerFunc) {
    }
    
    /**
     * 定期输出媒体音频统计信息
     */
    private final void logMediaAudioStatisticsIfNeeded() {
    }
    
    /**
     * 定期输出麦克风音频统计信息
     */
    private final void logMicAudioStatisticsIfNeeded() {
    }
    
    /**
     * 音频接收管理器回调接口
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J \u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0007\u001a\u00020\u0005H&J\u0018\u0010\b\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0005H&J\u0018\u0010\t\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0005H&J(\u0010\n\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\fH&\u00a8\u0006\u0010"}, d2 = {"Lcom/example/castapp/manager/AudioReceivingManager$AudioReceivingCallback;", "", "onAudioError", "", "connectionId", "", "audioType", "error", "onAudioPlayerCreated", "onAudioPlayerStopped", "onAudioStatistics", "packetsProcessed", "", "bytesProcessed", "", "activeConnections", "app_debug"})
    public static abstract interface AudioReceivingCallback {
        
        public abstract void onAudioPlayerCreated(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
        java.lang.String audioType);
        
        public abstract void onAudioPlayerStopped(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
        java.lang.String audioType);
        
        public abstract void onAudioError(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
        java.lang.String audioType, @org.jetbrains.annotations.NotNull()
        java.lang.String error);
        
        public abstract void onAudioStatistics(@org.jetbrains.annotations.NotNull()
        java.lang.String audioType, int packetsProcessed, long bytesProcessed, int activeConnections);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0002\b\u0005\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/example/castapp/manager/AudioReceivingManager$Companion;", "", "()V", "AUDIO_SAMPLE_RATE", "", "LOG_INTERVAL_MS", "", "MEDIA_AUDIO_CHANNELS", "MEDIA_AUDIO_PORT_OFFSET", "MIC_AUDIO_CHANNELS", "MIC_AUDIO_PORT_OFFSET", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}