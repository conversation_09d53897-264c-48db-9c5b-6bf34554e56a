package com.example.castapp.ui;

/**
 * 主界面Activity - 重构为协调者模式
 * 只负责UI初始化和各管理器的协调，具体功能委托给专门的管理器
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0084\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\n\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010+\u001a\u00020,H\u0002J\u0006\u0010-\u001a\u00020\u0004J\u0016\u0010.\u001a\u00020,2\f\u0010/\u001a\b\u0012\u0004\u0012\u00020100H\u0002J\u0016\u00102\u001a\u00020,2\f\u0010/\u001a\b\u0012\u0004\u0012\u00020100H\u0002J\b\u00103\u001a\u00020,H\u0002J\b\u00104\u001a\u00020,H\u0002J\b\u00105\u001a\u00020,H\u0002J\b\u00106\u001a\u00020,H\u0002J\b\u00107\u001a\u00020,H\u0002J\b\u00108\u001a\u00020,H\u0002J\b\u00109\u001a\u00020,H\u0002J\b\u0010:\u001a\u00020,H\u0002J\b\u0010;\u001a\u00020,H\u0002J\u0012\u0010<\u001a\u00020,2\b\u0010=\u001a\u0004\u0018\u00010>H\u0014J\b\u0010?\u001a\u00020,H\u0014J\b\u0010@\u001a\u00020,H\u0002J\u0018\u0010A\u001a\u00020,2\u0006\u0010B\u001a\u00020C2\u0006\u0010D\u001a\u00020EH\u0002J\b\u0010F\u001a\u00020,H\u0002J\b\u0010G\u001a\u00020,H\u0002J\b\u0010H\u001a\u00020,H\u0002J\b\u0010I\u001a\u00020,H\u0002J\b\u0010J\u001a\u00020,H\u0002J\b\u0010K\u001a\u00020,H\u0002J\b\u0010L\u001a\u00020,H\u0002J\b\u0010M\u001a\u00020,H\u0002J\b\u0010N\u001a\u00020,H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0013X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0015X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0017X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0019X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u001bX\u0082.\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u001c\u001a\u00020\u001d8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b \u0010!\u001a\u0004\b\u001e\u0010\u001fR\u000e\u0010\"\u001a\u00020#X\u0082.\u00a2\u0006\u0002\n\u0000R\u001b\u0010$\u001a\u00020%8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b(\u0010!\u001a\u0004\b&\u0010\'R\u000e\u0010)\u001a\u00020*X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006O"}, d2 = {"Lcom/example/castapp/ui/MainActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "activityPermissionHelper", "Lcom/example/castapp/manager/PermissionManager$ActivityPermissionHelper;", "btnAddMedia", "Landroid/widget/Button;", "btnClear", "btnDirector", "btnLayerManager", "btnReceive", "btnRemoteControl", "btnSave", "btnSend", "btnStopwatch", "btnWindowManager", "buttonGroup", "Landroid/widget/LinearLayout;", "floatingWindowManager", "Lcom/example/castapp/manager/FloatingWindowManager;", "hideShowManager", "Lcom/example/castapp/manager/HideShowManager;", "layoutManager", "Lcom/example/castapp/manager/LayoutManager;", "mainContainer", "Landroid/widget/RelativeLayout;", "precisionControlPanelManager", "Lcom/example/castapp/manager/PrecisionControlPanelManager;", "senderViewModel", "Lcom/example/castapp/viewmodel/SenderViewModel;", "getSenderViewModel", "()Lcom/example/castapp/viewmodel/SenderViewModel;", "senderViewModel$delegate", "Lkotlin/Lazy;", "surfaceContainer", "Landroid/widget/FrameLayout;", "viewModel", "Lcom/example/castapp/viewmodel/MainViewModel;", "getViewModel", "()Lcom/example/castapp/viewmodel/MainViewModel;", "viewModel$delegate", "windowSettingsManager", "Lcom/example/castapp/manager/WindowSettingsManager;", "checkAndRequestPermissions", "", "getActivityPermissionHelper", "handleRestoreLayout", "layoutItems", "", "Lcom/example/castapp/database/entity/WindowLayoutItemEntity;", "handleRestoreLayoutSilently", "handleSaveLayout", "handleShowDirector", "initViews", "initializeHideShowManager", "initializeManagers", "initializeMicrophoneManager", "initializePrecisionControlPanelManager", "observeSenderViewModelPermissionRequests", "observeViewModel", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onDestroy", "preRequestMediaProjectionPermission", "requestMediaProjectionPermissionForSender", "connection", "Lcom/example/castapp/model/Connection;", "featureType", "", "restoreAppliedLayoutOnStartup", "restoreFixedWebSocketServerState", "restoreRemoteControlServerState", "setupClickListeners", "showAddMediaDialog", "showReceiverDialog", "showRemoteControlManagerDialog", "showSenderDialog", "updatePrecisionControlPanels", "app_debug"})
public final class MainActivity extends androidx.appcompat.app.AppCompatActivity {
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy viewModel$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy senderViewModel$delegate = null;
    private com.example.castapp.manager.PermissionManager.ActivityPermissionHelper activityPermissionHelper;
    private android.widget.FrameLayout surfaceContainer;
    private android.widget.Button btnSend;
    private android.widget.Button btnReceive;
    private android.widget.Button btnRemoteControl;
    private android.widget.Button btnStopwatch;
    private android.widget.Button btnLayerManager;
    private android.widget.Button btnWindowManager;
    private android.widget.Button btnSave;
    private android.widget.Button btnDirector;
    private android.widget.Button btnClear;
    private android.widget.Button btnAddMedia;
    private android.widget.LinearLayout buttonGroup;
    private android.widget.RelativeLayout mainContainer;
    private com.example.castapp.manager.PrecisionControlPanelManager precisionControlPanelManager;
    private com.example.castapp.manager.WindowSettingsManager windowSettingsManager;
    private com.example.castapp.manager.FloatingWindowManager floatingWindowManager;
    private com.example.castapp.manager.LayoutManager layoutManager;
    private com.example.castapp.manager.HideShowManager hideShowManager;
    
    public MainActivity() {
        super();
    }
    
    private final com.example.castapp.viewmodel.MainViewModel getViewModel() {
        return null;
    }
    
    private final com.example.castapp.viewmodel.SenderViewModel getSenderViewModel() {
        return null;
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    /**
     * 初始化各个管理器
     */
    private final void initializeManagers() {
    }
    
    /**
     * 初始化视图
     */
    private final void initViews() {
    }
    
    /**
     * 设置点击监听器
     */
    private final void setupClickListeners() {
    }
    
    /**
     * 观察ViewModel状态变化 - 重构为事件分发
     */
    private final void observeViewModel() {
    }
    
    /**
     * 检查并请求权限 - 使用新的Activity Result API
     */
    private final void checkAndRequestPermissions() {
    }
    
    /**
     * 🔥 新增：APP启动时预请求MediaProjection权限
     * 实现方案一：启动时静默预请求权限，缓存后供后续功能使用
     */
    private final void preRequestMediaProjectionPermission() {
    }
    
    /**
     * 🔥 新增：APP启动时初始化麦克风管理器
     * 预创建麦克风AudioRecord实例，避免后台录音权限问题
     */
    private final void initializeMicrophoneManager() {
    }
    
    @java.lang.Override()
    protected void onDestroy() {
    }
    
    /**
     * 显示发送端设置对话框
     */
    private final void showSenderDialog() {
    }
    
    /**
     * 显示接收端设置对话框
     */
    private final void showReceiverDialog() {
    }
    
    /**
     * 显示遥控管理对话框
     */
    private final void showRemoteControlManagerDialog() {
    }
    
    /**
     * 处理保存布局操作
     */
    private final void handleSaveLayout() {
    }
    
    /**
     * 处理显示导播台操作
     */
    private final void handleShowDirector() {
    }
    
    /**
     * 处理恢复布局操作
     */
    private final void handleRestoreLayout(java.util.List<com.example.castapp.database.entity.WindowLayoutItemEntity> layoutItems) {
    }
    
    /**
     * 显示添加媒体对话框
     */
    private final void showAddMediaDialog() {
    }
    
    /**
     * 获取权限管理器实例
     * 供其他组件使用
     */
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.manager.PermissionManager.ActivityPermissionHelper getActivityPermissionHelper() {
        return null;
    }
    
    /**
     * 初始化精准控制面板管理器
     */
    private final void initializePrecisionControlPanelManager() {
    }
    
    /**
     * 更新精准控制面板（委托给管理器）
     */
    private final void updatePrecisionControlPanels() {
    }
    
    /**
     * 初始化清屏功能管理器
     */
    private final void initializeHideShowManager() {
    }
    
    /**
     * 🔥 关键修复：监听SenderViewModel的权限请求
     * 确保即使发送端设置窗口关闭，也能处理远程控制触发的权限请求
     */
    private final void observeSenderViewModelPermissionRequests() {
    }
    
    /**
     * 🔥 关键修复：为SenderViewModel处理MediaProjection权限请求
     * 这是全局权限处理入口，不依赖于任何Fragment的生命周期
     */
    private final void requestMediaProjectionPermissionForSender(com.example.castapp.model.Connection connection, java.lang.String featureType) {
    }
    
    /**
     * 🔥 关键修复：恢复远程被控服务器状态
     * 在应用启动时检查并恢复远程被控服务器的状态，确保不依赖于UI窗口
     */
    private final void restoreRemoteControlServerState() {
    }
    
    /**
     * 恢复固定端口7777 WebSocket服务器状态
     * 在应用启动时检查用户之前是否开启了远程被控开关，如果是则自动启动7777端口服务器
     */
    private final void restoreFixedWebSocketServerState() {
    }
    
    /**
     * 🎯 在应用启动时自动恢复之前应用的布局
     * 解决重启APP后导播台应用布局时文本窗口参数没有自动恢复的问题
     */
    private final void restoreAppliedLayoutOnStartup() {
    }
    
    /**
     * 🎯 静默恢复布局操作（不显示Toast提示）
     * 用于APP启动时的自动布局恢复
     */
    private final void handleRestoreLayoutSilently(java.util.List<com.example.castapp.database.entity.WindowLayoutItemEntity> layoutItems) {
    }
}