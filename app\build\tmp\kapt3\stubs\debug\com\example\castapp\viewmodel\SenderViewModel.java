package com.example.castapp.viewmodel;

/**
 * 发送端ViewModel
 * 管理发送端连接列表和投屏状态
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0096\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\"\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0011\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0015\n\u0002\u0010\u0000\n\u0002\b\u0016\n\u0002\u0018\u0002\n\u0002\b\b\u0018\u0000 t2\u00020\u0001:\u0003tuvB\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010+\u001a\u00020,2\u0006\u0010-\u001a\u00020\u000b2\u0006\u0010.\u001a\u00020\u001bJ\u0010\u0010/\u001a\u00020,2\u0006\u00100\u001a\u00020\nH\u0002J\b\u00101\u001a\u00020,H\u0002J\u0010\u00102\u001a\u00020,2\u0006\u00103\u001a\u00020\u000bH\u0002J\u000e\u00104\u001a\u00020,2\u0006\u00100\u001a\u00020\nJ\b\u00105\u001a\u00020,H\u0002J\u0010\u00106\u001a\u00020,2\u0006\u00100\u001a\u00020\nH\u0002J\u0010\u00107\u001a\u00020,2\u0006\u00100\u001a\u00020\nH\u0002J\u001e\u00108\u001a\u00020\u000f2\u0006\u00100\u001a\u00020\n2\u0006\u00109\u001a\u00020\u000bH\u0082@\u00a2\u0006\u0002\u0010:J8\u0010;\u001a\u00020,2\u0006\u0010<\u001a\u00020\u000b2\n\u0010=\u001a\u00060>j\u0002`?2\b\b\u0002\u0010@\u001a\u00020\u000f2\u0010\b\u0002\u0010A\u001a\n\u0012\u0004\u0012\u00020,\u0018\u00010BH\u0002J*\u0010C\u001a\u00020,2\u0006\u0010D\u001a\u00020\u000b2\u0006\u0010-\u001a\u00020\u000b2\u0006\u0010.\u001a\u00020\u001b2\b\u0010E\u001a\u0004\u0018\u00010\u000bH\u0002J\"\u0010F\u001a\u00020,2\u0006\u00109\u001a\u00020\u000b2\u0006\u0010G\u001a\u00020\u000f2\b\u0010E\u001a\u0004\u0018\u00010\u000bH\u0002J \u0010H\u001a\u00020,2\u0006\u0010I\u001a\u00020\u001b2\u0006\u0010J\u001a\u00020\u000f2\b\u0010K\u001a\u0004\u0018\u00010\u000bJ6\u0010L\u001a\u00020,2\b\u00100\u001a\u0004\u0018\u00010\n2\u0006\u0010M\u001a\u00020\u000b2\u0006\u0010N\u001a\u00020\u000f2\b\b\u0002\u0010O\u001a\u00020\u001b2\n\b\u0002\u0010P\u001a\u0004\u0018\u00010\u001cJ\u0006\u0010Q\u001a\u00020,J\b\u0010R\u001a\u00020\u000fH\u0002J\b\u0010S\u001a\u00020,H\u0014J\u000e\u0010T\u001a\u00020,2\u0006\u00100\u001a\u00020\nJ\u0018\u0010U\u001a\u00020,2\u0006\u0010V\u001a\u00020\u000b2\u0006\u0010W\u001a\u00020XH\u0002J\b\u0010Y\u001a\u00020,H\u0002J\u0006\u0010#\u001a\u00020,J\u0010\u0010Z\u001a\u00020,2\u0006\u00100\u001a\u00020\nH\u0002J\u0016\u0010[\u001a\u00020,2\u0006\u00100\u001a\u00020\nH\u0082@\u00a2\u0006\u0002\u0010\\J \u0010]\u001a\u00020,2\u0006\u00100\u001a\u00020\n2\u0006\u0010O\u001a\u00020\u001b2\u0006\u0010P\u001a\u00020\u001cH\u0002J\u0010\u0010^\u001a\u00020,2\u0006\u00100\u001a\u00020\nH\u0002J\u0010\u0010_\u001a\u00020,2\u0006\u0010`\u001a\u00020\u001cH\u0002J\u0018\u0010a\u001a\u00020,2\u0006\u00103\u001a\u00020\u000b2\u0006\u0010D\u001a\u00020\u000bH\u0002J\u0010\u0010b\u001a\u00020,2\u0006\u00100\u001a\u00020\nH\u0002J\u0016\u0010c\u001a\u00020,2\u0006\u00100\u001a\u00020\n2\u0006\u0010d\u001a\u00020\u000fJ\u0016\u0010e\u001a\u00020,2\u0006\u00100\u001a\u00020\n2\u0006\u0010f\u001a\u00020\u000fJ\u0016\u0010g\u001a\u00020,2\u0006\u00100\u001a\u00020\n2\u0006\u0010f\u001a\u00020\u000fJ \u0010h\u001a\u00020,2\u0006\u0010i\u001a\u00020\u001b2\u0006\u0010D\u001a\u00020\u000b2\u0006\u0010j\u001a\u00020\u000bH\u0002J\u000e\u0010k\u001a\u00020,2\u0006\u0010l\u001a\u00020\u001bJ$\u0010m\u001a\u00020,2\u0006\u00103\u001a\u00020\u000b2\u0012\u0010n\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\n0oH\u0002J\u000e\u0010p\u001a\u00020,2\u0006\u0010i\u001a\u00020\u001bJ\u000e\u0010q\u001a\u00020,2\u0006\u0010i\u001a\u00020\u001bJ\u0018\u0010r\u001a\u00020,2\u0006\u0010I\u001a\u00020\u001b2\b\b\u0002\u0010s\u001a\u00020\u001bR\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\"\u0010\b\u001a\u0016\u0012\u0012\u0012\u0010\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u000b\u0018\u00010\t0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\r0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0011\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\u00130\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u001d\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\u00170\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0015R&\u0010\u0019\u001a\u001a\u0012\u0004\u0012\u00020\u000b\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u001b\u0012\u0004\u0012\u00020\u001c0\t0\u001aX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00070\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0015R%\u0010\u001f\u001a\u0016\u0012\u0012\u0012\u0010\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u000b\u0018\u00010\t0\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u0015R\u0017\u0010!\u001a\b\u0012\u0004\u0012\u00020\r0\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u0015R\u0017\u0010#\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010\u0015R\u000e\u0010%\u001a\u00020&X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\'\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010\u0015R\u000e\u0010)\u001a\u00020*X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006w"}, d2 = {"Lcom/example/castapp/viewmodel/SenderViewModel;", "Landroidx/lifecycle/AndroidViewModel;", "application", "Landroid/app/Application;", "(Landroid/app/Application;)V", "_remoteControlUIUpdate", "Landroidx/lifecycle/MutableLiveData;", "Lcom/example/castapp/viewmodel/SenderViewModel$RemoteControlUIUpdate;", "_requestMediaProjection", "Lkotlin/Pair;", "Lcom/example/castapp/model/Connection;", "", "_resolutionAdjustmentState", "Lcom/example/castapp/viewmodel/SenderViewModel$ResolutionAdjustmentState;", "_showAddReceiverDialog", "", "_toastMessage", "castingConnections", "Landroidx/lifecycle/LiveData;", "", "getCastingConnections", "()Landroidx/lifecycle/LiveData;", "connections", "", "getConnections", "pendingPermissionData", "Ljava/util/concurrent/ConcurrentHashMap;", "", "Landroid/content/Intent;", "remoteControlUIUpdate", "getRemoteControlUIUpdate", "requestMediaProjection", "getRequestMediaProjection", "resolutionAdjustmentState", "getResolutionAdjustmentState", "showAddReceiverDialog", "getShowAddReceiverDialog", "stateManager", "Lcom/example/castapp/manager/StateManager;", "toastMessage", "getToastMessage", "webSocketManager", "Lcom/example/castapp/manager/WebSocketManager;", "addConnection", "", "ipAddress", "port", "broadcastConnectionAdded", "connection", "broadcastConnectionListUpdate", "broadcastConnectionRemoved", "connectionId", "broadcastConnectionUpdated", "clearRemoteControlCallbacks", "continueMediaAudioEnable", "continueMicAudioEnable", "establishWebSocketConnectionFirst", "functionType", "(Lcom/example/castapp/model/Connection;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "handleOperationError", "message", "exception", "Ljava/lang/Exception;", "Lkotlin/Exception;", "shouldResetState", "resetAction", "Lkotlin/Function0;", "handleRemoteConnectionManagementRequest", "action", "targetConnectionId", "handleRemoteConnectionToggle", "enabled", "handleResolutionAdjustmentComplete", "scalePercent", "success", "error", "handleUnifiedMediaProjectionResult", "featureType", "granted", "resultCode", "resultData", "hideAddReceiverDialog", "needsMediaAudioPermission", "onCleared", "removeConnection", "saveSettingToPreferences", "key", "value", "", "setupRemoteControlCallbacks", "startCasting", "startCastingServiceAfterPermission", "(Lcom/example/castapp/model/Connection;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "startMediaAudioAfterPermission", "startMediaAudioServiceDirectly", "startServiceWithIntent", "intent", "stopAudioService", "stopCasting", "toggleCasting", "shouldCast", "toggleMediaAudio", "shouldEnable", "toggleMicAudio", "updateAudioVolume", "volume", "audioType", "updateBitRate", "bitrateMbps", "updateConnectionState", "update", "Lkotlin/Function1;", "updateMediaAudioVolume", "updateMicAudioVolume", "updateResolutionScale", "retryCount", "Companion", "RemoteControlUIUpdate", "ResolutionAdjustmentState", "app_debug"})
public final class SenderViewModel extends androidx.lifecycle.AndroidViewModel {
    @org.jetbrains.annotations.Nullable()
    private static kotlin.jvm.functions.Function3<? super java.lang.Integer, ? super java.lang.Boolean, ? super java.lang.String, kotlin.Unit> resolutionAdjustmentCallback;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.StateManager stateManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.WebSocketManager webSocketManager = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.util.List<com.example.castapp.model.Connection>> connections = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.util.Set<java.lang.String>> castingConnections = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.Boolean> _showAddReceiverDialog = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.Boolean> showAddReceiverDialog = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.String> _toastMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.String> toastMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<kotlin.Pair<com.example.castapp.model.Connection, java.lang.String>> _requestMediaProjection = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<kotlin.Pair<com.example.castapp.model.Connection, java.lang.String>> requestMediaProjection = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.example.castapp.viewmodel.SenderViewModel.ResolutionAdjustmentState> _resolutionAdjustmentState = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.example.castapp.viewmodel.SenderViewModel.ResolutionAdjustmentState> resolutionAdjustmentState = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.example.castapp.viewmodel.SenderViewModel.RemoteControlUIUpdate> _remoteControlUIUpdate = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.example.castapp.viewmodel.SenderViewModel.RemoteControlUIUpdate> remoteControlUIUpdate = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, kotlin.Pair<java.lang.Integer, android.content.Intent>> pendingPermissionData = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.viewmodel.SenderViewModel.Companion Companion = null;
    
    public SenderViewModel(@org.jetbrains.annotations.NotNull()
    android.app.Application application) {
        super(null);
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.List<com.example.castapp.model.Connection>> getConnections() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.Set<java.lang.String>> getCastingConnections() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.Boolean> getShowAddReceiverDialog() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.String> getToastMessage() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<kotlin.Pair<com.example.castapp.model.Connection, java.lang.String>> getRequestMediaProjection() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.example.castapp.viewmodel.SenderViewModel.ResolutionAdjustmentState> getResolutionAdjustmentState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.example.castapp.viewmodel.SenderViewModel.RemoteControlUIUpdate> getRemoteControlUIUpdate() {
        return null;
    }
    
    /**
     * 显示添加接收端对话框
     */
    public final void showAddReceiverDialog() {
    }
    
    /**
     * 隐藏添加接收端对话框
     */
    public final void hideAddReceiverDialog() {
    }
    
    /**
     * 添加新连接
     */
    public final void addConnection(@org.jetbrains.annotations.NotNull()
    java.lang.String ipAddress, int port) {
    }
    
    /**
     * 移除连接
     */
    public final void removeConnection(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.Connection connection) {
    }
    
    /**
     * 切换投屏状态
     */
    public final void toggleCasting(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.Connection connection, boolean shouldCast) {
    }
    
    /**
     * 开始投屏 - WebSocket连接已建立，现在检查权限并启动服务
     */
    private final void startCasting(com.example.castapp.model.Connection connection) {
    }
    
    /**
     * 优先建立WebSocket连接
     */
    private final java.lang.Object establishWebSocketConnectionFirst(com.example.castapp.model.Connection connection, java.lang.String functionType, kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * 停止投屏
     */
    private final void stopCasting(com.example.castapp.model.Connection connection) {
    }
    
    /**
     * 处理统一MediaProjection权限结果
     */
    public final void handleUnifiedMediaProjectionResult(@org.jetbrains.annotations.Nullable()
    com.example.castapp.model.Connection connection, @org.jetbrains.annotations.NotNull()
    java.lang.String featureType, boolean granted, int resultCode, @org.jetbrains.annotations.Nullable()
    android.content.Intent resultData) {
    }
    
    /**
     * 权限授权后启动投屏服务
     */
    private final java.lang.Object startCastingServiceAfterPermission(com.example.castapp.model.Connection connection, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 切换媒体音频状态
     */
    public final void toggleMediaAudio(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.Connection connection, boolean shouldEnable) {
    }
    
    /**
     * 继续媒体音频启用流程（WebSocket连接已确认）
     */
    private final void continueMediaAudioEnable(com.example.castapp.model.Connection connection) {
    }
    
    /**
     * 检查是否需要为媒体音频申请MediaProjection权限
     */
    private final boolean needsMediaAudioPermission() {
        return false;
    }
    
    /**
     * 媒体音频权限授权后的处理
     */
    private final void startMediaAudioAfterPermission(com.example.castapp.model.Connection connection, int resultCode, android.content.Intent resultData) {
    }
    
    /**
     * 直接启动媒体音频服务（WebSocket连接已建立，权限已授权）
     */
    private final void startMediaAudioServiceDirectly(com.example.castapp.model.Connection connection) {
    }
    
    /**
     * 切换麦克风音频状态
     */
    public final void toggleMicAudio(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.Connection connection, boolean shouldEnable) {
    }
    
    /**
     * 继续麦克风音频启用流程（WebSocket连接已确认）
     */
    private final void continueMicAudioEnable(com.example.castapp.model.Connection connection) {
    }
    
    /**
     * 更新码率
     */
    public final void updateBitRate(int bitrateMbps) {
    }
    
    /**
     * 更新分辨率缩放比例（带重试机制）
     */
    public final void updateResolutionScale(int scalePercent, int retryCount) {
    }
    
    /**
     * 处理接收端发送的分辨率调整完成通知
     */
    public final void handleResolutionAdjustmentComplete(int scalePercent, boolean success, @org.jetbrains.annotations.Nullable()
    java.lang.String error) {
    }
    
    /**
     * 更新媒体音频音量
     */
    public final void updateMediaAudioVolume(int volume) {
    }
    
    /**
     * 更新麦克风音频音量
     */
    public final void updateMicAudioVolume(int volume) {
    }
    
    /**
     * 更新音频音量的公共方法
     */
    private final void updateAudioVolume(int volume, java.lang.String action, java.lang.String audioType) {
    }
    
    /**
     * 停止音频服务的公共方法
     */
    private final void stopAudioService(java.lang.String connectionId, java.lang.String action) {
    }
    
    /**
     * 启动服务的公共方法
     */
    private final void startServiceWithIntent(android.content.Intent intent) {
    }
    
    /**
     * 更新连接状态的公共方法
     */
    private final void updateConnectionState(java.lang.String connectionId, kotlin.jvm.functions.Function1<? super com.example.castapp.model.Connection, com.example.castapp.model.Connection> update) {
    }
    
    /**
     * 处理操作错误的公共方法
     */
    private final void handleOperationError(java.lang.String message, java.lang.Exception exception, boolean shouldResetState, kotlin.jvm.functions.Function0<kotlin.Unit> resetAction) {
    }
    
    /**
     * 设置远程控制服务器回调
     * 确保即使UI窗口关闭，远程控制仍能正常工作
     */
    private final void setupRemoteControlCallbacks() {
    }
    
    /**
     * 处理远程连接切换请求
     */
    private final void handleRemoteConnectionToggle(java.lang.String functionType, boolean enabled, java.lang.String targetConnectionId) {
    }
    
    /**
     * 广播连接列表状态更新到所有远程控制端
     */
    private final void broadcastConnectionListUpdate() {
    }
    
    /**
     * 广播连接添加事件到所有远程控制端
     */
    private final void broadcastConnectionAdded(com.example.castapp.model.Connection connection) {
    }
    
    /**
     * 广播连接更新事件到所有远程控制端
     */
    public final void broadcastConnectionUpdated(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.Connection connection) {
    }
    
    /**
     * 广播连接删除事件到所有远程控制端
     */
    private final void broadcastConnectionRemoved(java.lang.String connectionId) {
    }
    
    /**
     * 处理远程连接管理请求
     */
    private final void handleRemoteConnectionManagementRequest(java.lang.String action, java.lang.String ipAddress, int port, java.lang.String targetConnectionId) {
    }
    
    /**
     * 保存设置到SharedPreferences
     * 🔥 关键修复：确保远程控制状态变更能正确保存，即使UI窗口关闭
     */
    private final void saveSettingToPreferences(java.lang.String key, java.lang.Object value) {
    }
    
    /**
     * 清理远程控制回调
     */
    private final void clearRemoteControlCallbacks() {
    }
    
    @java.lang.Override()
    protected void onCleared() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0002\u0010\u000b\n\u0002\u0010\u000e\n\u0002\u0010\u0002\n\u0002\b\b\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J \u0010\t\u001a\u00020\b2\u0006\u0010\n\u001a\u00020\u00052\u0006\u0010\u000b\u001a\u00020\u00062\b\u0010\f\u001a\u0004\u0018\u00010\u0007J(\u0010\r\u001a\u00020\b2 \u0010\u000e\u001a\u001c\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u0006\u0012\u0006\u0012\u0004\u0018\u00010\u0007\u0012\u0004\u0012\u00020\b0\u0004J\u0006\u0010\u000f\u001a\u00020\bR*\u0010\u0003\u001a\u001e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u0006\u0012\u0006\u0012\u0004\u0018\u00010\u0007\u0012\u0004\u0012\u00020\b\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0010"}, d2 = {"Lcom/example/castapp/viewmodel/SenderViewModel$Companion;", "", "()V", "resolutionAdjustmentCallback", "Lkotlin/Function3;", "", "", "", "", "notifyResolutionAdjustmentComplete", "scalePercent", "success", "error", "registerResolutionAdjustmentCallback", "callback", "unregisterResolutionAdjustmentCallback", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * 注册分辨率调整完成回调
         */
        public final void registerResolutionAdjustmentCallback(@org.jetbrains.annotations.NotNull()
        kotlin.jvm.functions.Function3<? super java.lang.Integer, ? super java.lang.Boolean, ? super java.lang.String, kotlin.Unit> callback) {
        }
        
        /**
         * 取消注册分辨率调整完成回调
         */
        public final void unregisterResolutionAdjustmentCallback() {
        }
        
        /**
         * 通知分辨率调整完成（供CastingService调用）
         */
        public final void notifyResolutionAdjustmentComplete(int scalePercent, boolean success, @org.jetbrains.annotations.Nullable()
        java.lang.String error) {
        }
    }
    
    /**
     * 远程控制UI更新密封类
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b6\u0018\u00002\u00020\u0001:\u0004\u0003\u0004\u0005\u0006B\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0002\u0082\u0001\u0004\u0007\b\t\n\u00a8\u0006\u000b"}, d2 = {"Lcom/example/castapp/viewmodel/SenderViewModel$RemoteControlUIUpdate;", "", "()V", "BitrateUpdate", "ConnectionToggle", "ResolutionUpdate", "VolumeUpdate", "Lcom/example/castapp/viewmodel/SenderViewModel$RemoteControlUIUpdate$BitrateUpdate;", "Lcom/example/castapp/viewmodel/SenderViewModel$RemoteControlUIUpdate$ConnectionToggle;", "Lcom/example/castapp/viewmodel/SenderViewModel$RemoteControlUIUpdate$ResolutionUpdate;", "Lcom/example/castapp/viewmodel/SenderViewModel$RemoteControlUIUpdate$VolumeUpdate;", "app_debug"})
    public static abstract class RemoteControlUIUpdate {
        
        private RemoteControlUIUpdate() {
            super();
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\u000e\u001a\u00020\u000fH\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/example/castapp/viewmodel/SenderViewModel$RemoteControlUIUpdate$BitrateUpdate;", "Lcom/example/castapp/viewmodel/SenderViewModel$RemoteControlUIUpdate;", "bitrateMbps", "", "(I)V", "getBitrateMbps", "()I", "component1", "copy", "equals", "", "other", "", "hashCode", "toString", "", "app_debug"})
        public static final class BitrateUpdate extends com.example.castapp.viewmodel.SenderViewModel.RemoteControlUIUpdate {
            private final int bitrateMbps = 0;
            
            public BitrateUpdate(int bitrateMbps) {
            }
            
            public final int getBitrateMbps() {
                return 0;
            }
            
            public final int component1() {
                return 0;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.example.castapp.viewmodel.SenderViewModel.RemoteControlUIUpdate.BitrateUpdate copy(int bitrateMbps) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\n\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\t\u0010\u000b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\f\u001a\u00020\u0005H\u00c6\u0003J\u001d\u0010\r\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u000e\u001a\u00020\u00052\b\u0010\u000f\u001a\u0004\u0018\u00010\u0010H\u00d6\u0003J\t\u0010\u0011\u001a\u00020\u0012H\u00d6\u0001J\t\u0010\u0013\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\n\u00a8\u0006\u0014"}, d2 = {"Lcom/example/castapp/viewmodel/SenderViewModel$RemoteControlUIUpdate$ConnectionToggle;", "Lcom/example/castapp/viewmodel/SenderViewModel$RemoteControlUIUpdate;", "functionType", "", "enabled", "", "(Ljava/lang/String;Z)V", "getEnabled", "()Z", "getFunctionType", "()Ljava/lang/String;", "component1", "component2", "copy", "equals", "other", "", "hashCode", "", "toString", "app_debug"})
        public static final class ConnectionToggle extends com.example.castapp.viewmodel.SenderViewModel.RemoteControlUIUpdate {
            @org.jetbrains.annotations.NotNull()
            private final java.lang.String functionType = null;
            private final boolean enabled = false;
            
            public ConnectionToggle(@org.jetbrains.annotations.NotNull()
            java.lang.String functionType, boolean enabled) {
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String getFunctionType() {
                return null;
            }
            
            public final boolean getEnabled() {
                return false;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String component1() {
                return null;
            }
            
            public final boolean component2() {
                return false;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.example.castapp.viewmodel.SenderViewModel.RemoteControlUIUpdate.ConnectionToggle copy(@org.jetbrains.annotations.NotNull()
            java.lang.String functionType, boolean enabled) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\u000e\u001a\u00020\u000fH\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/example/castapp/viewmodel/SenderViewModel$RemoteControlUIUpdate$ResolutionUpdate;", "Lcom/example/castapp/viewmodel/SenderViewModel$RemoteControlUIUpdate;", "scalePercent", "", "(I)V", "getScalePercent", "()I", "component1", "copy", "equals", "", "other", "", "hashCode", "toString", "", "app_debug"})
        public static final class ResolutionUpdate extends com.example.castapp.viewmodel.SenderViewModel.RemoteControlUIUpdate {
            private final int scalePercent = 0;
            
            public ResolutionUpdate(int scalePercent) {
            }
            
            public final int getScalePercent() {
                return 0;
            }
            
            public final int component1() {
                return 0;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.example.castapp.viewmodel.SenderViewModel.RemoteControlUIUpdate.ResolutionUpdate copy(int scalePercent) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\t\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0003\b\u0086\b\u0018\u00002\u00020\u0001B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\t\u0010\u000b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\f\u001a\u00020\u0005H\u00c6\u0003J\u001d\u0010\r\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u000e\u001a\u00020\u000f2\b\u0010\u0010\u001a\u0004\u0018\u00010\u0011H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0005H\u00d6\u0001J\t\u0010\u0013\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\n\u00a8\u0006\u0014"}, d2 = {"Lcom/example/castapp/viewmodel/SenderViewModel$RemoteControlUIUpdate$VolumeUpdate;", "Lcom/example/castapp/viewmodel/SenderViewModel$RemoteControlUIUpdate;", "volumeType", "", "volume", "", "(Ljava/lang/String;I)V", "getVolume", "()I", "getVolumeType", "()Ljava/lang/String;", "component1", "component2", "copy", "equals", "", "other", "", "hashCode", "toString", "app_debug"})
        public static final class VolumeUpdate extends com.example.castapp.viewmodel.SenderViewModel.RemoteControlUIUpdate {
            @org.jetbrains.annotations.NotNull()
            private final java.lang.String volumeType = null;
            private final int volume = 0;
            
            public VolumeUpdate(@org.jetbrains.annotations.NotNull()
            java.lang.String volumeType, int volume) {
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String getVolumeType() {
                return null;
            }
            
            public final int getVolume() {
                return 0;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String component1() {
                return null;
            }
            
            public final int component2() {
                return 0;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.example.castapp.viewmodel.SenderViewModel.RemoteControlUIUpdate.VolumeUpdate copy(@org.jetbrains.annotations.NotNull()
            java.lang.String volumeType, int volume) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
    }
    
    /**
     * 分辨率调整状态密封类
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b6\u0018\u00002\u00020\u0001:\u0003\u0003\u0004\u0005B\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0002\u0082\u0001\u0003\u0006\u0007\b\u00a8\u0006\t"}, d2 = {"Lcom/example/castapp/viewmodel/SenderViewModel$ResolutionAdjustmentState;", "", "()V", "Failed", "InProgress", "Success", "Lcom/example/castapp/viewmodel/SenderViewModel$ResolutionAdjustmentState$Failed;", "Lcom/example/castapp/viewmodel/SenderViewModel$ResolutionAdjustmentState$InProgress;", "Lcom/example/castapp/viewmodel/SenderViewModel$ResolutionAdjustmentState$Success;", "app_debug"})
    public static abstract class ResolutionAdjustmentState {
        
        private ResolutionAdjustmentState() {
            super();
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\f\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0003\b\u0086\b\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0007J\t\u0010\r\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000e\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u000f\u001a\u00020\u0003H\u00c6\u0003J\'\u0010\u0010\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u0011\u001a\u00020\u00122\b\u0010\u0013\u001a\u0004\u0018\u00010\u0014H\u00d6\u0003J\t\u0010\u0015\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\u0016\u001a\u00020\u0005H\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u000b\u00a8\u0006\u0017"}, d2 = {"Lcom/example/castapp/viewmodel/SenderViewModel$ResolutionAdjustmentState$Failed;", "Lcom/example/castapp/viewmodel/SenderViewModel$ResolutionAdjustmentState;", "scalePercent", "", "error", "", "retryCount", "(ILjava/lang/String;I)V", "getError", "()Ljava/lang/String;", "getRetryCount", "()I", "getScalePercent", "component1", "component2", "component3", "copy", "equals", "", "other", "", "hashCode", "toString", "app_debug"})
        public static final class Failed extends com.example.castapp.viewmodel.SenderViewModel.ResolutionAdjustmentState {
            private final int scalePercent = 0;
            @org.jetbrains.annotations.NotNull()
            private final java.lang.String error = null;
            private final int retryCount = 0;
            
            public Failed(int scalePercent, @org.jetbrains.annotations.NotNull()
            java.lang.String error, int retryCount) {
            }
            
            public final int getScalePercent() {
                return 0;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String getError() {
                return null;
            }
            
            public final int getRetryCount() {
                return 0;
            }
            
            public final int component1() {
                return 0;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String component2() {
                return null;
            }
            
            public final int component3() {
                return 0;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.example.castapp.viewmodel.SenderViewModel.ResolutionAdjustmentState.Failed copy(int scalePercent, @org.jetbrains.annotations.NotNull()
            java.lang.String error, int retryCount) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\u000e\u001a\u00020\u000fH\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/example/castapp/viewmodel/SenderViewModel$ResolutionAdjustmentState$InProgress;", "Lcom/example/castapp/viewmodel/SenderViewModel$ResolutionAdjustmentState;", "scalePercent", "", "(I)V", "getScalePercent", "()I", "component1", "copy", "equals", "", "other", "", "hashCode", "toString", "", "app_debug"})
        public static final class InProgress extends com.example.castapp.viewmodel.SenderViewModel.ResolutionAdjustmentState {
            private final int scalePercent = 0;
            
            public InProgress(int scalePercent) {
            }
            
            public final int getScalePercent() {
                return 0;
            }
            
            public final int component1() {
                return 0;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.example.castapp.viewmodel.SenderViewModel.ResolutionAdjustmentState.InProgress copy(int scalePercent) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\u000e\u001a\u00020\u000fH\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/example/castapp/viewmodel/SenderViewModel$ResolutionAdjustmentState$Success;", "Lcom/example/castapp/viewmodel/SenderViewModel$ResolutionAdjustmentState;", "scalePercent", "", "(I)V", "getScalePercent", "()I", "component1", "copy", "equals", "", "other", "", "hashCode", "toString", "", "app_debug"})
        public static final class Success extends com.example.castapp.viewmodel.SenderViewModel.ResolutionAdjustmentState {
            private final int scalePercent = 0;
            
            public Success(int scalePercent) {
            }
            
            public final int getScalePercent() {
                return 0;
            }
            
            public final int component1() {
                return 0;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.example.castapp.viewmodel.SenderViewModel.ResolutionAdjustmentState.Success copy(int scalePercent) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
    }
}