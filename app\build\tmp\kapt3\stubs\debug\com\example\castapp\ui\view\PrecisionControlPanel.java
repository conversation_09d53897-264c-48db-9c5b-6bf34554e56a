package com.example.castapp.ui.view;

/**
 * 精准控制面板
 * 提供坐标、缩放、旋转的精确数值控制
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u000e\n\u0002\u0010\u000e\n\u0002\b\u0006\u0018\u00002\u00020\u0001:\u00018B%\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\b\u0010#\u001a\u00020$H\u0002J\u000e\u0010%\u001a\u00020$2\u0006\u0010&\u001a\u00020\nJ\b\u0010\'\u001a\u00020$H\u0002J\b\u0010(\u001a\u00020$H\u0002J\u0010\u0010)\u001a\u00020$2\b\u0010*\u001a\u0004\u0018\u00010 J\b\u0010+\u001a\u00020$H\u0002J\b\u0010,\u001a\u00020$H\u0002J\u0006\u0010-\u001a\u00020$J\u000e\u0010.\u001a\u00020$2\u0006\u0010&\u001a\u00020\nJ\b\u0010/\u001a\u00020$H\u0002J\b\u00100\u001a\u00020$H\u0002J.\u00101\u001a\u00020$2\u0006\u00102\u001a\u0002032\u0006\u00104\u001a\u00020\u00172\u0006\u00105\u001a\u00020\u00172\u0006\u00106\u001a\u00020\u00172\u0006\u00107\u001a\u00020\u0017R\u0010\u0010\t\u001a\u0004\u0018\u00010\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0017X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0017X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\u0017X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\u0017X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001e\u001a\u00020\u0001X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001f\u001a\u0004\u0018\u00010 X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010!\u001a\u00020\"X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u00069"}, d2 = {"Lcom/example/castapp/ui/view/PrecisionControlPanel;", "Landroid/widget/LinearLayout;", "context", "Landroid/content/Context;", "attrs", "Landroid/util/AttributeSet;", "defStyleAttr", "", "(Landroid/content/Context;Landroid/util/AttributeSet;I)V", "boundWindow", "Lcom/example/castapp/model/CastWindowInfo;", "btnApplyTransform", "Landroid/widget/Button;", "btnClosePanel", "Landroid/widget/ImageView;", "btnResetTransform", "dragHandle", "etPositionX", "Landroid/widget/EditText;", "etPositionY", "etRotationAngle", "etScaleFactor", "initialX", "", "initialY", "isDragging", "", "isUpdatingFromCode", "lastTouchX", "lastTouchY", "titleBar", "transformChangeListener", "Lcom/example/castapp/ui/view/PrecisionControlPanel$OnTransformChangeListener;", "tvPanelTitle", "Landroid/widget/TextView;", "applyTransform", "", "bindWindow", "windowInfo", "initView", "resetTransform", "setOnTransformChangeListener", "listener", "setupDragListeners", "setupListeners", "showPanel", "updateBoundWindow", "updateControlValues", "updatePanelTitle", "updateTransformValues", "connectionId", "", "x", "y", "scale", "rotation", "OnTransformChangeListener", "app_debug"})
public final class PrecisionControlPanel extends android.widget.LinearLayout {
    private android.widget.LinearLayout titleBar;
    private android.widget.ImageView dragHandle;
    private android.widget.TextView tvPanelTitle;
    private android.widget.ImageView btnClosePanel;
    private android.widget.EditText etPositionX;
    private android.widget.EditText etPositionY;
    private android.widget.EditText etScaleFactor;
    private android.widget.EditText etRotationAngle;
    private android.widget.Button btnApplyTransform;
    private android.widget.Button btnResetTransform;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.model.CastWindowInfo boundWindow;
    private boolean isUpdatingFromCode = false;
    private boolean isDragging = false;
    private float lastTouchX = 0.0F;
    private float lastTouchY = 0.0F;
    private float initialX = 0.0F;
    private float initialY = 0.0F;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.view.PrecisionControlPanel.OnTransformChangeListener transformChangeListener;
    
    @kotlin.jvm.JvmOverloads()
    public PrecisionControlPanel(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    android.util.AttributeSet attrs, int defStyleAttr) {
        super(null);
    }
    
    private final void initView() {
    }
    
    private final void setupListeners() {
    }
    
    /**
     * 绑定窗口信息
     */
    public final void bindWindow(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.CastWindowInfo windowInfo) {
    }
    
    /**
     * 更新绑定窗口的信息
     */
    public final void updateBoundWindow(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.CastWindowInfo windowInfo) {
    }
    
    /**
     * 更新控件数值显示
     */
    private final void updateControlValues() {
    }
    
    /**
     * 更新面板标题显示
     */
    private final void updatePanelTitle() {
    }
    
    /**
     * 应用变换
     */
    private final void applyTransform() {
    }
    
    /**
     * 重置变换
     */
    private final void resetTransform() {
    }
    
    /**
     * 设置变换监听器
     */
    public final void setOnTransformChangeListener(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.view.PrecisionControlPanel.OnTransformChangeListener listener) {
    }
    
    /**
     * 显示面板
     */
    public final void showPanel() {
    }
    
    /**
     * 更新指定窗口的变换数值显示
     */
    public final void updateTransformValues(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, float x, float y, float scale, float rotation) {
    }
    
    /**
     * 设置拖拽监听器
     */
    private final void setupDragListeners() {
    }
    
    @kotlin.jvm.JvmOverloads()
    public PrecisionControlPanel(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super(null);
    }
    
    @kotlin.jvm.JvmOverloads()
    public PrecisionControlPanel(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    android.util.AttributeSet attrs) {
        super(null);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u0007\n\u0002\b\u0004\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\u0010\u0010\u0006\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J0\u0010\u0007\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\t2\u0006\u0010\u000b\u001a\u00020\t2\u0006\u0010\f\u001a\u00020\tH&\u00a8\u0006\r"}, d2 = {"Lcom/example/castapp/ui/view/PrecisionControlPanel$OnTransformChangeListener;", "", "onPanelClosed", "", "connectionId", "", "onResetTransform", "onTransformChanged", "x", "", "y", "scale", "rotation", "app_debug"})
    public static abstract interface OnTransformChangeListener {
        
        public abstract void onTransformChanged(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, float x, float y, float scale, float rotation);
        
        public abstract void onResetTransform(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId);
        
        public abstract void onPanelClosed(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId);
    }
}