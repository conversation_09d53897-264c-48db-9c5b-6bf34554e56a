<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:background="@drawable/floating_stopwatch_background"
    android:padding="30dp"
    android:gravity="center_vertical">

    <TextView
        android:id="@+id/tv_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="00:00:00.000"
        android:textColor="#FFFFFF"
        android:textSize="25sp"
        android:fontFamily="monospace"
        android:layout_marginEnd="8dp"
        android:shadowColor="#000000"
        android:shadowDx="1"
        android:shadowDy="1"
        android:shadowRadius="2"
        android:textStyle="bold" />

    <ImageButton
        android:id="@+id/btn_close"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:src="@drawable/ic_close"
        android:background="@android:color/transparent"
        android:padding="4dp"
        android:contentDescription="@string/close" />

</LinearLayout>
