package com.example.castapp.ui.helper;

/**
 * 布局列表拖拽排序的ItemTouchHelper回调类
 * 实现长按拖拽排序功能
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000V\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u000b\u0018\u00002\u00020\u0001:\u0001(B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J0\u0010\n\u001a\u0004\u0018\u00010\u000b2\u0006\u0010\f\u001a\u00020\u000b2\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000b0\u000e2\u0006\u0010\u000f\u001a\u00020\u00062\u0006\u0010\u0010\u001a\u00020\u0006H\u0016J\u0018\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u000bH\u0016J\u0010\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0015\u001a\u00020\u000bH\u0016J\u0018\u0010\u0018\u001a\u00020\u00062\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u000bH\u0016J\b\u0010\u0019\u001a\u00020\u001aH\u0016J\b\u0010\u001b\u001a\u00020\u001aH\u0016J@\u0010\u001c\u001a\u00020\u00122\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u000b2\u0006\u0010\u001f\u001a\u00020\u00172\u0006\u0010 \u001a\u00020\u00172\u0006\u0010!\u001a\u00020\u00062\u0006\u0010\"\u001a\u00020\u001aH\u0016J \u0010#\u001a\u00020\u001a2\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u000b2\u0006\u0010$\u001a\u00020\u000bH\u0016J\u001a\u0010%\u001a\u00020\u00122\b\u0010\u0015\u001a\u0004\u0018\u00010\u000b2\u0006\u0010!\u001a\u00020\u0006H\u0016J\u0018\u0010&\u001a\u00020\u00122\u0006\u0010\u0015\u001a\u00020\u000b2\u0006\u0010\'\u001a\u00020\u0006H\u0016R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006)"}, d2 = {"Lcom/example/castapp/ui/helper/LayoutItemTouchHelperCallback;", "Landroidx/recyclerview/widget/ItemTouchHelper$Callback;", "adapter", "Lcom/example/castapp/ui/helper/LayoutItemTouchHelperCallback$ItemTouchHelperAdapter;", "(Lcom/example/castapp/ui/helper/LayoutItemTouchHelperCallback$ItemTouchHelperAdapter;)V", "dragFromPosition", "", "dragToPosition", "mainHandler", "Landroid/os/Handler;", "chooseDropTarget", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "selected", "dropTargets", "", "curX", "curY", "clearView", "", "recyclerView", "Landroidx/recyclerview/widget/RecyclerView;", "viewHolder", "getMoveThreshold", "", "getMovementFlags", "isItemViewSwipeEnabled", "", "isLongPressDragEnabled", "onChildDraw", "c", "Landroid/graphics/Canvas;", "dX", "dY", "actionState", "isCurrentlyActive", "onMove", "target", "onSelectedChanged", "onSwiped", "direction", "ItemTouchHelperAdapter", "app_debug"})
public final class LayoutItemTouchHelperCallback extends androidx.recyclerview.widget.ItemTouchHelper.Callback {
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.ui.helper.LayoutItemTouchHelperCallback.ItemTouchHelperAdapter adapter = null;
    @org.jetbrains.annotations.NotNull()
    private final android.os.Handler mainHandler = null;
    private int dragFromPosition = -1;
    private int dragToPosition = -1;
    
    public LayoutItemTouchHelperCallback(@org.jetbrains.annotations.NotNull()
    com.example.castapp.ui.helper.LayoutItemTouchHelperCallback.ItemTouchHelperAdapter adapter) {
        super();
    }
    
    /**
     * 设置支持的移动方向
     * 只支持上下拖拽，不支持左右滑动
     */
    @java.lang.Override()
    public int getMovementFlags(@org.jetbrains.annotations.NotNull()
    androidx.recyclerview.widget.RecyclerView recyclerView, @org.jetbrains.annotations.NotNull()
    androidx.recyclerview.widget.RecyclerView.ViewHolder viewHolder) {
        return 0;
    }
    
    /**
     * 当item被拖拽移动时调用
     */
    @java.lang.Override()
    public boolean onMove(@org.jetbrains.annotations.NotNull()
    androidx.recyclerview.widget.RecyclerView recyclerView, @org.jetbrains.annotations.NotNull()
    androidx.recyclerview.widget.RecyclerView.ViewHolder viewHolder, @org.jetbrains.annotations.NotNull()
    androidx.recyclerview.widget.RecyclerView.ViewHolder target) {
        return false;
    }
    
    /**
     * 不支持滑动删除
     */
    @java.lang.Override()
    public void onSwiped(@org.jetbrains.annotations.NotNull()
    androidx.recyclerview.widget.RecyclerView.ViewHolder viewHolder, int direction) {
    }
    
    /**
     * 是否启用长按拖拽
     */
    @java.lang.Override()
    public boolean isLongPressDragEnabled() {
        return false;
    }
    
    /**
     * 是否启用滑动删除
     */
    @java.lang.Override()
    public boolean isItemViewSwipeEnabled() {
        return false;
    }
    
    /**
     * 设置拖拽阈值为50%，实现半程拖拽交换
     */
    @java.lang.Override()
    public float getMoveThreshold(@org.jetbrains.annotations.NotNull()
    androidx.recyclerview.widget.RecyclerView.ViewHolder viewHolder) {
        return 0.0F;
    }
    
    /**
     * 优化拖拽目标选择，实现半程交换效果
     */
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public androidx.recyclerview.widget.RecyclerView.ViewHolder chooseDropTarget(@org.jetbrains.annotations.NotNull()
    androidx.recyclerview.widget.RecyclerView.ViewHolder selected, @org.jetbrains.annotations.NotNull()
    java.util.List<? extends androidx.recyclerview.widget.RecyclerView.ViewHolder> dropTargets, int curX, int curY) {
        return null;
    }
    
    /**
     * 当拖拽状态改变时调用
     */
    @java.lang.Override()
    public void onSelectedChanged(@org.jetbrains.annotations.Nullable()
    androidx.recyclerview.widget.RecyclerView.ViewHolder viewHolder, int actionState) {
    }
    
    /**
     * 当拖拽结束，清除视图状态时调用
     */
    @java.lang.Override()
    public void clearView(@org.jetbrains.annotations.NotNull()
    androidx.recyclerview.widget.RecyclerView recyclerView, @org.jetbrains.annotations.NotNull()
    androidx.recyclerview.widget.RecyclerView.ViewHolder viewHolder) {
    }
    
    /**
     * 自定义拖拽绘制，添加阴影效果
     */
    @java.lang.Override()
    public void onChildDraw(@org.jetbrains.annotations.NotNull()
    android.graphics.Canvas c, @org.jetbrains.annotations.NotNull()
    androidx.recyclerview.widget.RecyclerView recyclerView, @org.jetbrains.annotations.NotNull()
    androidx.recyclerview.widget.RecyclerView.ViewHolder viewHolder, float dX, float dY, int actionState, boolean isCurrentlyActive) {
    }
    
    /**
     * 拖拽排序适配器接口
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\bf\u0018\u00002\u00020\u0001J\u0018\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0005H&J\u0018\u0010\u0007\u001a\u00020\b2\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0005H&\u00a8\u0006\t"}, d2 = {"Lcom/example/castapp/ui/helper/LayoutItemTouchHelperCallback$ItemTouchHelperAdapter;", "", "onItemMove", "", "fromPosition", "", "toPosition", "onItemMoveCompleted", "", "app_debug"})
    public static abstract interface ItemTouchHelperAdapter {
        
        /**
         * 当两个item位置交换时调用
         * @param fromPosition 起始位置
         * @param toPosition 目标位置
         * @return 是否成功移动
         */
        public abstract boolean onItemMove(int fromPosition, int toPosition);
        
        /**
         * 当拖拽完成时调用，用于保存最终的排序结果
         * @param fromPosition 起始位置
         * @param toPosition 最终位置
         */
        public abstract void onItemMoveCompleted(int fromPosition, int toPosition);
    }
}