package com.example.castapp.ui.dialog;

/**
 * 添加媒体对话框
 * 用于选择添加前置摄像头或后置摄像头窗口
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000r\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0011\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0012\u001a\u00020\u0013H\u0002J\b\u0010\u0014\u001a\u00020\u0013H\u0002J\u0018\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u000e2\u0006\u0010\u0018\u001a\u00020\u000eH\u0002J0\u0010\u0019\u001a\u00020\u00162\u0006\u0010\u001a\u001a\u00020\u000e2\u0006\u0010\u001b\u001a\u00020\u000e2\u0006\u0010\u001c\u001a\u00020\u000e2\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u001f\u001a\u00020\u000eH\u0002J\u0018\u0010 \u001a\u00020\u000e2\u0006\u0010\u001c\u001a\u00020\u000e2\u0006\u0010\u001b\u001a\u00020\u000eH\u0002J\u0012\u0010!\u001a\u0004\u0018\u00010\u000e2\u0006\u0010\u001d\u001a\u00020\u001eH\u0002J\b\u0010\"\u001a\u00020\u0016H\u0002J\b\u0010#\u001a\u00020\u0016H\u0002J\b\u0010$\u001a\u00020\u0016H\u0002J\b\u0010%\u001a\u00020\u0016H\u0002J\b\u0010&\u001a\u00020\u0016H\u0002J\u0010\u0010\'\u001a\u00020\u00162\u0006\u0010(\u001a\u00020)H\u0002J\u001c\u0010*\u001a\u00020\u00162\u0012\u0010+\u001a\u000e\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u00130,H\u0002J\u0010\u0010-\u001a\u00020\u00162\u0006\u0010(\u001a\u00020)H\u0002J\u0010\u0010.\u001a\u00020\u00162\u0006\u0010/\u001a\u000200H\u0002J&\u00101\u001a\u0004\u0018\u0001002\u0006\u00102\u001a\u0002032\b\u00104\u001a\u0004\u0018\u0001052\b\u00106\u001a\u0004\u0018\u000107H\u0016J\u001a\u00108\u001a\u00020\u00162\u0006\u0010/\u001a\u0002002\b\u00106\u001a\u0004\u0018\u000107H\u0016J\b\u00109\u001a\u00020\u0016H\u0002J\b\u0010:\u001a\u00020\u0016H\u0002J\u0018\u0010;\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u000e2\u0006\u0010\u0018\u001a\u00020\u000eH\u0002J\u0010\u0010<\u001a\u00020\u00162\u0006\u0010\u001b\u001a\u00020\u000eH\u0002J\b\u0010=\u001a\u00020\u0016H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u001c\u0010\t\u001a\u0010\u0012\f\u0012\n \f*\u0004\u0018\u00010\u000b0\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\r\u001a\u0004\u0018\u00010\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u000f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00100\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u0011\u001a\u0010\u0012\f\u0012\n \f*\u0004\u0018\u00010\u000b0\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006>"}, d2 = {"Lcom/example/castapp/ui/dialog/AddMediaDialogFragment;", "Landroidx/fragment/app/DialogFragment;", "()V", "btnAddFrontCamera", "Landroid/widget/LinearLayout;", "btnAddPicture", "btnAddRearCamera", "btnAddText", "btnAddVideo", "imagePickerLauncher", "Landroidx/activity/result/ActivityResultLauncher;", "Landroid/content/Intent;", "kotlin.jvm.PlatformType", "pendingMediaType", "", "storagePermissionLauncher", "", "videoPickerLauncher", "checkCameraPermission", "", "checkStoragePermission", "createCameraWindow", "", "cameraId", "cameraName", "createMediaWindow", "mediaId", "mediaType", "fileName", "uri", "Landroid/net/Uri;", "contentType", "generateMediaId", "getFileName", "handleAddFrontCamera", "handleAddPicture", "handleAddRearCamera", "handleAddText", "handleAddVideo", "handleImagePickerResult", "result", "Landroidx/activity/result/ActivityResult;", "handleStoragePermissionResult", "permissions", "", "handleVideoPickerResult", "initViews", "view", "Landroid/view/View;", "onCreateView", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "savedInstanceState", "Landroid/os/Bundle;", "onViewCreated", "openImageFilePicker", "openVideoFilePicker", "requestBasicPermissions", "requestStoragePermissions", "setupClickListeners", "app_debug"})
public final class AddMediaDialogFragment extends androidx.fragment.app.DialogFragment {
    private android.widget.LinearLayout btnAddFrontCamera;
    private android.widget.LinearLayout btnAddRearCamera;
    private android.widget.LinearLayout btnAddVideo;
    private android.widget.LinearLayout btnAddPicture;
    private android.widget.LinearLayout btnAddText;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String pendingMediaType;
    @org.jetbrains.annotations.NotNull()
    private final androidx.activity.result.ActivityResultLauncher<android.content.Intent> videoPickerLauncher = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.activity.result.ActivityResultLauncher<android.content.Intent> imagePickerLauncher = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.activity.result.ActivityResultLauncher<java.lang.String[]> storagePermissionLauncher = null;
    
    public AddMediaDialogFragment() {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    /**
     * 初始化视图组件
     */
    private final void initViews(android.view.View view) {
    }
    
    /**
     * 设置点击监听器
     */
    private final void setupClickListeners() {
    }
    
    /**
     * 处理添加前置摄像头
     */
    private final void handleAddFrontCamera() {
    }
    
    /**
     * 处理添加后置摄像头
     */
    private final void handleAddRearCamera() {
    }
    
    /**
     * 处理添加视频
     */
    private final void handleAddVideo() {
    }
    
    /**
     * 处理添加图片
     */
    private final void handleAddPicture() {
    }
    
    /**
     * 处理添加文本
     */
    private final void handleAddText() {
    }
    
    /**
     * 检查摄像头权限
     */
    private final boolean checkCameraPermission() {
        return false;
    }
    
    /**
     * 请求基础权限（包含摄像头权限）
     */
    private final void requestBasicPermissions(java.lang.String cameraId, java.lang.String cameraName) {
    }
    
    /**
     * 创建摄像头窗口
     */
    private final void createCameraWindow(java.lang.String cameraId, java.lang.String cameraName) {
    }
    
    /**
     * 检查存储权限
     */
    private final boolean checkStoragePermission() {
        return false;
    }
    
    /**
     * 请求存储权限
     */
    private final void requestStoragePermissions(java.lang.String mediaType) {
    }
    
    /**
     * 打开视频文件选择器（支持持久化权限）
     */
    private final void openVideoFilePicker() {
    }
    
    /**
     * 打开图片文件选择器（支持持久化权限）
     */
    private final void openImageFilePicker() {
    }
    
    /**
     * 处理视频文件选择结果
     */
    private final void handleVideoPickerResult(androidx.activity.result.ActivityResult result) {
    }
    
    /**
     * 处理图片文件选择结果
     */
    private final void handleImagePickerResult(androidx.activity.result.ActivityResult result) {
    }
    
    /**
     * 获取文件名
     */
    private final java.lang.String getFileName(android.net.Uri uri) {
        return null;
    }
    
    /**
     * 生成媒体窗口ID
     */
    private final java.lang.String generateMediaId(java.lang.String fileName, java.lang.String mediaType) {
        return null;
    }
    
    /**
     * 创建媒体窗口
     */
    private final void createMediaWindow(java.lang.String mediaId, java.lang.String mediaType, java.lang.String fileName, android.net.Uri uri, java.lang.String contentType) {
    }
    
    /**
     * 处理存储权限请求结果
     */
    private final void handleStoragePermissionResult(java.util.Map<java.lang.String, java.lang.Boolean> permissions) {
    }
}