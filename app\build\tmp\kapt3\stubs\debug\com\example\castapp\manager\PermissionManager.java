package com.example.castapp.manager;

/**
 * 统一的权限管理器 - 简化版
 * 以MediaProjectionManager为核心的权限管理
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0007\u0018\u0000 \u000b2\u00020\u0001:\u0006\n\u000b\f\r\u000e\u000fB\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u00042\u0006\u0010\u0005\u001a\u00020\u0006J\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\b2\u0006\u0010\u0005\u001a\u00020\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/example/castapp/manager/PermissionManager;", "", "()V", "createMediaProjectionIntent", "Landroid/content/Intent;", "context", "Landroid/content/Context;", "getMissingBasicPermissions", "", "", "ActivityPermissionHelper", "Companion", "OverlayPermissionCallback", "PermissionCallback", "PermissionHelper", "UnifiedMediaProjectionCallback", "app_debug"})
public final class PermissionManager {
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.example.castapp.manager.PermissionManager INSTANCE;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.manager.PermissionManager.Companion Companion = null;
    
    private PermissionManager() {
        super();
    }
    
    /**
     * 获取未授予的基础权限
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getMissingBasicPermissions(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    /**
     * 创建MediaProjection权限请求Intent
     */
    @org.jetbrains.annotations.Nullable()
    public final android.content.Intent createMediaProjectionIntent(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    /**
     * Activity版本的权限管理器辅助类
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0011\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0006\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\u0012\u001a\u00020\u0013J\u0012\u0010\u0014\u001a\u00020\u00132\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\u000bJ\u000e\u0010\u0016\u001a\u00020\u00132\u0006\u0010\u0015\u001a\u00020\u0010J\u0010\u0010\u0017\u001a\u00020\u00132\u0006\u0010\u0015\u001a\u00020\u0010H\u0002J\u000e\u0010\u0018\u001a\u00020\u00132\u0006\u0010\u0015\u001a\u00020\u0006R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\n\u001a\u0004\u0018\u00010\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\r0\bX\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000f\u001a\u0004\u0018\u00010\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0019"}, d2 = {"Lcom/example/castapp/manager/PermissionManager$ActivityPermissionHelper;", "", "activity", "Landroid/app/Activity;", "(Landroid/app/Activity;)V", "overlayPermissionCallback", "Lcom/example/castapp/manager/PermissionManager$OverlayPermissionCallback;", "overlayPermissionLauncher", "Landroidx/activity/result/ActivityResultLauncher;", "Landroid/content/Intent;", "permissionCallback", "Lcom/example/castapp/manager/PermissionManager$PermissionCallback;", "permissionLauncher", "", "", "unifiedMediaProjectionCallback", "Lcom/example/castapp/manager/PermissionManager$UnifiedMediaProjectionCallback;", "unifiedMediaProjectionLauncher", "initialize", "", "requestBasicPermissions", "callback", "requestMediaProjection", "requestNewMediaProjectionPermission", "requestOverlayPermission", "app_debug"})
    public static final class ActivityPermissionHelper {
        @org.jetbrains.annotations.NotNull()
        private final android.app.Activity activity = null;
        @org.jetbrains.annotations.Nullable()
        private com.example.castapp.manager.PermissionManager.PermissionCallback permissionCallback;
        @org.jetbrains.annotations.Nullable()
        private com.example.castapp.manager.PermissionManager.OverlayPermissionCallback overlayPermissionCallback;
        @org.jetbrains.annotations.Nullable()
        private com.example.castapp.manager.PermissionManager.UnifiedMediaProjectionCallback unifiedMediaProjectionCallback;
        private androidx.activity.result.ActivityResultLauncher<java.lang.String[]> permissionLauncher;
        private androidx.activity.result.ActivityResultLauncher<android.content.Intent> overlayPermissionLauncher;
        private androidx.activity.result.ActivityResultLauncher<android.content.Intent> unifiedMediaProjectionLauncher;
        
        public ActivityPermissionHelper(@org.jetbrains.annotations.NotNull()
        android.app.Activity activity) {
            super();
        }
        
        public final void initialize() {
        }
        
        /**
         * 请求基础权限
         */
        public final void requestBasicPermissions(@org.jetbrains.annotations.Nullable()
        com.example.castapp.manager.PermissionManager.PermissionCallback callback) {
        }
        
        /**
         * 请求悬浮窗权限
         */
        public final void requestOverlayPermission(@org.jetbrains.annotations.NotNull()
        com.example.castapp.manager.PermissionManager.OverlayPermissionCallback callback) {
        }
        
        /**
         * 🔥 关键修复：请求MediaProjection权限（Activity版本）
         * 智能权限复用：检查是否已有可用权限，如果有则直接返回，否则申请新权限
         */
        public final void requestMediaProjection(@org.jetbrains.annotations.NotNull()
        com.example.castapp.manager.PermissionManager.UnifiedMediaProjectionCallback callback) {
        }
        
        /**
         * 申请新的MediaProjection权限
         */
        private final void requestNewMediaProjectionPermission(com.example.castapp.manager.PermissionManager.UnifiedMediaProjectionCallback callback) {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0011\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\n\u001a\u00020\tR\u001a\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u00048BX\u0082\u0004\u00a2\u0006\u0006\u001a\u0004\b\u0006\u0010\u0007R\u0010\u0010\b\u001a\u0004\u0018\u00010\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/example/castapp/manager/PermissionManager$Companion;", "", "()V", "BASIC_PERMISSIONS", "", "", "getBASIC_PERMISSIONS", "()[Ljava/lang/String;", "INSTANCE", "Lcom/example/castapp/manager/PermissionManager;", "getInstance", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        private final java.lang.String[] getBASIC_PERMISSIONS() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.manager.PermissionManager getInstance() {
            return null;
        }
    }
    
    /**
     * 悬浮窗权限回调接口
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J\b\u0010\u0002\u001a\u00020\u0003H&J\b\u0010\u0004\u001a\u00020\u0003H&\u00a8\u0006\u0005"}, d2 = {"Lcom/example/castapp/manager/PermissionManager$OverlayPermissionCallback;", "", "onOverlayPermissionDenied", "", "onOverlayPermissionGranted", "app_debug"})
    public static abstract interface OverlayPermissionCallback {
        
        public abstract void onOverlayPermissionGranted();
        
        public abstract void onOverlayPermissionDenied();
    }
    
    /**
     * 权限结果回调接口
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H&J\b\u0010\u0007\u001a\u00020\u0003H&\u00a8\u0006\b"}, d2 = {"Lcom/example/castapp/manager/PermissionManager$PermissionCallback;", "", "onPermissionDenied", "", "deniedPermissions", "", "", "onPermissionGranted", "app_debug"})
    public static abstract interface PermissionCallback {
        
        public abstract void onPermissionGranted();
        
        public abstract void onPermissionDenied(@org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.String> deniedPermissions);
    }
    
    /**
     * Fragment权限管理器辅助类
     * 简化版：只保留统一MediaProjection权限管理
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u0007\u001a\u0010\u0012\f\u0012\n \n*\u0004\u0018\u00010\t0\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/example/castapp/manager/PermissionManager$PermissionHelper;", "", "fragment", "Landroidx/fragment/app/Fragment;", "(Landroidx/fragment/app/Fragment;)V", "unifiedMediaProjectionCallback", "Lcom/example/castapp/manager/PermissionManager$UnifiedMediaProjectionCallback;", "unifiedMediaProjectionLauncher", "Landroidx/activity/result/ActivityResultLauncher;", "Landroid/content/Intent;", "kotlin.jvm.PlatformType", "app_debug"})
    public static final class PermissionHelper {
        @org.jetbrains.annotations.NotNull()
        private final androidx.fragment.app.Fragment fragment = null;
        @org.jetbrains.annotations.Nullable()
        private com.example.castapp.manager.PermissionManager.UnifiedMediaProjectionCallback unifiedMediaProjectionCallback;
        @org.jetbrains.annotations.NotNull()
        private final androidx.activity.result.ActivityResultLauncher<android.content.Intent> unifiedMediaProjectionLauncher = null;
        
        public PermissionHelper(@org.jetbrains.annotations.NotNull()
        androidx.fragment.app.Fragment fragment) {
            super();
        }
    }
    
    /**
     * 统一MediaProjection权限回调接口
     * 唯一的MediaProjection权限管理接口
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J\b\u0010\u0002\u001a\u00020\u0003H&J\u0018\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH&J\u0018\u0010\t\u001a\u00020\u00032\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH&\u00a8\u0006\n"}, d2 = {"Lcom/example/castapp/manager/PermissionManager$UnifiedMediaProjectionCallback;", "", "onMediaProjectionDenied", "", "onMediaProjectionGranted", "resultCode", "", "data", "Landroid/content/Intent;", "onPermissionAlreadyGranted", "app_debug"})
    public static abstract interface UnifiedMediaProjectionCallback {
        
        public abstract void onMediaProjectionGranted(int resultCode, @org.jetbrains.annotations.NotNull()
        android.content.Intent data);
        
        public abstract void onMediaProjectionDenied();
        
        public abstract void onPermissionAlreadyGranted(int resultCode, @org.jetbrains.annotations.NotNull()
        android.content.Intent data);
    }
}