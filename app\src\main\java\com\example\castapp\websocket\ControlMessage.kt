package com.example.castapp.websocket

import com.google.gson.Gson
import com.google.gson.JsonSyntaxException

/**
 * WebSocket控制消息数据类
 */
data class ControlMessage(
    val type: String,
    val connectionId: String,
    val data: Map<String, Any> = emptyMap()
) {
    companion object {
        // 消息类型常量
        const val TYPE_CONNECTION_REQUEST = "connection_request"
        const val TYPE_CONNECTION_RESPONSE = "connection_response"
        const val TYPE_SCREEN_RESOLUTION = "screen_resolution"
        const val TYPE_DISCONNECT = "disconnect"
        const val TYPE_HEARTBEAT = "heartbeat"

        // 新增消息类型
        const val TYPE_H264_CONFIG = "h264_config"
        const val TYPE_SSRC_MAPPING = "ssrc_mapping"
        const val TYPE_CASTING_STATE = "casting_state"
        const val TYPE_BITRATE_CONTROL = "bitrate_control"

        // 分辨率控制消息类型
        const val TYPE_RESOLUTION_CHANGE = "resolution_change"
        const val TYPE_RESOLUTION_ADJUSTMENT_COMPLETE = "resolution_adjustment_complete"

        // 音频控制消息类型
        const val TYPE_MEDIA_AUDIO_CONTROL = "media_audio_control"
        const val TYPE_MIC_AUDIO_CONTROL = "mic_audio_control"

        // 音频配置数据消息类型
        const val TYPE_AAC_CONFIG = "aac_config"

        // 流控制消息类型
        const val TYPE_VIDEO_STREAM_STOP = "video_stream_stop"

        // 功能控制消息类型
        const val TYPE_FUNCTION_CONTROL = "function_control"

        // 远程控制消息类型
        const val TYPE_REMOTE_BITRATE_CHANGE = "remote_bitrate_change"
        const val TYPE_REMOTE_RESOLUTION_CHANGE = "remote_resolution_change"
        const val TYPE_REMOTE_VOLUME_CHANGE = "remote_volume_change"
        const val TYPE_REMOTE_CONNECTION_TOGGLE = "remote_connection_toggle"
        const val TYPE_REMOTE_SETTINGS_SYNC = "remote_settings_sync"
        const val TYPE_REMOTE_CONTROL_REQUEST = "remote_control_request"
        const val TYPE_REMOTE_CONTROL_RESPONSE = "remote_control_response"
        const val TYPE_REMOTE_CONNECTION_LIST_SYNC = "remote_connection_list_sync"
        const val TYPE_REMOTE_CONNECTION_ADDED = "remote_connection_added"
        const val TYPE_REMOTE_CONNECTION_UPDATED = "remote_connection_updated"
        const val TYPE_REMOTE_CONNECTION_REMOVED = "remote_connection_removed"

        // 🚀 新增：远程被控服务停止消息类型
        const val TYPE_REMOTE_CONTROL_SERVICE_STOPPED = "remote_control_service_stopped"
        const val TYPE_REMOTE_CONNECTION_ADD_REQUEST = "remote_connection_add_request"
        const val TYPE_REMOTE_CONNECTION_EDIT_REQUEST = "remote_connection_edit_request"
        const val TYPE_REMOTE_CONNECTION_DELETE_REQUEST = "remote_connection_delete_request"

        // 🎛️ 新增：远程接收端设置控制消息类型
        const val TYPE_REMOTE_RECEIVER_AUDIO_VIDEO_TOGGLE = "remote_receiver_audio_video_toggle"
        const val TYPE_REMOTE_RECEIVER_PLAYBACK_MODE_CHANGE = "remote_receiver_playback_mode_change"
        const val TYPE_REMOTE_RECEIVER_VOLUME_CHANGE = "remote_receiver_volume_change"
        const val TYPE_REMOTE_RECEIVER_SETTINGS_SYNC = "remote_receiver_settings_sync"
        const val TYPE_REMOTE_RECEIVER_SETTINGS_REQUEST = "remote_receiver_settings_request"

        // 🔄 新增：接收端到遥控端的反向同步消息类型
        const val TYPE_RECEIVER_LOCAL_AUDIO_VIDEO_CHANGED = "receiver_local_audio_video_changed"
        const val TYPE_RECEIVER_LOCAL_PLAYBACK_MODE_CHANGED = "receiver_local_playback_mode_changed"
        const val TYPE_RECEIVER_LOCAL_VOLUME_CHANGED = "receiver_local_volume_changed"
        const val TYPE_RECEIVER_LOCAL_SETTINGS_BROADCAST = "receiver_local_settings_broadcast"

        // 🪟 新增：投屏窗口管理消息类型
        const val TYPE_WINDOW_MANAGER_REQUEST = "window_manager_request"
        const val TYPE_WINDOW_MANAGER_RESPONSE = "window_manager_response"

        // 📸 新增：截图功能消息类型
        const val TYPE_SCREENSHOT_REQUEST = "screenshot_request"
        const val TYPE_SCREENSHOT_RESPONSE = "screenshot_response"
        const val TYPE_SCREENSHOT_ERROR = "screenshot_error"

        // 📝 新增：文字内容获取消息类型
        const val TYPE_TEXT_CONTENT_REQUEST = "text_content_request"
        const val TYPE_TEXT_CONTENT_RESPONSE = "text_content_response"
        const val TYPE_TEXT_CONTENT_ERROR = "text_content_error"

        // 📝 新增：文字格式同步消息类型
        const val TYPE_TEXT_FORMAT_SYNC = "text_format_sync"

        // 📐 屏幕分辨率请求响应消息类型
        const val TYPE_SCREEN_RESOLUTION_REQUEST = "screen_resolution_request"
        const val TYPE_SCREEN_RESOLUTION_RESPONSE = "screen_resolution_response"

        // 🎯 横屏模式控制消息类型
        const val TYPE_LANDSCAPE_MODE_CONTROL = "landscape_mode_control"

        // 🔄 新增：远程窗口变换控制消息类型
        const val TYPE_REMOTE_WINDOW_TRANSFORM_CONTROL = "remote_window_transform_control"

        // 🏷️ 新增：备注更新消息类型
        const val TYPE_NOTE_UPDATE = "note_update"

        private val gson = Gson()

        // ========== 🎛️ 远程接收端设置控制消息创建方法 ==========

        /**
         * 创建远程接收端音视频服务切换消息
         */
        fun createRemoteReceiverAudioVideoToggle(connectionId: String, enabled: Boolean): ControlMessage {
            return ControlMessage(
                type = TYPE_REMOTE_RECEIVER_AUDIO_VIDEO_TOGGLE,
                connectionId = connectionId,
                data = mapOf(
                    "enabled" to enabled,
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 创建远程接收端播放模式切换消息
         */
        fun createRemoteReceiverPlaybackModeChange(connectionId: String, isSpeakerMode: Boolean): ControlMessage {
            return ControlMessage(
                type = TYPE_REMOTE_RECEIVER_PLAYBACK_MODE_CHANGE,
                connectionId = connectionId,
                data = mapOf(
                    "is_speaker_mode" to isSpeakerMode,
                    "mode" to if (isSpeakerMode) "speaker" else "earpiece",
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 创建远程接收端音量调整消息
         */
        fun createRemoteReceiverVolumeChange(connectionId: String, volume: Int): ControlMessage {
            return ControlMessage(
                type = TYPE_REMOTE_RECEIVER_VOLUME_CHANGE,
                connectionId = connectionId,
                data = mapOf(
                    "volume" to volume,
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 创建远程接收端设置同步消息
         */
        fun createRemoteReceiverSettingsSync(connectionId: String, settings: Map<String, Any>): ControlMessage {
            return ControlMessage(
                type = TYPE_REMOTE_RECEIVER_SETTINGS_SYNC,
                connectionId = connectionId,
                data = settings + mapOf("timestamp" to System.currentTimeMillis())
            )
        }

        /**
         * 创建远程接收端设置请求消息
         */
        fun createRemoteReceiverSettingsRequest(connectionId: String): ControlMessage {
            return ControlMessage(
                type = TYPE_REMOTE_RECEIVER_SETTINGS_REQUEST,
                connectionId = connectionId,
                data = mapOf("timestamp" to System.currentTimeMillis())
            )
        }

        // ========== 🔄 接收端到遥控端的反向同步消息创建方法 ==========

        /**
         * 创建接收端本地音视频服务状态变更消息
         */
        fun createReceiverLocalAudioVideoChanged(connectionId: String, enabled: Boolean): ControlMessage {
            return ControlMessage(
                type = TYPE_RECEIVER_LOCAL_AUDIO_VIDEO_CHANGED,
                connectionId = connectionId,
                data = mapOf(
                    "enabled" to enabled,
                    "source" to "local_operation",
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 创建接收端本地播放模式变更消息
         */
        fun createReceiverLocalPlaybackModeChanged(connectionId: String, isSpeakerMode: Boolean): ControlMessage {
            return ControlMessage(
                type = TYPE_RECEIVER_LOCAL_PLAYBACK_MODE_CHANGED,
                connectionId = connectionId,
                data = mapOf(
                    "is_speaker_mode" to isSpeakerMode,
                    "mode" to if (isSpeakerMode) "speaker" else "earpiece",
                    "source" to "local_operation",
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 创建接收端本地音量变更消息
         */
        fun createReceiverLocalVolumeChanged(connectionId: String, volume: Int): ControlMessage {
            return ControlMessage(
                type = TYPE_RECEIVER_LOCAL_VOLUME_CHANGED,
                connectionId = connectionId,
                data = mapOf(
                    "volume" to volume,
                    "source" to "local_operation",
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        // ========== 🪟 投屏窗口管理消息创建方法 ==========

        /**
         * 创建投屏窗口管理请求消息
         */
        fun createWindowManagerRequest(connectionId: String): ControlMessage {
            return ControlMessage(
                type = TYPE_WINDOW_MANAGER_REQUEST,
                connectionId = connectionId,
                data = mapOf(
                    "timestamp" to System.currentTimeMillis(),
                    "request_type" to "get_window_info"
                )
            )
        }

        /**
         * 创建投屏窗口管理响应消息
         */
        fun createWindowManagerResponse(connectionId: String, windowInfoList: List<Map<String, Any>>): ControlMessage {
            return ControlMessage(
                type = TYPE_WINDOW_MANAGER_RESPONSE,
                connectionId = connectionId,
                data = mapOf(
                    "timestamp" to System.currentTimeMillis(),
                    "window_count" to windowInfoList.size,
                    "window_info_list" to windowInfoList,
                    "success" to true
                )
            )
        }

        /**
         * 从JSON字符串解析控制消息
         */
        fun fromJson(json: String): ControlMessage? {
            return try {
                gson.fromJson(json, ControlMessage::class.java)
            } catch (_: JsonSyntaxException) {
                null
            }
        }

        /**
         * 创建纯净连接请求消息
         */
        fun createConnectionRequest(connectionId: String, deviceName: String? = null): ControlMessage {
            val data = mutableMapOf<String, Any>("timestamp" to System.currentTimeMillis())
            deviceName?.let { data["device_name"] = it }

            return ControlMessage(
                type = TYPE_CONNECTION_REQUEST,
                connectionId = connectionId,
                data = data
            )
        }

        /**
         * 创建连接响应消息
         */
        fun createConnectionResponse(connectionId: String, success: Boolean, message: String = ""): ControlMessage {
            return ControlMessage(
                type = TYPE_CONNECTION_RESPONSE,
                connectionId = connectionId,
                data = mapOf(
                    "success" to success,
                    "message" to message,
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 创建屏幕分辨率消息
         */
        fun createScreenResolution(connectionId: String, width: Int, height: Int): ControlMessage {
            return ControlMessage(
                type = TYPE_SCREEN_RESOLUTION,
                connectionId = connectionId,
                data = mapOf(
                    "width" to width,
                    "height" to height,
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 🎯 创建横屏模式控制消息
         * 用于接收端控制发送端的横屏检测功能
         */
        fun createLandscapeModeControl(connectionId: String, enabled: Boolean): ControlMessage {
            return ControlMessage(
                type = TYPE_LANDSCAPE_MODE_CONTROL,
                connectionId = connectionId,
                data = mapOf(
                    "enabled" to enabled,
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 🔄 创建远程窗口变换控制消息
         */
        fun createRemoteWindowTransformControl(
            connectionId: String,
            targetWindowId: String,
            transformType: String,
            transformData: Map<String, Any>
        ): ControlMessage {
            return ControlMessage(
                type = TYPE_REMOTE_WINDOW_TRANSFORM_CONTROL,
                connectionId = connectionId,
                data = mapOf(
                    "target_window_id" to targetWindowId,
                    "transform_type" to transformType, // "position", "scale", "rotation", "crop", "visibility", "mirror", "border", "alpha", "corner_radius"
                    "transform_data" to transformData,
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 🏷️ 创建备注更新消息
         */
        fun createNoteUpdate(
            connectionId: String,
            targetWindowId: String,
            note: String
        ): ControlMessage {
            return ControlMessage(
                type = TYPE_NOTE_UPDATE,
                connectionId = connectionId,
                data = mapOf(
                    "target_window_id" to targetWindowId,
                    "note" to note,
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 创建分辨率变化消息
         * 用于通知接收端实时分辨率调整
         */
        fun createResolutionChange(connectionId: String, width: Int, height: Int): ControlMessage {
            return ControlMessage(
                type = TYPE_RESOLUTION_CHANGE,
                connectionId = connectionId,
                data = mapOf(
                    "width" to width,
                    "height" to height,
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 创建分辨率调整完成消息
         * 用于接收端通知发送端分辨率调整已完成
         */
        fun createResolutionAdjustmentComplete(connectionId: String, width: Int, height: Int, success: Boolean, error: String? = null): ControlMessage {
            val data = mutableMapOf<String, Any>(
                "width" to width,
                "height" to height,
                "success" to success,
                "timestamp" to System.currentTimeMillis()
            )

            error?.let {
                data["error"] = it
            }

            return ControlMessage(
                type = TYPE_RESOLUTION_ADJUSTMENT_COMPLETE,
                connectionId = connectionId,
                data = data
            )
        }



        /**
         * 创建断开连接消息
         */
        fun createDisconnect(connectionId: String): ControlMessage {
            return ControlMessage(
                type = TYPE_DISCONNECT,
                connectionId = connectionId,
                data = mapOf("timestamp" to System.currentTimeMillis())
            )
        }

        /**
         * 🚀 创建远程被控服务停止消息
         * 用于7777端口服务器停止时通知遥控端
         */
        fun createRemoteControlServiceStopped(connectionId: String, reason: String = "用户关闭远程被控开关"): ControlMessage {
            return ControlMessage(
                type = TYPE_REMOTE_CONTROL_SERVICE_STOPPED,
                connectionId = connectionId,
                data = mapOf(
                    "timestamp" to System.currentTimeMillis(),
                    "reason" to reason
                )
            )
        }

        /**
         * 创建心跳消息
         */
        fun createHeartbeat(connectionId: String): ControlMessage {
            return ControlMessage(
                type = TYPE_HEARTBEAT,
                connectionId = connectionId,
                data = mapOf("timestamp" to System.currentTimeMillis())
            )
        }

        /**
         * 创建H.264配置数据消息
         * 🚀 增强版：包含分辨率信息，提供双重验证机制
         */
        fun createH264Config(connectionId: String, spsData: ByteArray?, ppsData: ByteArray?): ControlMessage {
            val data = mutableMapOf<String, Any>(
                "timestamp" to System.currentTimeMillis()
            )

            spsData?.let {
                data["sps"] = android.util.Base64.encodeToString(it, android.util.Base64.NO_WRAP)
            }
            ppsData?.let {
                data["pps"] = android.util.Base64.encodeToString(it, android.util.Base64.NO_WRAP)
            }

            return ControlMessage(
                type = TYPE_H264_CONFIG,
                connectionId = connectionId,
                data = data
            )
        }

        /**
         * 🚀 新增：创建包含分辨率信息的H.264配置数据消息
         * 提供WebSocket分辨率传递机制，作为SPS解析的补充验证
         */
        fun createH264ConfigWithResolution(
            connectionId: String,
            spsData: ByteArray?,
            ppsData: ByteArray?,
            width: Int,
            height: Int
        ): ControlMessage {
            val data = mutableMapOf<String, Any>(
                "timestamp" to System.currentTimeMillis(),
                "width" to width,
                "height" to height
            )

            return createH264ConfigWithResolutionAndOrientation(connectionId, spsData, ppsData, width, height, null, data)
        }

        /**
         * 🎯 横竖屏适配：创建包含分辨率和方向信息的H.264配置数据消息
         */
        fun createH264ConfigWithResolutionAndOrientation(
            connectionId: String,
            spsData: ByteArray?,
            ppsData: ByteArray?,
            width: Int,
            height: Int,
            orientation: Int? = null,
            existingData: MutableMap<String, Any>? = null
        ): ControlMessage {
            val data = existingData ?: mutableMapOf<String, Any>(
                "timestamp" to System.currentTimeMillis(),
                "width" to width,
                "height" to height
            )

            // 🎯 添加方向信息
            orientation?.let {
                data["orientation"] = it
            }

            spsData?.let {
                data["sps"] = android.util.Base64.encodeToString(it, android.util.Base64.NO_WRAP)
            }
            ppsData?.let {
                data["pps"] = android.util.Base64.encodeToString(it, android.util.Base64.NO_WRAP)
            }

            return ControlMessage(
                type = TYPE_H264_CONFIG,
                connectionId = connectionId,
                data = data
            )
        }

        /**
         * 创建AAC配置数据消息
         */
        fun createAacConfig(connectionId: String, configData: ByteArray?): ControlMessage {
            val data = mutableMapOf<String, Any>(
                "timestamp" to System.currentTimeMillis()
            )

            configData?.let {
                data["config"] = android.util.Base64.encodeToString(it, android.util.Base64.NO_WRAP)
            }

            return ControlMessage(
                type = TYPE_AAC_CONFIG,
                connectionId = connectionId,
                data = data
            )
        }

        /**
         * 创建SSRC映射消息
         */
        fun createSsrcMapping(connectionId: String, ssrc: Long): ControlMessage {
            return ControlMessage(
                type = TYPE_SSRC_MAPPING,
                connectionId = connectionId,
                data = mapOf(
                    "ssrc" to ssrc,
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 创建投屏状态同步消息
         */
        fun createCastingState(connectionId: String, isCasting: Boolean, reason: String = ""): ControlMessage {
            return ControlMessage(
                type = TYPE_CASTING_STATE,
                connectionId = connectionId,
                data = mapOf(
                    "is_casting" to isCasting,
                    "reason" to reason,
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 创建码率控制消息
         */
        fun createBitrateControl(connectionId: String, bitrate: Int, targetConnectionId: String? = null): ControlMessage {
            val data = mutableMapOf<String, Any>(
                "bitrate" to bitrate,
                "timestamp" to System.currentTimeMillis()
            )

            targetConnectionId?.let {
                data["target_connection_id"] = it
            }

            return ControlMessage(
                type = TYPE_BITRATE_CONTROL,
                connectionId = connectionId,
                data = data
            )
        }

        /**
         * 创建媒体音频控制消息
         */
        fun createMediaAudioControl(connectionId: String, isEnabled: Boolean): ControlMessage {
            return ControlMessage(
                type = TYPE_MEDIA_AUDIO_CONTROL,
                connectionId = connectionId,
                data = mapOf(
                    "enabled" to isEnabled,
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 创建麦克风音频控制消息
         */
        fun createMicAudioControl(connectionId: String, isEnabled: Boolean): ControlMessage {
            return ControlMessage(
                type = TYPE_MIC_AUDIO_CONTROL,
                connectionId = connectionId,
                data = mapOf(
                    "enabled" to isEnabled,
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 创建视频流停止消息
         */
        fun createVideoStreamStop(connectionId: String): ControlMessage {
            return ControlMessage(
                type = TYPE_VIDEO_STREAM_STOP,
                connectionId = connectionId,
                data = mapOf("timestamp" to System.currentTimeMillis())
            )
        }

        /**
         * 创建功能控制消息
         */
        fun createFunctionControl(connectionId: String, functionType: String, enabled: Boolean): ControlMessage {
            return ControlMessage(
                type = TYPE_FUNCTION_CONTROL,
                connectionId = connectionId,
                data = mapOf(
                    "function_type" to functionType,
                    "enabled" to enabled,
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 创建远程控制请求消息
         */
        fun createRemoteControlRequest(connectionId: String, deviceName: String): ControlMessage {
            return ControlMessage(
                type = TYPE_REMOTE_CONTROL_REQUEST,
                connectionId = connectionId,
                data = mapOf(
                    "device_name" to deviceName,
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 创建远程控制响应消息
         */
        fun createRemoteControlResponse(connectionId: String, success: Boolean, message: String = ""): ControlMessage {
            return ControlMessage(
                type = TYPE_REMOTE_CONTROL_RESPONSE,
                connectionId = connectionId,
                data = mapOf(
                    "success" to success,
                    "message" to message,
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 创建远程码率变更消息
         */
        fun createRemoteBitrateChange(connectionId: String, bitrateMbps: Int): ControlMessage {
            return ControlMessage(
                type = TYPE_REMOTE_BITRATE_CHANGE,
                connectionId = connectionId,
                data = mapOf(
                    "bitrate_mbps" to bitrateMbps,
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 创建远程分辨率变更消息
         */
        fun createRemoteResolutionChange(connectionId: String, scalePercent: Int): ControlMessage {
            return ControlMessage(
                type = TYPE_REMOTE_RESOLUTION_CHANGE,
                connectionId = connectionId,
                data = mapOf(
                    "scale_percent" to scalePercent,
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 创建远程音量变更消息
         */
        fun createRemoteVolumeChange(connectionId: String, volumeType: String, volume: Int): ControlMessage {
            return ControlMessage(
                type = TYPE_REMOTE_VOLUME_CHANGE,
                connectionId = connectionId,
                data = mapOf(
                    "volume_type" to volumeType, // "media" or "mic"
                    "volume" to volume,
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 创建远程连接切换消息
         */
        fun createRemoteConnectionToggle(connectionId: String, functionType: String, enabled: Boolean, targetConnectionId: String? = null): ControlMessage {
            val dataMap = mutableMapOf<String, Any>(
                "function_type" to functionType, // "video", "media_audio", "mic_audio"
                "enabled" to enabled,
                "timestamp" to System.currentTimeMillis()
            )

            // 只有当 targetConnectionId 不为 null 时才添加到 map 中
            targetConnectionId?.let { dataMap["target_connection_id"] = it }

            return ControlMessage(
                type = TYPE_REMOTE_CONNECTION_TOGGLE,
                connectionId = connectionId,
                data = dataMap
            )
        }

        /**
         * 创建远程设置同步消息
         */
        fun createRemoteSettingsSync(connectionId: String, settings: Map<String, Any>): ControlMessage {
            return ControlMessage(
                type = TYPE_REMOTE_SETTINGS_SYNC,
                connectionId = connectionId,
                data = settings + mapOf("timestamp" to System.currentTimeMillis())
            )
        }

        /**
         * 创建远程连接添加消息
         */
        fun createRemoteConnectionAdded(connectionId: String, connectionData: Map<String, Any>): ControlMessage {
            return ControlMessage(
                type = TYPE_REMOTE_CONNECTION_ADDED,
                connectionId = connectionId,
                data = mapOf(
                    "connection" to connectionData,
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 创建远程连接更新消息
         */
        fun createRemoteConnectionUpdated(connectionId: String, connectionData: Map<String, Any>): ControlMessage {
            return ControlMessage(
                type = TYPE_REMOTE_CONNECTION_UPDATED,
                connectionId = connectionId,
                data = mapOf(
                    "connection" to connectionData,
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 创建远程连接删除消息
         */
        fun createRemoteConnectionRemoved(connectionId: String, removedConnectionId: String): ControlMessage {
            return ControlMessage(
                type = TYPE_REMOTE_CONNECTION_REMOVED,
                connectionId = connectionId,
                data = mapOf(
                    "removed_connection_id" to removedConnectionId,
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 创建远程连接添加请求
         */
        fun createRemoteConnectionAddRequest(connectionId: String, ipAddress: String, port: Int): ControlMessage {
            return ControlMessage(
                type = TYPE_REMOTE_CONNECTION_ADD_REQUEST,
                connectionId = connectionId,
                data = mapOf(
                    "ip_address" to ipAddress,
                    "port" to port,
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 创建远程连接编辑请求
         */
        fun createRemoteConnectionEditRequest(connectionId: String, targetConnectionId: String, newIpAddress: String, newPort: Int): ControlMessage {
            return ControlMessage(
                type = TYPE_REMOTE_CONNECTION_EDIT_REQUEST,
                connectionId = connectionId,
                data = mapOf(
                    "target_connection_id" to targetConnectionId,
                    "new_ip_address" to newIpAddress,
                    "new_port" to newPort,
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 创建远程连接删除请求
         */
        fun createRemoteConnectionDeleteRequest(connectionId: String, targetConnectionId: String): ControlMessage {
            return ControlMessage(
                type = TYPE_REMOTE_CONNECTION_DELETE_REQUEST,
                connectionId = connectionId,
                data = mapOf(
                    "target_connection_id" to targetConnectionId,
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        // ========== 📸 截图功能消息创建方法 ==========

        /**
         * 创建截图请求消息
         */
        fun createScreenshotRequest(connectionId: String): ControlMessage {
            return ControlMessage(
                type = TYPE_SCREENSHOT_REQUEST,
                connectionId = connectionId,
                data = mapOf(
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 创建截图响应消息
         */
        fun createScreenshotResponse(connectionId: String, screenshotDataList: List<Map<String, Any>>): ControlMessage {
            return ControlMessage(
                type = TYPE_SCREENSHOT_RESPONSE,
                connectionId = connectionId,
                data = mapOf(
                    "screenshots" to screenshotDataList,
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 创建截图错误消息
         */
        fun createScreenshotError(connectionId: String, errorMessage: String): ControlMessage {
            return ControlMessage(
                type = TYPE_SCREENSHOT_ERROR,
                connectionId = connectionId,
                data = mapOf(
                    "error" to errorMessage,
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        // ========== 📝 文字内容获取消息创建方法 ==========

        /**
         * 创建文字内容请求消息
         */
        fun createTextContentRequest(connectionId: String): ControlMessage {
            return ControlMessage(
                type = TYPE_TEXT_CONTENT_REQUEST,
                connectionId = connectionId,
                data = mapOf(
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 创建文字内容响应消息
         */
        fun createTextContentResponse(connectionId: String, textContentDataList: List<Map<String, Any>>): ControlMessage {
            return ControlMessage(
                type = TYPE_TEXT_CONTENT_RESPONSE,
                connectionId = connectionId,
                data = mapOf(
                    "text_contents" to textContentDataList,
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 创建文字内容错误消息
         */
        fun createTextContentError(connectionId: String, errorMessage: String): ControlMessage {
            return ControlMessage(
                type = TYPE_TEXT_CONTENT_ERROR,
                connectionId = connectionId,
                data = mapOf(
                    "error" to errorMessage,
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 创建文字格式同步消息
         */
        fun createTextFormatSyncMessage(connectionId: String, formatData: Map<String, Any>): ControlMessage {
            return ControlMessage(
                type = TYPE_TEXT_FORMAT_SYNC,
                connectionId = connectionId,
                data = mapOf(
                    "format_data" to formatData,
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        // ========== 📐 屏幕分辨率请求响应消息创建方法 ==========

        /**
         * 创建屏幕分辨率请求消息
         */
        fun createScreenResolutionRequest(connectionId: String): ControlMessage {
            return ControlMessage(
                type = TYPE_SCREEN_RESOLUTION_REQUEST,
                connectionId = connectionId,
                data = mapOf(
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }

        /**
         * 创建屏幕分辨率响应消息
         */
        fun createScreenResolutionResponse(connectionId: String, width: Int, height: Int): ControlMessage {
            return ControlMessage(
                type = TYPE_SCREEN_RESOLUTION_RESPONSE,
                connectionId = connectionId,
                data = mapOf(
                    "width" to width,
                    "height" to height,
                    "timestamp" to System.currentTimeMillis()
                )
            )
        }
    }

    /**
     * 获取布尔类型数据
     */
    fun getBooleanData(key: String): Boolean? {
        val value = data[key]
        return when (value) {
            is Boolean -> value
            is String -> value.toBooleanStrictOrNull()
            else -> null
        }
    }

    /**
     * 获取字符串数据
     */
    fun getStringData(key: String): String? {
        return data[key]?.toString()
    }

    /**
     * 获取整数数据
     */
    fun getIntData(key: String): Int? {
        val value = data[key]
        return when (value) {
            is Number -> value.toInt()
            is String -> value.toIntOrNull()
            else -> null
        }
    }

    /**
     * 获取Long数据
     */
    fun getLongData(key: String): Long? {
        val value = data[key]
        return when (value) {
            is Number -> value.toLong()
            is String -> value.toLongOrNull()
            else -> null
        }
    }

    /**
     * 获取字节数组数据
     */
    fun getByteArrayData(key: String): ByteArray? {
        val value = data[key]
        return if (value is String) {
            try {
                android.util.Base64.decode(value, android.util.Base64.NO_WRAP)
            } catch (_: Exception) {
                null
            }
        } else {
            null
        }
    }

    /**
     * 转换为JSON字符串
     */
    fun toJson(): String {
        return gson.toJson(this)
    }
}
