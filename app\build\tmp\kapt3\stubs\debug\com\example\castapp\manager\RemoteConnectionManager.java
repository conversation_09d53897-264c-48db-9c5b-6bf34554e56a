package com.example.castapp.manager;

/**
 * 🐾 远程连接全局管理器
 * 负责管理所有远程连接的全局状态，包括WebSocket客户端、连接状态和控制对话框
 *
 * 主要功能：
 * - 全局WebSocket客户端管理（发送端和接收端）
 * - 连接状态的持久化存储和恢复
 * - 活跃控制对话框的生命周期管理
 * - 线程安全的状态操作
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000P\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010%\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\t\n\u0002\u0010$\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\t\u0018\u0000 )2\u00020\u0001:\u0001)B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0016\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u00052\u0006\u0010\u0011\u001a\u00020\u0006J\u0016\u0010\u0012\u001a\u00020\u000f2\u0006\u0010\u0013\u001a\u00020\u00052\u0006\u0010\u0014\u001a\u00020\nJ\u0016\u0010\u0015\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u00052\u0006\u0010\u0014\u001a\u00020\nJ\u0006\u0010\u0016\u001a\u00020\u000fJ\u0010\u0010\u0017\u001a\u0004\u0018\u00010\u00062\u0006\u0010\u0010\u001a\u00020\u0005J\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u001a0\u0019J\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\b0\u001cJ\u0010\u0010\u001d\u001a\u0004\u0018\u00010\n2\u0006\u0010\u0013\u001a\u00020\u0005J\u0010\u0010\u001e\u001a\u0004\u0018\u00010\n2\u0006\u0010\u0010\u001a\u00020\u0005J\u000e\u0010\u001f\u001a\u00020\u000f2\u0006\u0010 \u001a\u00020!J\u000e\u0010\"\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0005J\u000e\u0010#\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0005J\u000e\u0010$\u001a\u00020\u000f2\u0006\u0010\u0013\u001a\u00020\u0005J\u000e\u0010%\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0005J\u000e\u0010&\u001a\u00020\u000f2\u0006\u0010 \u001a\u00020!J\u000e\u0010\'\u001a\u00020\u000f2\u0006\u0010(\u001a\u00020\bR\u001a\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\b0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\n0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\n0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006*"}, d2 = {"Lcom/example/castapp/manager/RemoteConnectionManager;", "", "()V", "globalActiveControlDialogs", "", "", "Lcom/example/castapp/ui/dialog/RemoteSenderControlDialog;", "globalConnectionStates", "Lcom/example/castapp/model/RemoteSenderConnection;", "globalReceiverClients", "Lcom/example/castapp/remote/RemoteSenderWebSocketClient;", "globalRemoteClients", "gson", "Lcom/google/gson/Gson;", "addActiveControlDialog", "", "connectionId", "dialog", "addReceiverClient", "receiverId", "client", "addRemoteClient", "cleanup", "getActiveControlDialog", "getAllRemoteConnectionStates", "", "", "getGlobalConnectionStates", "", "getReceiverClient", "getRemoteClient", "initializeGlobalConnectionStates", "context", "Landroid/content/Context;", "removeActiveControlDialog", "removeGlobalConnectionState", "removeReceiverClient", "removeRemoteClient", "saveConnectionsToPreferences", "updateGlobalConnectionState", "connection", "Companion", "app_debug"})
public final class RemoteConnectionManager {
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.example.castapp.manager.RemoteConnectionManager INSTANCE;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.String, com.example.castapp.remote.RemoteSenderWebSocketClient> globalRemoteClients = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.String, com.example.castapp.remote.RemoteSenderWebSocketClient> globalReceiverClients = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.String, com.example.castapp.ui.dialog.RemoteSenderControlDialog> globalActiveControlDialogs = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.String, com.example.castapp.model.RemoteSenderConnection> globalConnectionStates = null;
    @org.jetbrains.annotations.NotNull()
    private final com.google.gson.Gson gson = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.manager.RemoteConnectionManager.Companion Companion = null;
    
    private RemoteConnectionManager() {
        super();
    }
    
    /**
     * 获取发送端远程连接客户端
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.remote.RemoteSenderWebSocketClient getRemoteClient(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
        return null;
    }
    
    /**
     * 添加发送端远程连接客户端
     */
    public final void addRemoteClient(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    com.example.castapp.remote.RemoteSenderWebSocketClient client) {
    }
    
    /**
     * 移除发送端远程连接客户端
     */
    public final void removeRemoteClient(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 获取所有发送端远程连接的状态
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.Boolean> getAllRemoteConnectionStates() {
        return null;
    }
    
    /**
     * 添加接收端WebSocket客户端
     */
    public final void addReceiverClient(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId, @org.jetbrains.annotations.NotNull()
    com.example.castapp.remote.RemoteSenderWebSocketClient client) {
    }
    
    /**
     * 🚀 高效移除接收端WebSocket客户端
     * 优化：原子化操作，避免资源泄漏
     */
    public final void removeReceiverClient(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId) {
    }
    
    /**
     * 获取接收端WebSocket客户端
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.remote.RemoteSenderWebSocketClient getReceiverClient(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId) {
        return null;
    }
    
    /**
     * 添加活跃的控制对话框
     */
    public final void addActiveControlDialog(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    com.example.castapp.ui.dialog.RemoteSenderControlDialog dialog) {
    }
    
    /**
     * 移除活跃的控制对话框
     */
    public final void removeActiveControlDialog(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 获取活跃的控制对话框
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.ui.dialog.RemoteSenderControlDialog getActiveControlDialog(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
        return null;
    }
    
    /**
     * 🐾 更新全局连接状态
     */
    public final void updateGlobalConnectionState(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.RemoteSenderConnection connection) {
    }
    
    /**
     * 🐾 获取全局连接状态列表
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.castapp.model.RemoteSenderConnection> getGlobalConnectionStates() {
        return null;
    }
    
    /**
     * 🐾 移除全局连接状态
     */
    public final void removeGlobalConnectionState(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 🐾 独立的状态保存方法，不依赖Fragment
     */
    public final void saveConnectionsToPreferences(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    /**
     * 🐾 初始化全局连接状态（从SharedPreferences加载）
     */
    public final void initializeGlobalConnectionStates(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    /**
     * 清理所有资源（用于应用退出时）
     */
    public final void cleanup() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0005\u001a\u00020\u0004R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0006"}, d2 = {"Lcom/example/castapp/manager/RemoteConnectionManager$Companion;", "", "()V", "INSTANCE", "Lcom/example/castapp/manager/RemoteConnectionManager;", "getInstance", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.manager.RemoteConnectionManager getInstance() {
            return null;
        }
    }
}