package com.example.castapp.audio;

/**
 * 音频RTP接收器
 * 专门用于接收AAC音频数据
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000V\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0012\n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0002\b\b\u0018\u0000 \'2\u00020\u0001:\u0002\'(B\'\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0018\u0010\u0004\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\b0\u0005\u00a2\u0006\u0002\u0010\tJ\b\u0010\u0016\u001a\u00020\bH\u0002J(\u0010\u0017\u001a\u00020\b2\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u00032\u0006\u0010\u001b\u001a\u00020\u00072\u0006\u0010\u001c\u001a\u00020\u0007H\u0002J\b\u0010\u001d\u001a\u00020\bH\u0002J0\u0010\u001e\u001a\u00020\b2\u0006\u0010\u001f\u001a\u00020\u00062\u0006\u0010\u001a\u001a\u00020\u00032\u0006\u0010\u001b\u001a\u00020\u00072\u0006\u0010\u001c\u001a\u00020\u00072\u0006\u0010 \u001a\u00020!H\u0002J\u0010\u0010\"\u001a\u00020\b2\u0006\u0010#\u001a\u00020\u0006H\u0002J\u0006\u0010$\u001a\u00020!J\b\u0010%\u001a\u00020\bH\u0002J\u0006\u0010&\u001a\u00020\bR\u000e\u0010\n\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R \u0010\u000b\u001a\u0014\u0012\u0004\u0012\u00020\r\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\u000e0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R \u0010\u0004\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\b0\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0003X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006)"}, d2 = {"Lcom/example/castapp/audio/AudioRtpReceiver;", "", "port", "", "onAacDataReceived", "Lkotlin/Function2;", "Lcom/example/castapp/network/DataView;", "", "", "(ILkotlin/jvm/functions/Function2;)V", "bytesReceived", "fragmentCache", "Ljava/util/concurrent/ConcurrentHashMap;", "", "", "Lcom/example/castapp/audio/AudioRtpReceiver$FragmentInfo;", "isRunning", "Ljava/util/concurrent/atomic/AtomicBoolean;", "lastLogTime", "packetsReceived", "udpReceiver", "Lcom/example/castapp/network/UdpReceiver;", "cleanupExpiredFragments", "handleFragment", "data", "", "sequenceNumber", "timestamp", "ssrc", "logStatisticsIfNeeded", "processAacPayloadView", "payloadView", "marker", "", "processRtpPacketZeroCopy", "dataView", "start", "startReceiveThread", "stop", "Companion", "FragmentInfo", "app_debug"})
public final class AudioRtpReceiver {
    private final int port = 0;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function2<com.example.castapp.network.DataView, java.lang.Long, kotlin.Unit> onAacDataReceived = null;
    private static final int RTP_VERSION = 2;
    private static final long LOG_INTERVAL_MS = 60000L;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.network.UdpReceiver udpReceiver = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.atomic.AtomicBoolean isRunning = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, java.util.List<com.example.castapp.audio.AudioRtpReceiver.FragmentInfo>> fragmentCache = null;
    private long lastLogTime = 0L;
    private int packetsReceived = 0;
    private long bytesReceived = 0L;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.audio.AudioRtpReceiver.Companion Companion = null;
    
    public AudioRtpReceiver(int port, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super com.example.castapp.network.DataView, ? super java.lang.Long, kotlin.Unit> onAacDataReceived) {
        super();
    }
    
    /**
     * 启动RTP接收器
     */
    public final boolean start() {
        return false;
    }
    
    /**
     * 停止RTP接收器
     */
    public final void stop() {
    }
    
    /**
     * 启动接收线程
     */
    private final void startReceiveThread() {
    }
    
    /**
     * 处理RTP包 - 零拷贝优化版本
     */
    private final void processRtpPacketZeroCopy(com.example.castapp.network.DataView dataView) {
    }
    
    /**
     * 处理AAC载荷 - 零拷贝优化版本
     */
    private final void processAacPayloadView(com.example.castapp.network.DataView payloadView, int sequenceNumber, long timestamp, long ssrc, boolean marker) {
    }
    
    /**
     * 处理分片数据
     */
    private final void handleFragment(byte[] data, int sequenceNumber, long timestamp, long ssrc) {
    }
    
    /**
     * 定期输出统计信息
     */
    private final void logStatisticsIfNeeded() {
    }
    
    /**
     * 清理过期的分片
     */
    private final void cleanupExpiredFragments() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\b\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0007"}, d2 = {"Lcom/example/castapp/audio/AudioRtpReceiver$Companion;", "", "()V", "LOG_INTERVAL_MS", "", "RTP_VERSION", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u0012\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0014\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B/\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\b\b\u0002\u0010\n\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u000bJ\t\u0010\u0014\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0016\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u0017\u001a\u00020\tH\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0005H\u00c6\u0003J;\u0010\u0019\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u001a\u001a\u00020\t2\b\u0010\u001b\u001a\u0004\u0018\u00010\u0001H\u0096\u0002J\b\u0010\u001c\u001a\u00020\u0003H\u0016J\t\u0010\u001d\u001a\u00020\u001eH\u00d6\u0001R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u000eR\u0011\u0010\n\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0010\u00a8\u0006\u001f"}, d2 = {"Lcom/example/castapp/audio/AudioRtpReceiver$FragmentInfo;", "", "sequenceNumber", "", "timestamp", "", "data", "", "isLast", "", "receivedTime", "(IJ[BZJ)V", "getData", "()[B", "()Z", "getReceivedTime", "()J", "getSequenceNumber", "()I", "getTimestamp", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "other", "hashCode", "toString", "", "app_debug"})
    public static final class FragmentInfo {
        private final int sequenceNumber = 0;
        private final long timestamp = 0L;
        @org.jetbrains.annotations.NotNull()
        private final byte[] data = null;
        private final boolean isLast = false;
        private final long receivedTime = 0L;
        
        public FragmentInfo(int sequenceNumber, long timestamp, @org.jetbrains.annotations.NotNull()
        byte[] data, boolean isLast, long receivedTime) {
            super();
        }
        
        public final int getSequenceNumber() {
            return 0;
        }
        
        public final long getTimestamp() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final byte[] getData() {
            return null;
        }
        
        public final boolean isLast() {
            return false;
        }
        
        public final long getReceivedTime() {
            return 0L;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        public final int component1() {
            return 0;
        }
        
        public final long component2() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final byte[] component3() {
            return null;
        }
        
        public final boolean component4() {
            return false;
        }
        
        public final long component5() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.audio.AudioRtpReceiver.FragmentInfo copy(int sequenceNumber, long timestamp, @org.jetbrains.annotations.NotNull()
        byte[] data, boolean isLast, long receivedTime) {
            return null;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}