package com.example.castapp.model;

/**
 * 远程连接数据模型
 * 用于管理远程控制发送端连接信息
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\t\n\u0002\b\u001e\b\u0086\b\u0018\u0000 (2\u00020\u0001:\u0001(BC\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\u0003\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u0012\b\b\u0002\u0010\n\u001a\u00020\u000b\u0012\b\b\u0002\u0010\f\u001a\u00020\u000b\u00a2\u0006\u0002\u0010\rJ\t\u0010\u0018\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\tH\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u000bH\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u000bH\u00c6\u0003JO\u0010\u001f\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u00032\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\u000bH\u00c6\u0001J\u0013\u0010 \u001a\u00020\t2\b\u0010!\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u0006\u0010\"\u001a\u00020\u0003J\u0006\u0010#\u001a\u00020\u0003J\t\u0010$\u001a\u00020\u0006H\u00d6\u0001J\t\u0010%\u001a\u00020\u0003H\u00d6\u0001J\u000e\u0010&\u001a\u00020\u00002\u0006\u0010\'\u001a\u00020\tR\u0011\u0010\f\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\u0007\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0011R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0011R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0014R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u000fR\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017\u00a8\u0006)"}, d2 = {"Lcom/example/castapp/model/RemoteSenderConnection;", "", "id", "", "ipAddress", "port", "", "deviceName", "isConnected", "", "lastConnectedTime", "", "createdTime", "(Ljava/lang/String;Ljava/lang/String;ILjava/lang/String;ZJJ)V", "getCreatedTime", "()J", "getDeviceName", "()Ljava/lang/String;", "getId", "getIpAddress", "()Z", "getLastConnectedTime", "getPort", "()I", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "equals", "other", "getDisplayText", "getStatusText", "hashCode", "toString", "withConnectionState", "connected", "Companion", "app_debug"})
public final class RemoteSenderConnection {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String id = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String ipAddress = null;
    private final int port = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String deviceName = null;
    private final boolean isConnected = false;
    private final long lastConnectedTime = 0L;
    private final long createdTime = 0L;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.model.RemoteSenderConnection.Companion Companion = null;
    
    public RemoteSenderConnection(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String ipAddress, int port, @org.jetbrains.annotations.NotNull()
    java.lang.String deviceName, boolean isConnected, long lastConnectedTime, long createdTime) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getIpAddress() {
        return null;
    }
    
    public final int getPort() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDeviceName() {
        return null;
    }
    
    public final boolean isConnected() {
        return false;
    }
    
    public final long getLastConnectedTime() {
        return 0L;
    }
    
    public final long getCreatedTime() {
        return 0L;
    }
    
    /**
     * 获取显示文本
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDisplayText() {
        return null;
    }
    
    /**
     * 获取连接状态文本
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getStatusText() {
        return null;
    }
    
    /**
     * 更新连接状态
     */
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.model.RemoteSenderConnection withConnectionState(boolean connected) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    public final int component3() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component4() {
        return null;
    }
    
    public final boolean component5() {
        return false;
    }
    
    public final long component6() {
        return 0L;
    }
    
    public final long component7() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.model.RemoteSenderConnection copy(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String ipAddress, int port, @org.jetbrains.annotations.NotNull()
    java.lang.String deviceName, boolean isConnected, long lastConnectedTime, long createdTime) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u0006J\u0006\u0010\n\u001a\u00020\u0006J\u000e\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u0006J\u000e\u0010\u000e\u001a\u00020\f2\u0006\u0010\u000f\u001a\u00020\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/example/castapp/model/RemoteSenderConnection$Companion;", "", "()V", "create", "Lcom/example/castapp/model/RemoteSenderConnection;", "ipAddress", "", "port", "", "deviceName", "generateId", "isValidDeviceName", "", "name", "isValidIpAddress", "ip", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * 生成连接ID
         */
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String generateId() {
            return null;
        }
        
        /**
         * 创建新的远程连接
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.model.RemoteSenderConnection create(@org.jetbrains.annotations.NotNull()
        java.lang.String ipAddress, int port, @org.jetbrains.annotations.NotNull()
        java.lang.String deviceName) {
            return null;
        }
        
        /**
         * 验证IP地址格式
         */
        public final boolean isValidIpAddress(@org.jetbrains.annotations.NotNull()
        java.lang.String ip) {
            return false;
        }
        
        /**
         * 验证设备名称
         */
        public final boolean isValidDeviceName(@org.jetbrains.annotations.NotNull()
        java.lang.String name) {
            return false;
        }
    }
}