package com.example.castapp.ui.windowsettings;

/**
 * 文本窗口管理器
 * 负责管理文本窗口的显示、编辑和格式化功能
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000n\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u0007\n\u0002\b\u000e\n\u0002\u0010$\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0017\u0018\u00002\u00020\u0001B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0010\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\bH\u0002J\u0010\u0010\u001a\u001a\u00020\u00182\u0006\u0010\u001b\u001a\u00020\u001cH\u0002J\u0010\u0010\u001d\u001a\u00020\u00182\u0006\u0010\u001e\u001a\u00020\bH\u0002J\u0018\u0010\u001f\u001a\u00020\u00182\u0006\u0010 \u001a\u00020\f2\u0006\u0010!\u001a\u00020\fH\u0002J\u0010\u0010\"\u001a\u00020\u00182\u0006\u0010#\u001a\u00020$H\u0002J\u0010\u0010%\u001a\u00020\u00182\u0006\u0010&\u001a\u00020$H\u0002J \u0010\'\u001a\u00020\u00182\u0006\u0010(\u001a\u00020\f2\u0006\u0010)\u001a\u00020$2\u0006\u0010*\u001a\u00020\bH\u0002J\u0010\u0010+\u001a\u00020\u00182\u0006\u0010,\u001a\u00020\bH\u0002J\u0018\u0010-\u001a\u00020\u00182\u0006\u0010(\u001a\u00020\f2\u0006\u0010\u0019\u001a\u00020\bH\u0002J\u0006\u0010.\u001a\u00020\u0018J\b\u0010/\u001a\u00020\u0018H\u0002J\b\u00100\u001a\u00020\u0018H\u0002J\b\u00101\u001a\u00020\u0018H\u0002J\u0014\u00102\u001a\u0010\u0012\u0004\u0012\u00020\n\u0012\u0006\u0012\u0004\u0018\u00010\u000103J\b\u00104\u001a\u00020$H\u0002J\u0006\u00105\u001a\u00020\nJ\n\u00106\u001a\u0004\u0018\u000107H\u0002J\b\u00108\u001a\u0004\u0018\u00010\u0016J\u0006\u00109\u001a\u00020\u0018J\u0006\u0010:\u001a\u00020\u0018JL\u0010;\u001a\u00020\u00182\b\u0010<\u001a\u0004\u0018\u00010\n2\u0006\u0010=\u001a\u00020\f2\u0006\u0010>\u001a\u00020\f2\u0006\u0010\u001e\u001a\u00020\b2\b\u0010?\u001a\u0004\u0018\u00010\n2\u0006\u0010&\u001a\u00020$2\u0006\u0010@\u001a\u00020\b2\b\u0010A\u001a\u0004\u0018\u00010\nJ\b\u0010B\u001a\u00020\u0018H\u0002J\"\u0010C\u001a\u00020\u00182\b\u0010?\u001a\u0004\u0018\u00010\n2\u0006\u0010&\u001a\u00020$2\u0006\u0010@\u001a\u00020\bH\u0002J\b\u0010D\u001a\u00020\u0018H\u0002J\u0010\u0010E\u001a\u00020\u00182\u0006\u0010&\u001a\u00020$H\u0002J\u0010\u0010F\u001a\u00020\u00182\u0006\u0010@\u001a\u00020\bH\u0002J\u0016\u0010G\u001a\u00020\u00182\u0006\u0010H\u001a\u00020\b2\u0006\u0010I\u001a\u00020\bJ\u0016\u0010J\u001a\u00020\u00182\u0006\u0010\u0012\u001a\u00020\n2\u0006\u0010<\u001a\u00020\nJ\u0006\u0010K\u001a\u00020\u0018J\b\u0010L\u001a\u00020\u0018H\u0002J\u0018\u0010M\u001a\u00020\u00182\u0006\u0010H\u001a\u00020\b2\u0006\u0010I\u001a\u00020\bH\u0002R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000e\u001a\u0004\u0018\u00010\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0015\u001a\u0004\u0018\u00010\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006N"}, d2 = {"Lcom/example/castapp/ui/windowsettings/TextWindowManager;", "", "context", "Landroid/content/Context;", "transformHandler", "Lcom/example/castapp/ui/windowsettings/TransformHandler;", "(Landroid/content/Context;Lcom/example/castapp/ui/windowsettings/TransformHandler;)V", "currentFontSize", "", "currentTextContent", "", "isBoldEnabled", "", "isItalicEnabled", "textEditPanel", "Lcom/example/castapp/ui/view/TextEditPanel;", "textFormatManager", "Lcom/example/castapp/utils/TextFormatManager;", "textId", "textSizeManager", "Lcom/example/castapp/utils/TextSizeManager;", "textWindowView", "Lcom/example/castapp/ui/view/TextWindowView;", "applyColorChangeRealTime", "", "color", "applyFontFamilyChangeRealTime", "fontItem", "Lcom/example/castapp/utils/FontPresetManager$FontItem;", "applyFontSizeChangeRealTime", "fontSize", "applyFormatChangesRealTime", "bold", "italic", "applyLetterSpacingChangeRealTime", "letterSpacing", "", "applyLineSpacingChangeRealTime", "lineSpacing", "applyStrokeChangeRealTime", "enabled", "strokeWidth", "strokeColor", "applyTextAlignmentChangeRealTime", "alignment", "applyWindowColorChangeRealTime", "cleanup", "clearSelectionFormat", "disableBorderResizing", "enableBorderResizing", "getCompleteTextFormatInfo", "", "getCurrentLineSpacing", "getCurrentTextContent", "getMainContainer", "Landroid/widget/RelativeLayout;", "getTextWindowView", "hideEditPanel", "refreshFormatDisplay", "restoreExtendedFormatState", "textContent", "isBold", "isItalic", "fontName", "textAlignment", "richTextData", "restoreFormatState", "saveExtendedFormatToPreferences", "saveFormatState", "saveLineSpacingToPreferences", "saveTextAlignmentToPreferences", "setTextWindowSize", "newWidth", "newHeight", "setupTextView", "showEditPanel", "syncFormatStateFromTextView", "updateTransformHandlerSize", "app_debug"})
public final class TextWindowManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.ui.windowsettings.TransformHandler transformHandler = null;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.view.TextWindowView textWindowView;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.view.TextEditPanel textEditPanel;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String textId = "";
    @org.jetbrains.annotations.NotNull()
    private java.lang.String currentTextContent = "\u9ed8\u8ba4\u6587\u5b57";
    private boolean isBoldEnabled = false;
    private boolean isItalicEnabled = false;
    private int currentFontSize = 13;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.utils.TextFormatManager textFormatManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.utils.TextSizeManager textSizeManager = null;
    
    public TextWindowManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.example.castapp.ui.windowsettings.TransformHandler transformHandler) {
        super();
    }
    
    /**
     * 设置文本视图
     */
    public final void setupTextView(@org.jetbrains.annotations.NotNull()
    java.lang.String textId, @org.jetbrains.annotations.NotNull()
    java.lang.String textContent) {
    }
    
    /**
     * 显示编辑面板（公共方法）
     */
    public final void showEditPanel() {
    }
    
    /**
     * 隐藏编辑面板（公共方法）
     */
    public final void hideEditPanel() {
    }
    
    /**
     * 实时应用格式更改到选中文字（不退出编辑模式）
     */
    private final void applyFormatChangesRealTime(boolean bold, boolean italic) {
    }
    
    /**
     * 实时应用字号更改到选中文字（不退出编辑模式）
     */
    private final void applyFontSizeChangeRealTime(int fontSize) {
    }
    
    /**
     * 实时应用字体更改到选中文字（不退出编辑模式）
     */
    private final void applyFontFamilyChangeRealTime(com.example.castapp.utils.FontPresetManager.FontItem fontItem) {
    }
    
    /**
     * 实时应用字间距更改到选中文字（不退出编辑模式）
     */
    private final void applyLetterSpacingChangeRealTime(float letterSpacing) {
    }
    
    /**
     * 实时应用行间距更改到选中文字（不退出编辑模式）
     */
    private final void applyLineSpacingChangeRealTime(float lineSpacing) {
    }
    
    /**
     * 实时应用对齐更改到选中文字（不退出编辑模式）
     */
    private final void applyTextAlignmentChangeRealTime(int alignment) {
    }
    
    /**
     * 实时应用颜色更改到选中文字（不退出编辑模式）
     */
    private final void applyColorChangeRealTime(int color) {
    }
    
    /**
     * 实时应用描边更改到选中文字（不退出编辑模式）
     */
    private final void applyStrokeChangeRealTime(boolean enabled, float strokeWidth, int strokeColor) {
    }
    
    /**
     * 实时应用窗口颜色更改（不退出编辑模式）
     */
    private final void applyWindowColorChangeRealTime(boolean enabled, int color) {
    }
    
    /**
     * 清除选中文字的格式
     */
    private final void clearSelectionFormat() {
    }
    
    /**
     * 获取主容器
     */
    private final android.widget.RelativeLayout getMainContainer() {
        return null;
    }
    
    /**
     * 保存格式状态
     */
    private final void saveFormatState() {
    }
    
    /**
     * 恢复格式状态
     */
    private final void restoreFormatState() {
    }
    
    /**
     * 🎯 从TextWindowView同步格式状态到管理器
     */
    private final void syncFormatStateFromTextView() {
    }
    
    /**
     * 🎨 获取文本窗口视图
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.ui.view.TextWindowView getTextWindowView() {
        return null;
    }
    
    /**
     * 📝 刷新格式显示（用于布局恢复后刷新文本格式）
     */
    public final void refreshFormatDisplay() {
    }
    
    /**
     * 更新TransformHandler的尺寸
     */
    private final void updateTransformHandlerSize(int newWidth, int newHeight) {
    }
    
    /**
     * 设置文本窗口尺寸（公共方法，用于布局恢复）
     */
    public final void setTextWindowSize(int newWidth, int newHeight) {
    }
    
    /**
     * 启用边框拖动调整大小功能
     */
    private final void enableBorderResizing() {
    }
    
    /**
     * 禁用边框拖动调整大小功能
     */
    private final void disableBorderResizing() {
    }
    
    /**
     * 📝 恢复扩展格式状态（从数据库布局恢复时使用）
     */
    public final void restoreExtendedFormatState(@org.jetbrains.annotations.Nullable()
    java.lang.String textContent, boolean isBold, boolean isItalic, int fontSize, @org.jetbrains.annotations.Nullable()
    java.lang.String fontName, float lineSpacing, int textAlignment, @org.jetbrains.annotations.Nullable()
    java.lang.String richTextData) {
    }
    
    /**
     * 📝 获取当前行间距
     */
    private final float getCurrentLineSpacing() {
        return 0.0F;
    }
    
    /**
     * 📝 保存行间距到SharedPreferences
     */
    private final void saveLineSpacingToPreferences(float lineSpacing) {
    }
    
    /**
     * 📝 保存文本对齐到SharedPreferences
     */
    private final void saveTextAlignmentToPreferences(int textAlignment) {
    }
    
    /**
     * 📝 保存扩展格式信息到SharedPreferences（用于布局恢复后的状态同步）
     */
    private final void saveExtendedFormatToPreferences(java.lang.String fontName, float lineSpacing, int textAlignment) {
    }
    
    /**
     * 清理资源
     */
    public final void cleanup() {
    }
    
    /**
     * 📝 获取当前文字内容
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCurrentTextContent() {
        return null;
    }
    
    /**
     * 📝 获取完整的文字格式信息（包含富文本格式）
     * @return 包含文字内容和格式信息的Map
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.Object> getCompleteTextFormatInfo() {
        return null;
    }
}