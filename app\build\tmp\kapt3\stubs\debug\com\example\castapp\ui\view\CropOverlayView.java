package com.example.castapp.ui.view;

/**
 * 简单的裁剪覆盖层视图
 * 提供基本的裁剪框和拖拽功能
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000`\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\b\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0018\n\u0002\u0018\u0002\n\u0002\b\u000e\u0018\u00002\u00020\u0001:\u0002JKB%\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u0010\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u001d\u001a\u00020\u0010H\u0002J\u0010\u0010\u001e\u001a\u00020\u001c2\u0006\u0010\u001f\u001a\u00020 H\u0002J0\u0010!\u001a\u00020\u001c2\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010\"\u001a\u00020\u00132\u0006\u0010#\u001a\u00020\u00132\u0006\u0010$\u001a\u00020%2\u0006\u0010&\u001a\u00020%H\u0002J0\u0010\'\u001a\u00020\u001c2\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010(\u001a\u00020\u00132\u0006\u0010)\u001a\u00020\u00132\u0006\u0010*\u001a\u00020\u00132\u0006\u0010+\u001a\u00020\u0013H\u0002J\u0010\u0010,\u001a\u00020\u001c2\u0006\u0010\u001f\u001a\u00020 H\u0002J\u0006\u0010-\u001a\u00020\u0010J\u0018\u0010.\u001a\u00020\n2\u0006\u0010\"\u001a\u00020\u00132\u0006\u0010#\u001a\u00020\u0013H\u0002J@\u0010/\u001a\u00020%2\u0006\u0010\"\u001a\u00020\u00132\u0006\u0010#\u001a\u00020\u00132\u0006\u00100\u001a\u00020\u00132\u0006\u00101\u001a\u00020\u00132\u0006\u0010$\u001a\u00020%2\u0006\u0010&\u001a\u00020%2\u0006\u00102\u001a\u00020\u0013H\u0002J \u00103\u001a\u00020%2\u0006\u00104\u001a\u00020\u00132\u0006\u00105\u001a\u00020\u00132\u0006\u00102\u001a\u00020\u0013H\u0002J\u0010\u00106\u001a\u00020\u001c2\u0006\u0010\u001f\u001a\u00020 H\u0014J(\u00107\u001a\u00020\u001c2\u0006\u00108\u001a\u00020\u00072\u0006\u00109\u001a\u00020\u00072\u0006\u0010:\u001a\u00020\u00072\u0006\u0010;\u001a\u00020\u0007H\u0014J\u0010\u0010<\u001a\u00020%2\u0006\u0010=\u001a\u00020>H\u0016J\b\u0010?\u001a\u00020%H\u0016J\u0010\u0010@\u001a\u00020\u001c2\b\b\u0002\u0010A\u001a\u00020%J\u0010\u0010B\u001a\u00020\u001c2\b\u0010C\u001a\u0004\u0018\u00010\u000eJ\u000e\u0010D\u001a\u00020\u001c2\u0006\u0010E\u001a\u00020\u0010J \u0010F\u001a\u00020\u001c2\u0006\u0010G\u001a\u00020\n2\u0006\u0010H\u001a\u00020\u00132\u0006\u0010I\u001a\u00020\u0013H\u0002R\u000e\u0010\t\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\r\u001a\u0004\u0018\u00010\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0013X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0013X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0013X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0013X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u0013X\u0082D\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001a\u001a\u0004\u0018\u00010\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006L"}, d2 = {"Lcom/example/castapp/ui/view/CropOverlayView;", "Landroid/view/View;", "context", "Landroid/content/Context;", "attrs", "Landroid/util/AttributeSet;", "defStyleAttr", "", "(Landroid/content/Context;Landroid/util/AttributeSet;I)V", "activeHandle", "Lcom/example/castapp/ui/view/CropOverlayView$HandleType;", "borderPaint", "Landroid/graphics/Paint;", "cropChangeListener", "Lcom/example/castapp/ui/view/CropOverlayView$CropChangeListener;", "cropRect", "Landroid/graphics/RectF;", "handleBorderPaint", "handleLength", "", "handlePaint", "handleSize", "lastTouchX", "lastTouchY", "maskPaint", "minCropSize", "pendingCropRatio", "constrainRect", "", "rect", "drawHandles", "canvas", "Landroid/graphics/Canvas;", "drawLShapeHandle", "x", "y", "isLeft", "", "isTop", "drawLineHandle", "startX", "startY", "endX", "endY", "drawMask", "getCropRectRatio", "getHandleAt", "isInLShapeArea", "cornerX", "cornerY", "tolerance", "isNear", "value1", "value2", "onDraw", "onSizeChanged", "w", "h", "oldw", "oldh", "onTouchEvent", "event", "Landroid/view/MotionEvent;", "performClick", "resetCrop", "notifyChange", "setCropChangeListener", "listener", "setCropRectRatio", "ratioRect", "updateCropRect", "handle", "deltaX", "deltaY", "CropChangeListener", "HandleType", "app_debug"})
public final class CropOverlayView extends android.view.View {
    @org.jetbrains.annotations.NotNull()
    private android.graphics.RectF cropRect;
    @org.jetbrains.annotations.Nullable()
    private android.graphics.RectF pendingCropRatio;
    @org.jetbrains.annotations.NotNull()
    private final android.graphics.Paint borderPaint = null;
    @org.jetbrains.annotations.NotNull()
    private final android.graphics.Paint handlePaint = null;
    @org.jetbrains.annotations.NotNull()
    private final android.graphics.Paint handleBorderPaint = null;
    @org.jetbrains.annotations.NotNull()
    private final android.graphics.Paint maskPaint = null;
    private final float handleSize = 80.0F;
    private final float handleLength = 50.0F;
    private final float minCropSize = 100.0F;
    @org.jetbrains.annotations.NotNull()
    private com.example.castapp.ui.view.CropOverlayView.HandleType activeHandle = com.example.castapp.ui.view.CropOverlayView.HandleType.NONE;
    private float lastTouchX = 0.0F;
    private float lastTouchY = 0.0F;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.view.CropOverlayView.CropChangeListener cropChangeListener;
    
    @kotlin.jvm.JvmOverloads()
    public CropOverlayView(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    android.util.AttributeSet attrs, int defStyleAttr) {
        super(null);
    }
    
    public final void setCropChangeListener(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.view.CropOverlayView.CropChangeListener listener) {
    }
    
    @java.lang.Override()
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
    }
    
    @java.lang.Override()
    protected void onDraw(@org.jetbrains.annotations.NotNull()
    android.graphics.Canvas canvas) {
    }
    
    private final void drawMask(android.graphics.Canvas canvas) {
    }
    
    private final void drawHandles(android.graphics.Canvas canvas) {
    }
    
    /**
     * 🎯 绘制L型手柄
     * @param canvas 画布
     * @param x 角落X坐标
     * @param y 角落Y坐标
     * @param isLeft 是否为左侧（true=左侧，false=右侧）
     * @param isTop 是否为顶部（true=顶部，false=底部）
     */
    private final void drawLShapeHandle(android.graphics.Canvas canvas, float x, float y, boolean isLeft, boolean isTop) {
    }
    
    /**
     * 🎯 绘制短线段手柄（边缘中心）
     */
    private final void drawLineHandle(android.graphics.Canvas canvas, float startX, float startY, float endX, float endY) {
    }
    
    @java.lang.Override()
    public boolean onTouchEvent(@org.jetbrains.annotations.NotNull()
    android.view.MotionEvent event) {
        return false;
    }
    
    @java.lang.Override()
    public boolean performClick() {
        return false;
    }
    
    private final com.example.castapp.ui.view.CropOverlayView.HandleType getHandleAt(float x, float y) {
        return null;
    }
    
    /**
     * 🎯 检查点是否在L型手柄区域内
     */
    private final boolean isInLShapeArea(float x, float y, float cornerX, float cornerY, boolean isLeft, boolean isTop, float tolerance) {
        return false;
    }
    
    private final boolean isNear(float value1, float value2, float tolerance) {
        return false;
    }
    
    private final void updateCropRect(com.example.castapp.ui.view.CropOverlayView.HandleType handle, float deltaX, float deltaY) {
    }
    
    private final void constrainRect(android.graphics.RectF rect) {
    }
    
    /**
     * 获取当前裁剪区域（相对于视图的比例）
     */
    @org.jetbrains.annotations.NotNull()
    public final android.graphics.RectF getCropRectRatio() {
        return null;
    }
    
    /**
     * 设置裁剪区域（使用相对比例）
     */
    public final void setCropRectRatio(@org.jetbrains.annotations.NotNull()
    android.graphics.RectF ratioRect) {
    }
    
    /**
     * 重置裁剪区域到全屏
     * @param notifyChange 是否通知裁剪变化，默认为true
     */
    public final void resetCrop(boolean notifyChange) {
    }
    
    @kotlin.jvm.JvmOverloads()
    public CropOverlayView(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super(null);
    }
    
    @kotlin.jvm.JvmOverloads()
    public CropOverlayView(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    android.util.AttributeSet attrs) {
        super(null);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&\u00a8\u0006\u0006"}, d2 = {"Lcom/example/castapp/ui/view/CropOverlayView$CropChangeListener;", "", "onCropChanged", "", "cropRect", "Landroid/graphics/RectF;", "app_debug"})
    public static abstract interface CropChangeListener {
        
        public abstract void onCropChanged(@org.jetbrains.annotations.NotNull()
        android.graphics.RectF cropRect);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\f\b\u0082\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\f\u00a8\u0006\r"}, d2 = {"Lcom/example/castapp/ui/view/CropOverlayView$HandleType;", "", "(Ljava/lang/String;I)V", "NONE", "TOP_LEFT", "TOP_RIGHT", "BOTTOM_LEFT", "BOTTOM_RIGHT", "TOP", "BOTTOM", "LEFT", "RIGHT", "CENTER", "app_debug"})
    static enum HandleType {
        /*public static final*/ NONE /* = new NONE() */,
        /*public static final*/ TOP_LEFT /* = new TOP_LEFT() */,
        /*public static final*/ TOP_RIGHT /* = new TOP_RIGHT() */,
        /*public static final*/ BOTTOM_LEFT /* = new BOTTOM_LEFT() */,
        /*public static final*/ BOTTOM_RIGHT /* = new BOTTOM_RIGHT() */,
        /*public static final*/ TOP /* = new TOP() */,
        /*public static final*/ BOTTOM /* = new BOTTOM() */,
        /*public static final*/ LEFT /* = new LEFT() */,
        /*public static final*/ RIGHT /* = new RIGHT() */,
        /*public static final*/ CENTER /* = new CENTER() */;
        
        HandleType() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.example.castapp.ui.view.CropOverlayView.HandleType> getEntries() {
            return null;
        }
    }
}