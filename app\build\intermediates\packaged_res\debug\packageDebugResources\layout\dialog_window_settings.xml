<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/bottom_sheet_background"
    android:paddingTop="4dp">

    <!-- 拖拽指示器 -->
    <View
        android:layout_width="40dp"
        android:layout_height="4dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="4dp"
        android:background="#CCCCCC"
        android:alpha="0.6" />

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/ic_window_settings"
            app:tint="#333333"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="10dp" />

        <TextView
            android:id="@+id/tv_window_settings_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="窗口设置"
            android:textColor="#333333"
            android:textSize="16sp"
            android:textStyle="bold"/>

        <TextView
            android:id="@+id/tv_window_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0个窗口"
            android:textSize="12sp"
            android:textColor="#666666"
            android:background="@drawable/count_badge_background"
            android:paddingHorizontal="12dp"
            android:paddingVertical="4dp"
            android:layout_marginEnd="12dp" />

        <ImageView
            android:id="@+id/btn_close"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_close"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="6dp"
            app:tint="#666666"
            android:layout_marginEnd="6dp"
            android:contentDescription="关闭窗口管理" />

    </LinearLayout>

    <!-- 🔄 实时同步控制区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="start|center_vertical"
        android:layout_marginBottom="2dp">

        <!-- 实时同步开关 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="实时同步"
            android:textSize="13sp"
            android:textColor="#666666"
            android:layout_marginStart="12dp"/>

        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/switch_sync_control"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="false"
            android:layout_marginStart="-1dp"
            android:scaleX="0.9"
            android:scaleY="0.9"
            android:theme="@style/SwitchTheme" />

        <!-- 获取接收端设置开关 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="获取接收端设置"
            android:textSize="13sp"
            android:textColor="#666666"
            android:layout_marginStart="16dp"/>

        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/switch_fetch_receiver_settings"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true"
            android:layout_marginStart="-1dp"
            android:scaleX="0.9"
            android:scaleY="0.9"
            android:theme="@style/SwitchTheme" />

    </LinearLayout>

    <!-- 分割线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#E0E0E0"
        android:layout_marginHorizontal="10dp"
        android:layout_marginBottom="6dp" />

    <!-- 窗口列表容器 -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="8dp"
        android:layout_marginBottom="20dp">

        <!-- 窗口列表 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_cast_windows"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxHeight="400dp"
            android:nestedScrollingEnabled="true" />

        <!-- 空状态提示 -->
        <LinearLayout
            android:id="@+id/layout_empty_state"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="40dp"
            android:visibility="gone">

            <ImageView
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:src="@drawable/ic_director"
                app:tint="#CCCCCC"
                android:layout_marginBottom="20dp"
                android:alpha="0.6" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="暂无活跃的投屏窗口"
                android:textSize="18sp"
                android:textColor="#999999"
                android:layout_marginBottom="8dp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="开始投屏后，窗口信息将在这里显示"
                android:textSize="14sp"
                android:textColor="#CCCCCC"
                android:gravity="center"
                android:lineSpacingExtra="2dp" />

        </LinearLayout>

    </FrameLayout>

</LinearLayout>
