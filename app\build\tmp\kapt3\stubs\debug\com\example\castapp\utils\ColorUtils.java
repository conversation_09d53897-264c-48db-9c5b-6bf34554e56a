package com.example.castapp.utils;

/**
 * 🎨 颜色工具类
 * 提供颜色格式转换、验证和处理功能
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0007\n\u0002\u0010\u0007\n\u0000\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006J\u000e\u0010\u0007\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006J-\u0010\b\u001a\u0004\u0018\u00010\u00062\u0006\u0010\t\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\u00062\u0006\u0010\f\u001a\u00020\u0006\u00a2\u0006\u0002\u0010\rJ\u000e\u0010\u000e\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0006J\u000e\u0010\u000f\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0006J\u000e\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0005\u001a\u00020\u0006J\u000e\u0010\u0012\u001a\u00020\u00112\u0006\u0010\u0013\u001a\u00020\u0006J\u0015\u0010\u0014\u001a\u0004\u0018\u00010\u00062\u0006\u0010\u0015\u001a\u00020\u0004\u00a2\u0006\u0002\u0010\u0016J\u0016\u0010\u0017\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0018\u001a\u00020\u0019\u00a8\u0006\u001a"}, d2 = {"Lcom/example/castapp/utils/ColorUtils;", "", "()V", "colorToArgbHex", "", "color", "", "colorToRgbString", "createArgbColor", "a", "r", "g", "b", "(IIII)Ljava/lang/Integer;", "getAlphaPercentage", "getContrastColor", "isDarkColor", "", "isValidRgbComponent", "component", "parseHexColor", "hexString", "(Ljava/lang/String;)Ljava/lang/Integer;", "setColorAlpha", "alpha", "", "app_debug"})
public final class ColorUtils {
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.utils.ColorUtils INSTANCE = null;
    
    private ColorUtils() {
        super();
    }
    
    /**
     * 🎨 将颜色转换为ARGB HEX字符串
     * @param color ARGB颜色值
     * @return HEX字符串，格式：#AARRGGBB
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String colorToArgbHex(int color) {
        return null;
    }
    
    /**
     * 🎨 解析HEX字符串为颜色值
     * @param hexString HEX字符串，支持 #RGB, #RRGGBB, #AARRGGBB 格式
     * @return 颜色值，解析失败返回null
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer parseHexColor(@org.jetbrains.annotations.NotNull()
    java.lang.String hexString) {
        return null;
    }
    
    /**
     * 🎨 从ARGB值创建颜色
     * @param a 透明度分量 (0-255)
     * @param r 红色分量 (0-255)
     * @param g 绿色分量 (0-255)
     * @param b 蓝色分量 (0-255)
     * @return 颜色值，输入无效返回null
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer createArgbColor(int a, int r, int g, int b) {
        return null;
    }
    
    /**
     * 🎨 设置颜色的透明度
     * @param color 原始颜色
     * @param alpha 透明度 (0.0f-1.0f)
     * @return 新的颜色值
     */
    public final int setColorAlpha(int color, float alpha) {
        return 0;
    }
    
    /**
     * 🎨 获取颜色的透明度百分比
     * @param color 颜色值
     * @return 透明度百分比 (0-100)
     */
    public final int getAlphaPercentage(int color) {
        return 0;
    }
    
    /**
     * 🎨 验证RGB分量是否有效
     * @param component RGB分量值
     * @return 是否有效 (0-255)
     */
    public final boolean isValidRgbComponent(int component) {
        return false;
    }
    
    /**
     * 🎨 获取颜色的RGB字符串表示
     * @param color 颜色值
     * @return RGB字符串，格式：RGB(R, G, B)
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String colorToRgbString(int color) {
        return null;
    }
    
    /**
     * 🎨 判断颜色是否为深色
     * @param color 颜色值
     * @return 是否为深色
     */
    public final boolean isDarkColor(int color) {
        return false;
    }
    
    /**
     * 🎨 获取对比色（黑色或白色）
     * @param color 背景颜色
     * @return 对比色（黑色或白色）
     */
    public final int getContrastColor(int color) {
        return 0;
    }
}