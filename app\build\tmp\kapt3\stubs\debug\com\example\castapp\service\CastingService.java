package com.example.castapp.service;

/**
 * 投屏服务
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u008a\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0011\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b \u0018\u0000 `2\u00020\u0001:\u0001`B\u0005\u00a2\u0006\u0002\u0010\u0002J$\u0010\u001e\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00160\u001f2\u0006\u0010 \u001a\u00020\u00162\u0006\u0010!\u001a\u00020\u0016H\u0002J\b\u0010\"\u001a\u00020#H\u0002J\b\u0010$\u001a\u00020#H\u0002J\b\u0010%\u001a\u00020#H\u0002J\b\u0010&\u001a\u00020#H\u0002J\u0010\u0010\'\u001a\u00020\b2\u0006\u0010 \u001a\u00020\u0016H\u0002J\b\u0010(\u001a\u00020#H\u0002J\u0018\u0010)\u001a\u00020#2\u0006\u0010*\u001a\u00020\b2\u0006\u0010+\u001a\u00020\u0004H\u0002J\u0010\u0010,\u001a\u00020#2\u0006\u0010-\u001a\u00020\u0016H\u0002J2\u0010.\u001a\u00020#2\u0006\u0010*\u001a\u00020\b2\u0006\u0010/\u001a\u00020\u00162\u0006\u00100\u001a\u00020\u00162\u0006\u00101\u001a\u00020\u00042\b\u00102\u001a\u0004\u0018\u00010\bH\u0002J\u0018\u00103\u001a\u00020#2\u0006\u0010*\u001a\u00020\b2\u0006\u00104\u001a\u000205H\u0002J\b\u00106\u001a\u00020\u0004H\u0002J\b\u00107\u001a\u00020#H\u0002J\u0018\u00108\u001a\u00020#2\u0006\u00109\u001a\u00020\u00162\u0006\u0010:\u001a\u00020\u0016H\u0002J\u0014\u0010;\u001a\u0004\u0018\u00010<2\b\u0010=\u001a\u0004\u0018\u00010>H\u0016J\u0010\u0010?\u001a\u00020#2\u0006\u0010@\u001a\u00020AH\u0016J\b\u0010B\u001a\u00020#H\u0016J\b\u0010C\u001a\u00020#H\u0016J\"\u0010D\u001a\u00020\u00162\b\u0010=\u001a\u0004\u0018\u00010>2\u0006\u0010E\u001a\u00020\u00162\u0006\u0010F\u001a\u00020\u0016H\u0016J\u0010\u0010G\u001a\u00020\u00042\u0006\u0010H\u001a\u00020\u0004H\u0002J\u0018\u0010I\u001a\u00020\u00042\u0006\u00109\u001a\u00020\u00162\u0006\u0010:\u001a\u00020\u0016H\u0002J\u0010\u0010J\u001a\u00020#2\u0006\u0010*\u001a\u00020\bH\u0002J \u0010K\u001a\u00020#2\u0006\u0010*\u001a\u00020\b2\u0006\u0010L\u001a\u00020\u001b2\u0006\u0010M\u001a\u00020\u0016H\u0002J\b\u0010N\u001a\u00020#H\u0002J\u0010\u0010O\u001a\u00020#2\u0006\u0010*\u001a\u00020\bH\u0002J\u0010\u0010P\u001a\u00020#2\u0006\u0010*\u001a\u00020\bH\u0002J\u0010\u0010Q\u001a\u00020#2\u0006\u0010*\u001a\u00020\bH\u0002J(\u0010R\u001a\u00020#2\u0006\u0010S\u001a\u00020\b2\u0006\u0010T\u001a\u00020\u00162\u0006\u0010*\u001a\u00020\b2\u0006\u0010U\u001a\u00020>H\u0002J\u0010\u0010V\u001a\u00020#2\u0006\u0010U\u001a\u00020>H\u0002J\u0014\u0010W\u001a\u00020#2\n\b\u0002\u0010*\u001a\u0004\u0018\u00010\bH\u0002J\b\u0010X\u001a\u00020#H\u0002J\b\u0010Y\u001a\u00020#H\u0002J\u0010\u0010Y\u001a\u00020#2\u0006\u0010Z\u001a\u00020\u0016H\u0002J\u0010\u0010[\u001a\u00020#2\u0006\u0010\\\u001a\u00020\u0016H\u0002J\b\u0010]\u001a\u00020#H\u0002J\b\u0010^\u001a\u00020#H\u0002J\u001a\u0010_\u001a\u00020#2\u0006\u0010!\u001a\u00020\u00162\b\b\u0002\u0010M\u001a\u00020\u0016H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00040\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\t\u001a\u0004\u0018\u00010\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000b\u001a\u0004\u0018\u00010\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\r\u001a\u0004\u0018\u00010\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00120\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0017\u001a\u0004\u0018\u00010\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u001a\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u001b0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\u001dX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006a"}, d2 = {"Lcom/example/castapp/service/CastingService;", "Landroid/app/Service;", "()V", "isResolutionAdjusting", "", "isVideoSourceRunning", "landscapeModeEnabled", "Ljava/util/concurrent/ConcurrentHashMap;", "", "mediaProjectionManager", "Lcom/example/castapp/manager/MediaProjectionManager;", "memoryMonitor", "Lcom/example/castapp/utils/MemoryMonitor;", "periodicCleanupTask", "Lcom/example/castapp/utils/PeriodicCleanupTask;", "resolutionAdjustLock", "", "rtpSenders", "Lcom/example/castapp/rtp/RtpSender;", "stateManager", "Lcom/example/castapp/manager/StateManager;", "systemOrientation", "", "videoEncoder", "Lcom/example/castapp/codec/VideoEncoder;", "videoSourceOrientation", "webSocketClients", "Lcom/example/castapp/websocket/WebSocketClient;", "webSocketManager", "Lcom/example/castapp/manager/WebSocketManager;", "calculateOrientationAwareResolution", "Lkotlin/Pair;", "orientation", "scalePercent", "cleanupDisconnectedConnections", "", "deepCleanupConnections", "forceGarbageCollection", "forcePortraitMode", "getOrientationName", "handleAudioIntent", "handleLandscapeModeControl", "connectionId", "enabled", "handleOrientationChange", "newOrientation", "handleResolutionAdjustmentComplete", "width", "height", "success", "error", "handleWebSocketMessage", "controlMessage", "Lcom/example/castapp/websocket/ControlMessage;", "hasLandscapeModeEnabled", "initCurrentOrientation", "notifyReceiversResolutionChange", "newWidth", "newHeight", "onBind", "Landroid/os/IBinder;", "intent", "Landroid/content/Intent;", "onConfigurationChanged", "newConfig", "Landroid/content/res/Configuration;", "onCreate", "onDestroy", "onStartCommand", "flags", "startId", "performOrientationDetection", "forceProcess", "restartVideoSourceWithNewResolution", "sendBasicConnectionInfo", "sendH264ConfigWithRetry", "webSocketClient", "retryCount", "sendInitializationDataToAllConnections", "sendInitializationDataToConnection", "sendScreenResolutionToConnectionViaWebSocket", "sendSensitiveDataToConnectionInternal", "startCasting", "targetIp", "targetPort", "resultData", "startVideoSource", "stopCasting", "stopVideoSource", "triggerOrientationDetectionWithRetry", "maxRetries", "updateBitRate", "newBitRate", "updateCurrentOrientationState", "updateNotification", "updateResolution", "Companion", "app_debug"})
public final class CastingService extends android.app.Service {
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_START_CASTING = "action_start_casting";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_STOP_CASTING = "action_stop_casting";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_UPDATE_BITRATE = "action_update_bitrate";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_UPDATE_RESOLUTION = "action_update_resolution";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_TARGET_IP = "target_ip";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_STOP_DISPLAY_AUDIO = "STOP_DISPLAY_AUDIO";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_TARGET_PORT = "target_port";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_CONNECTION_ID = "connection_id";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_RESULT_CODE = "result_code";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_RESULT_DATA = "result_data";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_BITRATE = "bitrate";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_RESOLUTION_SCALE = "resolution_scale";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_RETRY_COUNT = "retry_count";
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.manager.MediaProjectionManager mediaProjectionManager;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.codec.VideoEncoder videoEncoder;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, com.example.castapp.rtp.RtpSender> rtpSenders = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, com.example.castapp.websocket.WebSocketClient> webSocketClients = null;
    private boolean isVideoSourceRunning = false;
    private boolean isResolutionAdjusting = false;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.Object resolutionAdjustLock = null;
    private com.example.castapp.manager.StateManager stateManager;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.utils.MemoryMonitor memoryMonitor;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.utils.PeriodicCleanupTask periodicCleanupTask;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.WebSocketManager webSocketManager = null;
    private int systemOrientation = android.content.res.Configuration.ORIENTATION_PORTRAIT;
    private int videoSourceOrientation = android.content.res.Configuration.ORIENTATION_PORTRAIT;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, java.lang.Boolean> landscapeModeEnabled = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.service.CastingService.Companion Companion = null;
    
    public CastingService() {
        super();
    }
    
    @java.lang.Override()
    public void onCreate() {
    }
    
    @java.lang.Override()
    public int onStartCommand(@org.jetbrains.annotations.Nullable()
    android.content.Intent intent, int flags, int startId) {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.os.IBinder onBind(@org.jetbrains.annotations.Nullable()
    android.content.Intent intent) {
        return null;
    }
    
    /**
     * 🎯 横竖屏适配：监听配置变化（根源性修复版）
     */
    @java.lang.Override()
    public void onConfigurationChanged(@org.jetbrains.annotations.NotNull()
    android.content.res.Configuration newConfig) {
    }
    
    private final void handleAudioIntent() {
    }
    
    /**
     * 开始投屏
     */
    private final void startCasting(java.lang.String targetIp, int targetPort, java.lang.String connectionId, android.content.Intent resultData) {
    }
    
    private final void startVideoSource(android.content.Intent resultData) {
    }
    
    private final void stopVideoSource() {
    }
    
    private final void sendBasicConnectionInfo(java.lang.String connectionId) {
    }
    
    private final void sendInitializationDataToAllConnections() {
    }
    
    private final void sendInitializationDataToConnection(java.lang.String connectionId) {
    }
    
    private final void sendSensitiveDataToConnectionInternal(java.lang.String connectionId) {
    }
    
    private final void sendH264ConfigWithRetry(java.lang.String connectionId, com.example.castapp.websocket.WebSocketClient webSocketClient, int retryCount) {
    }
    
    private final void sendScreenResolutionToConnectionViaWebSocket(java.lang.String connectionId) {
    }
    
    private final void stopCasting(java.lang.String connectionId) {
    }
    
    private final void updateNotification() {
    }
    
    private final void handleWebSocketMessage(java.lang.String connectionId, com.example.castapp.websocket.ControlMessage controlMessage) {
    }
    
    private final void handleResolutionAdjustmentComplete(java.lang.String connectionId, int width, @kotlin.Suppress(names = {"UNUSED_PARAMETER"})
    int height, boolean success, java.lang.String error) {
    }
    
    private final void updateBitRate(int newBitRate) {
    }
    
    private final void updateResolution(int scalePercent, int retryCount) {
    }
    
    private final boolean restartVideoSourceWithNewResolution(int newWidth, int newHeight) {
        return false;
    }
    
    private final void notifyReceiversResolutionChange(int newWidth, int newHeight) {
    }
    
    private final void cleanupDisconnectedConnections() {
    }
    
    private final void deepCleanupConnections() {
    }
    
    private final void forceGarbageCollection() {
    }
    
    @java.lang.Override()
    public void onDestroy() {
    }
    
    /**
     * 🎯 横竖屏适配：初始化方向状态（根源性修复版）
     */
    private final void initCurrentOrientation() {
    }
    
    /**
     * 处理屏幕方向变化
     */
    private final void handleOrientationChange(int newOrientation) {
    }
    
    /**
     * 🎯 横竖屏适配：更新方向状态（在投屏开始时调用，根源性修复版）
     */
    private final void updateCurrentOrientationState() {
    }
    
    /**
     * 根据方向计算分辨率
     */
    private final kotlin.Pair<java.lang.Integer, java.lang.Integer> calculateOrientationAwareResolution(int orientation, int scalePercent) {
        return null;
    }
    
    /**
     * 获取方向名称（用于日志）
     */
    private final java.lang.String getOrientationName(int orientation) {
        return null;
    }
    
    /**
     * 处理横屏模式控制消息
     * 🎯 优化：开启时立即检测，关闭时强制竖屏
     */
    private final void handleLandscapeModeControl(java.lang.String connectionId, boolean enabled) {
    }
    
    /**
     * 检查是否有连接启用了横屏模式
     */
    private final boolean hasLandscapeModeEnabled() {
        return false;
    }
    
    /**
     * 🎯 带重试机制的方向检测（最终修复版）
     * 解决横屏开关时序问题：强制同步当前系统方向
     */
    private final void triggerOrientationDetectionWithRetry() {
    }
    
    /**
     * 🎯 带重试机制的方向检测（内部实现，支持重试次数限制）
     */
    private final void triggerOrientationDetectionWithRetry(int maxRetries) {
    }
    
    /**
     * 🎯 执行方向检测的核心逻辑（时序修复版）
     * @param forceProcess 是否强制处理，即使方向相同也要重新应用设置
     * @return 是否执行了方向处理
     */
    private final boolean performOrientationDetection(boolean forceProcess) {
        return false;
    }
    
    /**
     * 🎯 强制切换到竖屏模式（根源性修复版）
     * 当横屏模式关闭时，无论当前实际方向如何，都强制切换到竖屏模式
     */
    private final void forcePortraitMode() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\r\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0011"}, d2 = {"Lcom/example/castapp/service/CastingService$Companion;", "", "()V", "ACTION_START_CASTING", "", "ACTION_STOP_CASTING", "ACTION_STOP_DISPLAY_AUDIO", "ACTION_UPDATE_BITRATE", "ACTION_UPDATE_RESOLUTION", "EXTRA_BITRATE", "EXTRA_CONNECTION_ID", "EXTRA_RESOLUTION_SCALE", "EXTRA_RESULT_CODE", "EXTRA_RESULT_DATA", "EXTRA_RETRY_COUNT", "EXTRA_TARGET_IP", "EXTRA_TARGET_PORT", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}