package com.example.castapp.manager;

/**
 * 统一的MediaProjection管理器
 * 整合了原有的UnifiedMediaProjectionManager、MediaProjectionHelper和MediaAudioProjectionHelper
 * 解决Android系统同时只能有一个MediaProjection实例的限制
 * 实现投屏和媒体音频功能的权限复用
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0084\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\u0012\u0018\u0000 O2\u00020\u0001:\u0003OPQB\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\b\u0010\u001e\u001a\u00020\u001fH\u0002J\b\u0010 \u001a\u00020\u001fH\u0002J\b\u0010!\u001a\u00020\u001fH\u0002J\b\u0010\"\u001a\u00020\u001fH\u0002J\u0006\u0010#\u001a\u00020\u001fJ \u0010$\u001a\u00020\f2\u0006\u0010%\u001a\u00020\u00062\u0006\u0010&\u001a\u00020\b2\u0006\u0010\'\u001a\u00020\u000bH\u0002J\u000e\u0010(\u001a\u00020\f2\u0006\u0010\'\u001a\u00020\u000bJ \u0010)\u001a\u00020\f2\u0006\u0010%\u001a\u00020\u00062\u0006\u0010&\u001a\u00020\b2\u0006\u0010\'\u001a\u00020\u000bH\u0002J\u0014\u0010*\u001a\u0010\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\b\u0018\u00010+J\b\u0010,\u001a\u0004\u0018\u00010\u0010J\b\u0010-\u001a\u00020.H\u0002J\u0006\u0010/\u001a\u00020\fJ\b\u00100\u001a\u00020\fH\u0002J\u0010\u00101\u001a\u00020\u001f2\u0006\u0010\u000f\u001a\u00020\u0010H\u0002J\b\u00102\u001a\u00020\u001fH\u0002J \u00103\u001a\u00020\f2\u0006\u00104\u001a\u0002052\u0006\u00106\u001a\u00020\u00062\u0006\u00107\u001a\u00020\u0006H\u0002J(\u00108\u001a\u00020\f2\u0006\u00109\u001a\u00020\u00102\u0006\u00104\u001a\u0002052\u0006\u00106\u001a\u00020\u00062\u0006\u00107\u001a\u00020\u0006H\u0002J\u0010\u0010:\u001a\u00020\u001f2\u0006\u0010\'\u001a\u00020\u000bH\u0002J\u0006\u0010;\u001a\u00020\u001fJ\u001e\u0010<\u001a\u00020\f2\u0006\u00104\u001a\u0002052\u0006\u00106\u001a\u00020\u00062\u0006\u00107\u001a\u00020\u0006J\u001f\u0010=\u001a\u00020\u001f2\u0006\u0010>\u001a\u00020\u000b2\f\u0010?\u001a\b\u0012\u0004\u0012\u00020\u001f0@H\u0082\bJ(\u0010A\u001a\u00020\u001f2\b\u0010B\u001a\u0004\u0018\u00010\u00012\u0006\u0010C\u001a\u00020\u000b2\f\u0010D\u001a\b\u0012\u0004\u0012\u00020\u001f0@H\u0002J\u0010\u0010E\u001a\u00020\f2\b\u0010F\u001a\u0004\u0018\u00010\bJ \u0010G\u001a\u00020\f2\u0006\u0010%\u001a\u00020\u00062\b\u0010&\u001a\u0004\u0018\u00010\b2\u0006\u0010\'\u001a\u00020\u000bJ\u0018\u0010H\u001a\u00020\f2\b\u0010F\u001a\u0004\u0018\u00010\b2\u0006\u00104\u001a\u000205J\u0006\u0010I\u001a\u00020\u001fJ\u0006\u0010J\u001a\u00020\u001fJ\u0006\u0010K\u001a\u00020\u001fJ\'\u0010L\u001a\u0004\u0018\u00010\f2\u0006\u0010%\u001a\u00020\u00062\u0006\u0010&\u001a\u00020\b2\u0006\u0010\'\u001a\u00020\u000bH\u0002\u00a2\u0006\u0002\u0010MJ\u000e\u0010N\u001a\u00020\u001f2\u0006\u0010\'\u001a\u00020\u000bR\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0007\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\f0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000f\u001a\u0004\u0018\u00010\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0011\u001a\u0004\u0018\u00010\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0013\u001a\u0004\u0018\u00010\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000Rf\u0010\u0015\u001aZ\u0012\u0018\u0012\u0016\u0012\u0004\u0012\u00020\u0018 \u0019*\n\u0012\u0004\u0012\u00020\u0018\u0018\u00010\u00170\u0017\u0012\f\u0012\n \u0019*\u0004\u0018\u00010\f0\f \u0019*,\u0012\u0018\u0012\u0016\u0012\u0004\u0012\u00020\u0018 \u0019*\n\u0012\u0004\u0012\u00020\u0018\u0018\u00010\u00170\u0017\u0012\f\u0012\n \u0019*\u0004\u0018\u00010\f0\f\u0018\u00010\u00160\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u001bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001c\u001a\u0004\u0018\u00010\u001dX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006R"}, d2 = {"Lcom/example/castapp/manager/MediaProjectionManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "cachedResultCode", "", "cachedResultData", "Landroid/content/Intent;", "featureUsage", "Ljava/util/concurrent/ConcurrentHashMap;", "", "", "isAndroid14PlusDevice", "isPermissionGranted", "mediaProjection", "Landroid/media/projection/MediaProjection;", "mediaProjectionCallback", "Landroid/media/projection/MediaProjection$Callback;", "mediaProjectionManager", "Landroid/media/projection/MediaProjectionManager;", "stateListeners", "Ljava/util/concurrent/ConcurrentHashMap$KeySetView;", "Ljava/lang/ref/WeakReference;", "Lcom/example/castapp/manager/MediaProjectionManager$MediaProjectionStateListener;", "kotlin.jvm.PlatformType", "usageCounter", "Ljava/util/concurrent/atomic/AtomicInteger;", "virtualDisplay", "Landroid/hardware/display/VirtualDisplay;", "cleanupExistingProjection", "", "cleanupForAndroid14ScreenCasting", "cleanupInvalidMediaProjectionInstance", "cleanupListeners", "clearCachedPermission", "createMediaProjection", "resultCode", "resultData", "featureType", "createMediaProjectionInForegroundService", "createNewMediaProjection", "getCachedPermissionData", "Lkotlin/Pair;", "getMediaProjection", "getScreenMetrics", "Landroid/util/DisplayMetrics;", "hasValidPermission", "isMediaProjectionValid", "notifyMediaProjectionCreated", "notifyMediaProjectionStopped", "recreateEntireMediaProjection", "surface", "Landroid/view/Surface;", "newWidth", "newHeight", "recreateVirtualDisplayWithNewResolution", "projection", "registerFeatureUsage", "release", "restartScreenCaptureWithNewResolution", "safeExecute", "operation", "block", "Lkotlin/Function0;", "safeRelease", "resource", "resourceName", "releaseAction", "startMediaAudioProjection", "data", "startMediaProjectionWithPermission", "startScreenCapture", "stopMediaAudioProjectionKeepPermission", "stopMediaProjection", "stopScreenCapture", "tryReuseExistingProjection", "(ILandroid/content/Intent;Ljava/lang/String;)Ljava/lang/Boolean;", "unregisterFeatureUsage", "Companion", "MediaProjectionCallbackImpl", "MediaProjectionStateListener", "app_debug"})
public final class MediaProjectionManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String FEATURE_SCREEN_CASTING = "SCREEN_CASTING";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String FEATURE_MEDIA_AUDIO = "MEDIA_AUDIO";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String FEATURE_APP_STARTUP_CACHE = "APP_STARTUP_CACHE";
    @kotlin.jvm.Volatile()
    @android.annotation.SuppressLint(value = {"StaticFieldLeak"})
    @org.jetbrains.annotations.Nullable()
    private static volatile com.example.castapp.manager.MediaProjectionManager INSTANCE;
    @org.jetbrains.annotations.Nullable()
    private android.media.projection.MediaProjectionManager mediaProjectionManager;
    @org.jetbrains.annotations.Nullable()
    private android.media.projection.MediaProjection mediaProjection;
    @org.jetbrains.annotations.Nullable()
    private android.media.projection.MediaProjection.Callback mediaProjectionCallback;
    private boolean isPermissionGranted = false;
    private int cachedResultCode = 0;
    @org.jetbrains.annotations.Nullable()
    private android.content.Intent cachedResultData;
    private final boolean isAndroid14PlusDevice = false;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.atomic.AtomicInteger usageCounter = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, java.lang.Boolean> featureUsage = null;
    private final java.util.concurrent.ConcurrentHashMap.KeySetView<java.lang.ref.WeakReference<com.example.castapp.manager.MediaProjectionManager.MediaProjectionStateListener>, java.lang.Boolean> stateListeners = null;
    @org.jetbrains.annotations.Nullable()
    private android.hardware.display.VirtualDisplay virtualDisplay;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.manager.MediaProjectionManager.Companion Companion = null;
    
    private MediaProjectionManager(android.content.Context context) {
        super();
    }
    
    /**
     * 安全执行代码块，统一异常处理
     */
    private final void safeExecute(java.lang.String operation, kotlin.jvm.functions.Function0<kotlin.Unit> block) {
    }
    
    /**
     * 安全释放资源
     */
    private final void safeRelease(java.lang.Object resource, java.lang.String resourceName, kotlin.jvm.functions.Function0<kotlin.Unit> releaseAction) {
    }
    
    /**
     * 获取MediaProjection实例
     */
    @org.jetbrains.annotations.Nullable()
    public final android.media.projection.MediaProjection getMediaProjection() {
        return null;
    }
    
    /**
     * 检查是否有有效的MediaProjection权限
     * 现在只检查权限缓存是否存在，不检查MediaProjection实例
     * 因为实例可能被清理但权限缓存仍然有效
     */
    public final boolean hasValidPermission() {
        return false;
    }
    
    /**
     * 验证MediaProjection实例是否仍然有效
     */
    private final boolean isMediaProjectionValid() {
        return false;
    }
    
    /**
     * 获取缓存的权限数据
     */
    @org.jetbrains.annotations.Nullable()
    public final kotlin.Pair<java.lang.Integer, android.content.Intent> getCachedPermissionData() {
        return null;
    }
    
    /**
     * 使用权限结果数据启动MediaProjection
     */
    public final boolean startMediaProjectionWithPermission(int resultCode, @org.jetbrains.annotations.Nullable()
    android.content.Intent resultData, @org.jetbrains.annotations.NotNull()
    java.lang.String featureType) {
        return false;
    }
    
    /**
     * 在前台服务中创建MediaProjection实例
     * 这是Android 14+的要求
     */
    public final boolean createMediaProjectionInForegroundService(@org.jetbrains.annotations.NotNull()
    java.lang.String featureType) {
        return false;
    }
    
    /**
     * 创建MediaProjection实例
     */
    private final boolean createMediaProjection(int resultCode, android.content.Intent resultData, java.lang.String featureType) {
        return false;
    }
    
    private final void cleanupForAndroid14ScreenCasting() {
    }
    
    private final java.lang.Boolean tryReuseExistingProjection(int resultCode, android.content.Intent resultData, java.lang.String featureType) {
        return null;
    }
    
    private final void cleanupExistingProjection() {
    }
    
    private final boolean createNewMediaProjection(int resultCode, android.content.Intent resultData, java.lang.String featureType) {
        return false;
    }
    
    /**
     * 注册功能使用
     */
    private final void registerFeatureUsage(java.lang.String featureType) {
    }
    
    /**
     * 取消注册功能使用
     */
    public final void unregisterFeatureUsage(@org.jetbrains.annotations.NotNull()
    java.lang.String featureType) {
    }
    
    /**
     * 停止MediaProjection
     */
    public final void stopMediaProjection() {
    }
    
    /**
     * 清理缓存的权限数据
     */
    public final void clearCachedPermission() {
    }
    
    /**
     * 清理无效的MediaProjection实例，保留权限缓存
     */
    private final void cleanupInvalidMediaProjectionInstance() {
    }
    
    /**
     * 启动屏幕捕获
     */
    public final boolean startScreenCapture(@org.jetbrains.annotations.Nullable()
    android.content.Intent data, @org.jetbrains.annotations.NotNull()
    android.view.Surface surface) {
        return false;
    }
    
    /**
     * 停止屏幕捕获
     */
    public final void stopScreenCapture() {
    }
    
    /**
     * 获取屏幕尺寸信息的通用方法
     */
    private final android.util.DisplayMetrics getScreenMetrics() {
        return null;
    }
    
    /**
     * 使用新分辨率重启屏幕捕获
     */
    public final boolean restartScreenCaptureWithNewResolution(@org.jetbrains.annotations.NotNull()
    android.view.Surface surface, int newWidth, int newHeight) {
        return false;
    }
    
    /**
     * 重新创建整个MediaProjection
     */
    private final boolean recreateEntireMediaProjection(android.view.Surface surface, int newWidth, int newHeight) {
        return false;
    }
    
    /**
     * 重新创建VirtualDisplay
     */
    private final boolean recreateVirtualDisplayWithNewResolution(android.media.projection.MediaProjection projection, android.view.Surface surface, int newWidth, int newHeight) {
        return false;
    }
    
    /**
     * 启动媒体音频MediaProjection
     */
    public final boolean startMediaAudioProjection(@org.jetbrains.annotations.Nullable()
    android.content.Intent data) {
        return false;
    }
    
    /**
     * 停止媒体音频但保留权限
     */
    public final void stopMediaAudioProjectionKeepPermission() {
    }
    
    /**
     * 清理失效的监听器引用
     */
    private final void cleanupListeners() {
    }
    
    /**
     * 通知MediaProjection已创建
     */
    private final void notifyMediaProjectionCreated(android.media.projection.MediaProjection mediaProjection) {
    }
    
    /**
     * 通知MediaProjection已停止
     */
    private final void notifyMediaProjectionStopped() {
    }
    
    /**
     * 释放所有资源
     */
    public final void release() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\t\u001a\u00020\b2\u0006\u0010\n\u001a\u00020\u000bR\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0007\u001a\u0004\u0018\u00010\b8\u0002@\u0002X\u0083\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/example/castapp/manager/MediaProjectionManager$Companion;", "", "()V", "FEATURE_APP_STARTUP_CACHE", "", "FEATURE_MEDIA_AUDIO", "FEATURE_SCREEN_CASTING", "INSTANCE", "Lcom/example/castapp/manager/MediaProjectionManager;", "getInstance", "context", "Landroid/content/Context;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * 获取单例实例
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.manager.MediaProjectionManager getInstance(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
            return null;
        }
    }
    
    /**
     * MediaProjection回调实现（Android 14+要求）
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0002\b\u0082\u0004\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0018\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u0006H\u0016J\u0010\u0010\b\u001a\u00020\u00042\u0006\u0010\t\u001a\u00020\nH\u0016J\b\u0010\u000b\u001a\u00020\u0004H\u0016\u00a8\u0006\f"}, d2 = {"Lcom/example/castapp/manager/MediaProjectionManager$MediaProjectionCallbackImpl;", "Landroid/media/projection/MediaProjection$Callback;", "(Lcom/example/castapp/manager/MediaProjectionManager;)V", "onCapturedContentResize", "", "width", "", "height", "onCapturedContentVisibilityChanged", "isVisible", "", "onStop", "app_debug"})
    final class MediaProjectionCallbackImpl extends android.media.projection.MediaProjection.Callback {
        
        public MediaProjectionCallbackImpl() {
            super();
        }
        
        @java.lang.Override()
        public void onStop() {
        }
        
        @java.lang.Override()
        public void onCapturedContentResize(int width, int height) {
        }
        
        @java.lang.Override()
        public void onCapturedContentVisibilityChanged(boolean isVisible) {
        }
    }
    
    /**
     * MediaProjection状态监听接口
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\b\u0010\u0006\u001a\u00020\u0003H&\u00a8\u0006\u0007"}, d2 = {"Lcom/example/castapp/manager/MediaProjectionManager$MediaProjectionStateListener;", "", "onMediaProjectionCreated", "", "mediaProjection", "Landroid/media/projection/MediaProjection;", "onMediaProjectionStopped", "app_debug"})
    public static abstract interface MediaProjectionStateListener {
        
        public abstract void onMediaProjectionCreated(@org.jetbrains.annotations.NotNull()
        android.media.projection.MediaProjection mediaProjection);
        
        public abstract void onMediaProjectionStopped();
    }
}