package com.example.castapp.network;

/**
 * 数据视图接口 - 零拷贝数据访问
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0012\n\u0002\b\u0002\n\u0002\u0010\u0005\n\u0002\b\u0003\bf\u0018\u00002\u00020\u0001J \u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\u00032\u0006\u0010\r\u001a\u00020\u0003H&J$\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\u00032\b\b\u0002\u0010\r\u001a\u00020\u0003H&J(\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u00032\u0006\u0010\f\u001a\u00020\u00032\u0006\u0010\r\u001a\u00020\u0003H&J\u0010\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u0003H&J\b\u0010\u0013\u001a\u00020\u000eH&R\u0012\u0010\u0002\u001a\u00020\u0003X\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b\u0004\u0010\u0005R\u0012\u0010\u0006\u001a\u00020\u0003X\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b\u0007\u0010\u0005\u00a8\u0006\u0014"}, d2 = {"Lcom/example/castapp/network/DataView;", "", "offset", "", "getOffset", "()I", "size", "getSize", "copyTo", "", "dest", "Ljava/nio/ByteBuffer;", "srcOffset", "length", "", "destOffset", "getByte", "", "index", "toByteArray", "app_debug"})
public abstract interface DataView {
    
    public abstract int getSize();
    
    public abstract int getOffset();
    
    @org.jetbrains.annotations.NotNull()
    public abstract byte[] toByteArray();
    
    public abstract void copyTo(@org.jetbrains.annotations.NotNull()
    byte[] dest, int destOffset, int length);
    
    public abstract byte getByte(int index);
    
    public abstract void copyTo(@org.jetbrains.annotations.NotNull()
    byte[] dest, int destOffset, int srcOffset, int length);
    
    public abstract void copyTo(@org.jetbrains.annotations.NotNull()
    java.nio.ByteBuffer dest, int srcOffset, int length);
    
    /**
     * 数据视图接口 - 零拷贝数据访问
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}