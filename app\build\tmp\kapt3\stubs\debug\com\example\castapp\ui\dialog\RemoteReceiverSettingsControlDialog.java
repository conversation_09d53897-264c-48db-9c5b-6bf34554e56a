package com.example.castapp.ui.dialog;

/**
 * 🎛️ 远程接收端设置控制对话框
 * 独立的控制面板，专门用于远程控制接收端的各种设置
 *
 * 主要功能：
 * - 远程控制音视频服务开关
 * - 远程切换播放模式（扬声器/听筒）
 * - 远程调整音量
 * - 设置状态同步
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0092\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\u0010\u0000\n\u0002\b\r\u0018\u0000 @2\u00020\u0001:\u0001@B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0016\u0010\u001e\u001a\u00020\u001f2\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u001f0!H\u0002J\u0010\u0010\"\u001a\u00020\u001f2\u0006\u0010#\u001a\u00020\nH\u0002J\u0010\u0010$\u001a\u00020\u001f2\u0006\u0010%\u001a\u00020&H\u0002J\u0012\u0010\'\u001a\u00020\u001f2\b\u0010(\u001a\u0004\u0018\u00010)H\u0016J&\u0010*\u001a\u0004\u0018\u00010&2\u0006\u0010+\u001a\u00020,2\b\u0010-\u001a\u0004\u0018\u00010.2\b\u0010(\u001a\u0004\u0018\u00010)H\u0016J\b\u0010/\u001a\u00020\u001fH\u0016J\u001a\u00100\u001a\u00020\u001f2\u0012\u00101\u001a\u000e\u0012\u0004\u0012\u000203\u0012\u0004\u0012\u00020402J\u001a\u00105\u001a\u00020\u001f2\u0006\u0010%\u001a\u00020&2\b\u0010(\u001a\u0004\u0018\u00010)H\u0016J\b\u00106\u001a\u00020\u001fH\u0002J\b\u00107\u001a\u00020\u001fH\u0002J\u0010\u00108\u001a\u00020\u001f2\u0006\u0010#\u001a\u00020\nH\u0002J\u0010\u00109\u001a\u00020\u001f2\u0006\u0010:\u001a\u00020\nH\u0002J\u0010\u0010;\u001a\u00020\u001f2\u0006\u0010<\u001a\u00020\rH\u0002J\b\u0010=\u001a\u00020\u001fH\u0002J\b\u0010>\u001a\u00020\u001fH\u0002J\u001c\u0010?\u001a\u00020\u001f2\u0012\u00101\u001a\u000e\u0012\u0004\u0012\u000203\u0012\u0004\u0012\u00020402H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0013X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0015X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0017X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0019X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u0017X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u001cX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006A"}, d2 = {"Lcom/example/castapp/ui/dialog/RemoteReceiverSettingsControlDialog;", "Landroidx/fragment/app/DialogFragment;", "()V", "closeButton", "Landroid/widget/ImageButton;", "connectionManager", "Lcom/example/castapp/manager/RemoteConnectionManager;", "connectionStatusText", "Landroid/widget/TextView;", "currentAudioVideoEnabled", "", "currentIsSpeakerMode", "currentVolume", "", "dialogTitle", "isUpdatingFromSync", "receiverManager", "Lcom/example/castapp/manager/RemoteReceiverManager;", "remoteAudioOutputModeGroup", "Landroid/widget/RadioGroup;", "remoteAudioVideoSwitch", "Landroidx/appcompat/widget/SwitchCompat;", "remoteEarpieceModeRadio", "Landroid/widget/RadioButton;", "remoteReceiverConnection", "Lcom/example/castapp/model/RemoteReceiverConnection;", "remoteSpeakerModeRadio", "remoteVolumeSeekBar", "Landroid/widget/SeekBar;", "remoteVolumeText", "disableListenersTemporarily", "", "action", "Lkotlin/Function0;", "enableAllControls", "enabled", "initViews", "view", "Landroid/view/View;", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onCreateView", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "onDestroy", "onSettingsSyncReceived", "settings", "", "", "", "onViewCreated", "requestCurrentSettings", "requestCurrentSettingsWithDelay", "sendAudioVideoToggle", "sendPlaybackModeChange", "isSpeakerMode", "sendVolumeChange", "volume", "setupClickListeners", "updateUI", "updateUIFromSettings", "Companion", "app_debug"})
public final class RemoteReceiverSettingsControlDialog extends androidx.fragment.app.DialogFragment {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String ARG_RECEIVER_CONNECTION = "receiver_connection";
    private android.widget.TextView dialogTitle;
    private android.widget.ImageButton closeButton;
    private android.widget.TextView connectionStatusText;
    private androidx.appcompat.widget.SwitchCompat remoteAudioVideoSwitch;
    private android.widget.RadioGroup remoteAudioOutputModeGroup;
    private android.widget.RadioButton remoteSpeakerModeRadio;
    private android.widget.RadioButton remoteEarpieceModeRadio;
    private android.widget.SeekBar remoteVolumeSeekBar;
    private android.widget.TextView remoteVolumeText;
    private com.example.castapp.model.RemoteReceiverConnection remoteReceiverConnection;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.RemoteConnectionManager connectionManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.RemoteReceiverManager receiverManager = null;
    private boolean currentAudioVideoEnabled = false;
    private boolean currentIsSpeakerMode = true;
    private int currentVolume = 80;
    private boolean isUpdatingFromSync = false;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.ui.dialog.RemoteReceiverSettingsControlDialog.Companion Companion = null;
    
    public RemoteReceiverSettingsControlDialog() {
        super();
    }
    
    @java.lang.Override()
    public void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override()
    public void onDestroy() {
    }
    
    /**
     * 初始化视图控件
     */
    private final void initViews(android.view.View view) {
    }
    
    /**
     * 设置点击监听器
     */
    private final void setupClickListeners() {
    }
    
    /**
     * 更新UI显示
     */
    private final void updateUI() {
    }
    
    /**
     * 启用/禁用所有控件
     */
    private final void enableAllControls(boolean enabled) {
    }
    
    /**
     * 🔄 延迟请求当前设置状态（避免UI初始化冲突）
     */
    private final void requestCurrentSettingsWithDelay() {
    }
    
    /**
     * 请求当前设置状态
     */
    private final void requestCurrentSettings() {
    }
    
    /**
     * 发送音视频服务开关控制
     */
    private final void sendAudioVideoToggle(boolean enabled) {
    }
    
    /**
     * 发送播放模式切换控制
     */
    private final void sendPlaybackModeChange(boolean isSpeakerMode) {
    }
    
    /**
     * 发送音量调整控制
     */
    private final void sendVolumeChange(int volume) {
    }
    
    /**
     * 处理接收到的设置同步消息
     */
    public final void onSettingsSyncReceived(@org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.String, ? extends java.lang.Object> settings) {
    }
    
    /**
     * 🔄 从设置数据更新UI（不触发监听器）
     */
    private final void updateUIFromSettings(java.util.Map<java.lang.String, ? extends java.lang.Object> settings) {
    }
    
    /**
     * 🔄 暂时禁用监听器执行操作（避免反向同步时重复发送）
     */
    private final void disableListenersTemporarily(kotlin.jvm.functions.Function0<kotlin.Unit> action) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\t"}, d2 = {"Lcom/example/castapp/ui/dialog/RemoteReceiverSettingsControlDialog$Companion;", "", "()V", "ARG_RECEIVER_CONNECTION", "", "newInstance", "Lcom/example/castapp/ui/dialog/RemoteReceiverSettingsControlDialog;", "receiverConnection", "Lcom/example/castapp/model/RemoteReceiverConnection;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.ui.dialog.RemoteReceiverSettingsControlDialog newInstance(@org.jetbrains.annotations.NotNull()
        com.example.castapp.model.RemoteReceiverConnection receiverConnection) {
            return null;
        }
    }
}