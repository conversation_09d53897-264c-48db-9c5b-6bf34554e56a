package com.example.castapp.ui.adapter;

/**
 * 遥控管理标签页适配器
 * 管理发送端和接收端两个标签页
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0000\u0018\u00002\u00020\u0001B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0010\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0016J\b\u0010\u000f\u001a\u00020\u000eH\u0016J\u0006\u0010\u0010\u001a\u00020\bJ\u0006\u0010\u0011\u001a\u00020\nJ\u000e\u0010\u0012\u001a\u00020\u00132\u0006\u0010\r\u001a\u00020\u000eR\u0010\u0010\u0007\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\t\u001a\u0004\u0018\u00010\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0014"}, d2 = {"Lcom/example/castapp/ui/adapter/RemoteTabPagerAdapter;", "Landroidx/viewpager2/adapter/FragmentStateAdapter;", "fragmentManager", "Landroidx/fragment/app/FragmentManager;", "lifecycle", "Landroidx/lifecycle/Lifecycle;", "(Landroidx/fragment/app/FragmentManager;Landroidx/lifecycle/Lifecycle;)V", "receiverFragment", "Lcom/example/castapp/ui/fragment/RemoteReceiverTabFragment;", "senderFragment", "Lcom/example/castapp/ui/fragment/RemoteSenderTabFragment;", "createFragment", "Landroidx/fragment/app/Fragment;", "position", "", "getItemCount", "getReceiverFragment", "getSenderFragment", "getTabTitle", "", "app_debug"})
public final class RemoteTabPagerAdapter extends androidx.viewpager2.adapter.FragmentStateAdapter {
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.fragment.RemoteSenderTabFragment senderFragment;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.fragment.RemoteReceiverTabFragment receiverFragment;
    
    public RemoteTabPagerAdapter(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.FragmentManager fragmentManager, @org.jetbrains.annotations.NotNull()
    androidx.lifecycle.Lifecycle lifecycle) {
        super(null);
    }
    
    @java.lang.Override()
    public int getItemCount() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public androidx.fragment.app.Fragment createFragment(int position) {
        return null;
    }
    
    /**
     * 获取发送端Fragment
     * 确保Fragment已经创建
     */
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.ui.fragment.RemoteSenderTabFragment getSenderFragment() {
        return null;
    }
    
    /**
     * 获取接收端Fragment
     * 确保Fragment已经创建
     */
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.ui.fragment.RemoteReceiverTabFragment getReceiverFragment() {
        return null;
    }
    
    /**
     * 获取标签页标题
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getTabTitle(int position) {
        return null;
    }
}