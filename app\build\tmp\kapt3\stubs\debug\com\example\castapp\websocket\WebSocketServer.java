package com.example.castapp.websocket;

/**
 * WebSocket服务器
 * 用于接收发送端的控制消息
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0007\n\u0002\u0010\"\n\u0002\b\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\u0018\u00002\u00020\u0001BU\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u0005\u0012(\b\u0002\u0010\b\u001a\"\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u0003\u0012\u0006\u0012\u0004\u0018\u00010\n\u0012\u0004\u0012\u00020\u00070\t\u0012\b\b\u0002\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0002\u0010\fJ\u000e\u0010\u0014\u001a\u00020\u00032\u0006\u0010\u0015\u001a\u00020\u0006J\u000e\u0010\u0016\u001a\u00020\u00072\u0006\u0010\u0017\u001a\u00020\nJ\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\n0\u0019J\u0010\u0010\u001a\u001a\u0004\u0018\u00010\n2\u0006\u0010\u0017\u001a\u00020\nJ\u0015\u0010\u001b\u001a\u0004\u0018\u00010\u00032\u0006\u0010\u0017\u001a\u00020\n\u00a2\u0006\u0002\u0010\u001cJ\u0006\u0010\u001d\u001a\u00020\u0007J\b\u0010\u001e\u001a\u00020\u0007H\u0002J(\u0010\u001f\u001a\u00020\u00072\u0006\u0010 \u001a\u00020\u000f2\u0006\u0010!\u001a\u00020\u00032\u0006\u0010\"\u001a\u00020\n2\u0006\u0010#\u001a\u00020\u0011H\u0016J\u001e\u0010$\u001a\u00020\u00072\b\u0010 \u001a\u0004\u0018\u00010\u000f2\n\u0010%\u001a\u00060&j\u0002`\'H\u0016J\u0018\u0010(\u001a\u00020\u00072\u0006\u0010 \u001a\u00020\u000f2\u0006\u0010\u0015\u001a\u00020\nH\u0016J\u0018\u0010)\u001a\u00020\u00072\u0006\u0010 \u001a\u00020\u000f2\u0006\u0010*\u001a\u00020+H\u0016J\b\u0010,\u001a\u00020\u0007H\u0016J\u0018\u0010-\u001a\u00020\u00112\u0006\u0010\u0017\u001a\u00020\n2\u0006\u0010\u0015\u001a\u00020\u0006H\u0002J\u0016\u0010.\u001a\u00020\u00112\u0006\u0010\u0017\u001a\u00020\n2\u0006\u0010\u0015\u001a\u00020\u0006J\u0006\u0010/\u001a\u00020\u0007R\u001a\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u000f0\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R.\u0010\b\u001a\"\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u0003\u0012\u0006\u0012\u0004\u0018\u00010\n\u0012\u0004\u0012\u00020\u00070\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\n0\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00060"}, d2 = {"Lcom/example/castapp/websocket/WebSocketServer;", "Lorg/java_websocket/server/WebSocketServer;", "port", "", "onMessageReceived", "Lkotlin/Function1;", "Lcom/example/castapp/websocket/ControlMessage;", "", "onConnectionRequest", "Lkotlin/Function4;", "", "serverType", "(ILkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function4;Ljava/lang/String;)V", "connectionMap", "Ljava/util/concurrent/ConcurrentHashMap;", "Lorg/java_websocket/WebSocket;", "disconnectNotificationSent", "", "isRunning", "reverseConnectionMap", "broadcastMessage", "message", "disconnectSpecificConnection", "connectionId", "getActiveConnectionIds", "", "getClientIP", "getClientPort", "(Ljava/lang/String;)Ljava/lang/Integer;", "markDisconnectNotificationSent", "notifyAllConnectionsDisconnect", "onClose", "conn", "code", "reason", "remote", "onError", "ex", "Ljava/lang/Exception;", "Lkotlin/Exception;", "onMessage", "onOpen", "handshake", "Lorg/java_websocket/handshake/ClientHandshake;", "onStart", "sendMessage", "sendMessageToClient", "stopServer", "app_debug"})
public final class WebSocketServer extends org.java_websocket.server.WebSocketServer {
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<com.example.castapp.websocket.ControlMessage, kotlin.Unit> onMessageReceived = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function4<java.lang.String, java.lang.String, java.lang.Integer, java.lang.String, kotlin.Unit> onConnectionRequest = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String serverType = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, org.java_websocket.WebSocket> connectionMap = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<org.java_websocket.WebSocket, java.lang.String> reverseConnectionMap = null;
    private boolean isRunning = false;
    private boolean disconnectNotificationSent = false;
    
    public WebSocketServer(int port, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.castapp.websocket.ControlMessage, kotlin.Unit> onMessageReceived, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function4<? super java.lang.String, ? super java.lang.String, ? super java.lang.Integer, ? super java.lang.String, kotlin.Unit> onConnectionRequest, @org.jetbrains.annotations.NotNull()
    java.lang.String serverType) {
        super();
    }
    
    @java.lang.Override()
    public void onOpen(@org.jetbrains.annotations.NotNull()
    org.java_websocket.WebSocket conn, @org.jetbrains.annotations.NotNull()
    org.java_websocket.handshake.ClientHandshake handshake) {
    }
    
    @java.lang.Override()
    public void onClose(@org.jetbrains.annotations.NotNull()
    org.java_websocket.WebSocket conn, int code, @org.jetbrains.annotations.NotNull()
    java.lang.String reason, boolean remote) {
    }
    
    @java.lang.Override()
    public void onMessage(@org.jetbrains.annotations.NotNull()
    org.java_websocket.WebSocket conn, @org.jetbrains.annotations.NotNull()
    java.lang.String message) {
    }
    
    @java.lang.Override()
    public void onError(@org.jetbrains.annotations.Nullable()
    org.java_websocket.WebSocket conn, @org.jetbrains.annotations.NotNull()
    java.lang.Exception ex) {
    }
    
    @java.lang.Override()
    public void onStart() {
    }
    
    /**
     * 向指定连接发送消息（私有方法）
     */
    private final boolean sendMessage(java.lang.String connectionId, com.example.castapp.websocket.ControlMessage message) {
        return false;
    }
    
    /**
     * 向指定连接发送消息（公共方法）
     */
    public final boolean sendMessageToClient(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    com.example.castapp.websocket.ControlMessage message) {
        return false;
    }
    
    /**
     * 获取所有活跃连接ID
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Set<java.lang.String> getActiveConnectionIds() {
        return null;
    }
    
    /**
     * 广播消息到所有活跃连接
     */
    public final int broadcastMessage(@org.jetbrains.annotations.NotNull()
    com.example.castapp.websocket.ControlMessage message) {
        return 0;
    }
    
    /**
     * 根据连接ID获取客户端IP地址（统一ID架构）
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getClientIP(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
        return null;
    }
    
    /**
     * 根据连接ID获取客户端端口（统一ID架构）
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getClientPort(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
        return null;
    }
    
    /**
     * 停止服务器
     */
    public final void stopServer() {
    }
    
    /**
     * 标记断开通知已发送（供外部调用）
     */
    public final void markDisconnectNotificationSent() {
    }
    
    /**
     * 🗑️ 断开特定连接（用于窗口删除等场景）- 优化版本
     */
    public final void disconnectSpecificConnection(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 通知所有连接断开
     */
    private final void notifyAllConnectionsDisconnect() {
    }
}