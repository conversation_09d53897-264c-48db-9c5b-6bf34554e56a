package com.example.castapp.utils;

/**
 * 内存监控管理器
 * 用于监控应用内存使用情况，检测内存泄漏和性能问题
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000Z\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\n\n\u0002\u0010\t\n\u0002\b\t\u0018\u0000 )2\u00020\u0001:\u0003)*+B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\nJ\u0010\u0010\u001a\u001a\u00020\u00182\u0006\u0010\u001b\u001a\u00020\u0012H\u0002J\b\u0010\u001c\u001a\u00020\u0012H\u0002J\u0006\u0010\u001d\u001a\u00020\u0018J\b\u0010\u001e\u001a\u00020\u0018H\u0002J\u0006\u0010\u001f\u001a\u00020\u0018J\u0010\u0010 \u001a\u00020\u00182\u0006\u0010\u001b\u001a\u00020\u0012H\u0002J\u0018\u0010!\u001a\u00020\u00182\u0006\u0010\"\u001a\u00020#2\u0006\u0010$\u001a\u00020#H\u0002J\u0010\u0010%\u001a\u00020\u00182\u0006\u0010\u001b\u001a\u00020\u0012H\u0002J\u0010\u0010&\u001a\u00020\u00182\u0006\u0010\u001b\u001a\u00020\u0012H\u0002J\u0006\u0010\'\u001a\u00020\u0018J\u0006\u0010(\u001a\u00020\u0018R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000Rf\u0010\u0007\u001aZ\u0012\u0018\u0012\u0016\u0012\u0004\u0012\u00020\n \u000b*\n\u0012\u0004\u0012\u00020\n\u0018\u00010\t0\t\u0012\f\u0012\n \u000b*\u0004\u0018\u00010\f0\f \u000b*,\u0012\u0018\u0012\u0016\u0012\u0004\u0012\u00020\n \u000b*\n\u0012\u0004\u0012\u00020\n\u0018\u00010\t0\t\u0012\f\u0012\n \u000b*\u0004\u0018\u00010\f0\f\u0018\u00010\b0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00120\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0013\u001a\u0004\u0018\u00010\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006,"}, d2 = {"Lcom/example/castapp/utils/MemoryMonitor;", "", "()V", "isMonitoring", "Ljava/util/concurrent/atomic/AtomicBoolean;", "lastMemoryUsage", "Ljava/util/concurrent/atomic/AtomicLong;", "listeners", "Ljava/util/concurrent/ConcurrentHashMap$KeySetView;", "Ljava/lang/ref/WeakReference;", "Lcom/example/castapp/utils/MemoryMonitor$MemoryMonitorListener;", "kotlin.jvm.PlatformType", "", "maxHistorySize", "", "memoryGrowthCounter", "memoryHistory", "", "Lcom/example/castapp/utils/MemoryMonitor$MemorySnapshot;", "monitorJob", "Lkotlinx/coroutines/Job;", "monitorScope", "Lkotlinx/coroutines/CoroutineScope;", "addListener", "", "listener", "analyzeMemoryUsage", "snapshot", "captureMemorySnapshot", "cleanup", "cleanupListeners", "forceGarbageCollection", "notifyMemoryCritical", "notifyMemoryLeakSuspected", "growthMB", "", "consecutiveGrowths", "notifyMemoryStable", "notifyMemoryWarning", "startMonitoring", "stopMonitoring", "Companion", "MemoryMonitorListener", "MemorySnapshot", "app_debug"})
public final class MemoryMonitor {
    private static final long MONITOR_INTERVAL_MS = 120000L;
    private static final double MEMORY_WARNING_THRESHOLD = 0.8;
    private static final double MEMORY_CRITICAL_THRESHOLD = 0.9;
    private static final int GC_SUGGESTION_THRESHOLD = 52428800;
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.example.castapp.utils.MemoryMonitor INSTANCE;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.atomic.AtomicBoolean isMonitoring = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.CoroutineScope monitorScope = null;
    @org.jetbrains.annotations.Nullable()
    private kotlinx.coroutines.Job monitorJob;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.castapp.utils.MemoryMonitor.MemorySnapshot> memoryHistory = null;
    private final int maxHistorySize = 20;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.atomic.AtomicLong lastMemoryUsage = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.atomic.AtomicLong memoryGrowthCounter = null;
    private final java.util.concurrent.ConcurrentHashMap.KeySetView<java.lang.ref.WeakReference<com.example.castapp.utils.MemoryMonitor.MemoryMonitorListener>, java.lang.Boolean> listeners = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.utils.MemoryMonitor.Companion Companion = null;
    
    private MemoryMonitor() {
        super();
    }
    
    /**
     * 开始内存监控
     */
    public final void startMonitoring() {
    }
    
    /**
     * 停止内存监控
     */
    public final void stopMonitoring() {
    }
    
    /**
     * 捕获内存快照
     */
    private final com.example.castapp.utils.MemoryMonitor.MemorySnapshot captureMemorySnapshot() {
        return null;
    }
    
    /**
     * 分析内存使用情况
     */
    private final void analyzeMemoryUsage(com.example.castapp.utils.MemoryMonitor.MemorySnapshot snapshot) {
    }
    
    /**
     * 添加监听器
     */
    public final void addListener(@org.jetbrains.annotations.NotNull()
    com.example.castapp.utils.MemoryMonitor.MemoryMonitorListener listener) {
    }
    
    /**
     * 清理失效的监听器引用
     */
    private final void cleanupListeners() {
    }
    
    /**
     * 通知内存警告
     */
    private final void notifyMemoryWarning(com.example.castapp.utils.MemoryMonitor.MemorySnapshot snapshot) {
    }
    
    /**
     * 通知内存危险
     */
    private final void notifyMemoryCritical(com.example.castapp.utils.MemoryMonitor.MemorySnapshot snapshot) {
    }
    
    /**
     * 通知疑似内存泄漏
     */
    private final void notifyMemoryLeakSuspected(long growthMB, long consecutiveGrowths) {
    }
    
    /**
     * 通知内存稳定
     */
    private final void notifyMemoryStable(com.example.castapp.utils.MemoryMonitor.MemorySnapshot snapshot) {
    }
    
    /**
     * 强制执行垃圾回收
     */
    public final void forceGarbageCollection() {
    }
    
    /**
     * 清理资源
     */
    public final void cleanup() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\f\u001a\u00020\rJ\u0006\u0010\u000e\u001a\u00020\u0006R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\bX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000f"}, d2 = {"Lcom/example/castapp/utils/MemoryMonitor$Companion;", "", "()V", "GC_SUGGESTION_THRESHOLD", "", "INSTANCE", "Lcom/example/castapp/utils/MemoryMonitor;", "MEMORY_CRITICAL_THRESHOLD", "", "MEMORY_WARNING_THRESHOLD", "MONITOR_INTERVAL_MS", "", "clearInstance", "", "getInstance", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.utils.MemoryMonitor getInstance() {
            return null;
        }
        
        /**
         * 清理单例实例 - 🚀 根源优化：只清理引用，不调用cleanup()避免重复
         */
        public final void clearInstance() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0004\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\u0018\u0010\u0006\u001a\u00020\u00032\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\bH&J\u0010\u0010\n\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\u0010\u0010\u000b\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&\u00a8\u0006\f"}, d2 = {"Lcom/example/castapp/utils/MemoryMonitor$MemoryMonitorListener;", "", "onMemoryCritical", "", "snapshot", "Lcom/example/castapp/utils/MemoryMonitor$MemorySnapshot;", "onMemoryLeakSuspected", "growthMB", "", "consecutiveGrowths", "onMemoryStable", "onMemoryWarning", "app_debug"})
    public static abstract interface MemoryMonitorListener {
        
        public abstract void onMemoryWarning(@org.jetbrains.annotations.NotNull()
        com.example.castapp.utils.MemoryMonitor.MemorySnapshot snapshot);
        
        public abstract void onMemoryCritical(@org.jetbrains.annotations.NotNull()
        com.example.castapp.utils.MemoryMonitor.MemorySnapshot snapshot);
        
        public abstract void onMemoryLeakSuspected(long growthMB, long consecutiveGrowths);
        
        public abstract void onMemoryStable(@org.jetbrains.annotations.NotNull()
        com.example.castapp.utils.MemoryMonitor.MemorySnapshot snapshot);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0002\b\u0006\n\u0002\u0010\u0006\n\u0002\b\u0013\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B=\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u0012\u0006\u0010\u0007\u001a\u00020\u0003\u0012\u0006\u0010\b\u001a\u00020\u0003\u0012\u0006\u0010\t\u001a\u00020\n\u00a2\u0006\u0002\u0010\u000bJ\t\u0010\u0015\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0016\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0017\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\nH\u00c6\u0003JO\u0010\u001c\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\u00032\b\b\u0002\u0010\b\u001a\u00020\u00032\b\b\u0002\u0010\t\u001a\u00020\nH\u00c6\u0001J\u0013\u0010\u001d\u001a\u00020\u001e2\b\u0010\u001f\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010 \u001a\u00020!H\u00d6\u0001J\t\u0010\"\u001a\u00020#H\u00d6\u0001R\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\rR\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\rR\u0011\u0010\u0007\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\rR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\rR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\r\u00a8\u0006$"}, d2 = {"Lcom/example/castapp/utils/MemoryMonitor$MemorySnapshot;", "", "timestamp", "", "usedMemoryMB", "maxMemoryMB", "freeMemoryMB", "nativeHeapSizeMB", "nativeHeapAllocatedMB", "memoryUsagePercent", "", "(JJJJJJD)V", "getFreeMemoryMB", "()J", "getMaxMemoryMB", "getMemoryUsagePercent", "()D", "getNativeHeapAllocatedMB", "getNativeHeapSizeMB", "getTimestamp", "getUsedMemoryMB", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "equals", "", "other", "hashCode", "", "toString", "", "app_debug"})
    public static final class MemorySnapshot {
        private final long timestamp = 0L;
        private final long usedMemoryMB = 0L;
        private final long maxMemoryMB = 0L;
        private final long freeMemoryMB = 0L;
        private final long nativeHeapSizeMB = 0L;
        private final long nativeHeapAllocatedMB = 0L;
        private final double memoryUsagePercent = 0.0;
        
        public MemorySnapshot(long timestamp, long usedMemoryMB, long maxMemoryMB, long freeMemoryMB, long nativeHeapSizeMB, long nativeHeapAllocatedMB, double memoryUsagePercent) {
            super();
        }
        
        public final long getTimestamp() {
            return 0L;
        }
        
        public final long getUsedMemoryMB() {
            return 0L;
        }
        
        public final long getMaxMemoryMB() {
            return 0L;
        }
        
        public final long getFreeMemoryMB() {
            return 0L;
        }
        
        public final long getNativeHeapSizeMB() {
            return 0L;
        }
        
        public final long getNativeHeapAllocatedMB() {
            return 0L;
        }
        
        public final double getMemoryUsagePercent() {
            return 0.0;
        }
        
        public final long component1() {
            return 0L;
        }
        
        public final long component2() {
            return 0L;
        }
        
        public final long component3() {
            return 0L;
        }
        
        public final long component4() {
            return 0L;
        }
        
        public final long component5() {
            return 0L;
        }
        
        public final long component6() {
            return 0L;
        }
        
        public final double component7() {
            return 0.0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.utils.MemoryMonitor.MemorySnapshot copy(long timestamp, long usedMemoryMB, long maxMemoryMB, long freeMemoryMB, long nativeHeapSizeMB, long nativeHeapAllocatedMB, double memoryUsagePercent) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}