package com.example.castapp.ui.windowsettings;

/**
 * 裁剪功能管理器 - 统一版本
 * 🎯 合并了UnifiedCropManager的功能，提供完整的裁剪管理能力
 *
 * 核心功能：
 * 1. 裁剪UI交互管理（CropOverlayView、控制按钮）
 * 2. 统一裁剪逻辑处理（clipBounds机制）
 * 3. 裁剪状态管理和监听
 * 4. 圆角和边框同步回调
 * 5. 设备信息显示和按钮位置保存
 *
 * 设计理念：
 * - 使用Android原生的clipBounds机制，避免手动计算
 * - 统一处理所有窗口类型（投屏/视频/摄像头/图片）
 * - 消除位置跳动和复杂的偏移计算
 * - 简化状态管理，只保留必要的裁剪状态
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\\\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\b\n\u0002\b#\u0018\u00002\u00020\u0001B\u001d\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u0006\u0010%\u001a\u00020\u000eJ\u0010\u0010%\u001a\u00020\u000e2\b\u0010&\u001a\u0004\u0018\u00010\u0017J\u0012\u0010\'\u001a\u00020\u000e2\b\u0010&\u001a\u0004\u0018\u00010\u0017H\u0002J\u0006\u0010(\u001a\u00020\u000eJ\b\u0010)\u001a\u00020\u000eH\u0002J\u0010\u0010*\u001a\u00020!2\u0006\u0010+\u001a\u00020!H\u0002J\u0010\u0010,\u001a\u00020\u000e2\b\b\u0002\u0010-\u001a\u00020\u0013J\b\u0010.\u001a\u0004\u0018\u00010\u0017J\b\u0010/\u001a\u00020\u0007H\u0002J\u0006\u0010\u001c\u001a\u00020\u0013J\u0006\u00100\u001a\u00020\u0013J\b\u00101\u001a\u00020\u000eH\u0002J\b\u00102\u001a\u00020\u000eH\u0002J\u0006\u00103\u001a\u00020\u000eJ\b\u00104\u001a\u00020\u000eH\u0002J\u0014\u00105\u001a\u00020\u000e2\f\u00106\u001a\b\u0012\u0004\u0012\u00020\u000e0\rJ\u001a\u00107\u001a\u00020\u000e2\u0012\u00106\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u000e0\u0012J\u0010\u00108\u001a\u00020\u000e2\b\u00109\u001a\u0004\u0018\u00010\u0017J\u001c\u0010:\u001a\u00020\u000e2\u0014\u0010;\u001a\u0010\u0012\u0006\u0012\u0004\u0018\u00010\u0017\u0012\u0004\u0012\u00020\u000e0\u0012J\u0010\u0010:\u001a\u00020\u000e2\b\u0010;\u001a\u0004\u0018\u00010\u0019J \u0010<\u001a\u00020\u000e2\b\u0010\u001a\u001a\u0004\u0018\u00010\u00072\u0006\u0010\u001b\u001a\u00020\u00072\u0006\u0010 \u001a\u00020!J\u0016\u0010=\u001a\u00020\u000e2\u0006\u0010>\u001a\u00020!2\u0006\u0010?\u001a\u00020!J\b\u0010@\u001a\u00020\u000eH\u0002J\b\u0010A\u001a\u00020\u000eH\u0002J\b\u0010B\u001a\u00020\u000eH\u0002J\u0006\u0010C\u001a\u00020\u000eR\u000e\u0010\t\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\f\u001a\n\u0012\u0004\u0012\u00020\u000e\u0018\u00010\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000f\u001a\u0004\u0018\u00010\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u0011\u001a\u0010\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u000e\u0018\u00010\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0014\u001a\u0004\u0018\u00010\u0015X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0016\u001a\u0004\u0018\u00010\u0017X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0018\u001a\u0004\u0018\u00010\u0019X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001a\u001a\u0004\u0018\u00010\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\u0013X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\u0013X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001e\u001a\u0004\u0018\u00010\u0017X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001f\u001a\u00020\u0013X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010 \u001a\u00020!X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001e\u0010\"\u001a\u0012\u0012\u0006\u0012\u0004\u0018\u00010\u0017\u0012\u0004\u0012\u00020\u000e\u0018\u00010\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010#\u001a\u00020!X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010$\u001a\u00020!X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006D"}, d2 = {"Lcom/example/castapp/ui/windowsettings/CropManager;", "", "context", "Landroid/content/Context;", "container", "Landroid/widget/FrameLayout;", "connectionId", "", "(Landroid/content/Context;Landroid/widget/FrameLayout;Ljava/lang/String;)V", "buttonAbsoluteX", "", "buttonAbsoluteY", "cornerRadiusUpdateCallback", "Lkotlin/Function0;", "", "cropControlButtons", "Landroid/widget/LinearLayout;", "cropModeChangeCallback", "Lkotlin/Function1;", "", "cropOverlay", "Lcom/example/castapp/ui/view/CropOverlayView;", "cropRectRatio", "Landroid/graphics/RectF;", "cropStateListener", "Lcom/example/castapp/ui/windowsettings/interfaces/CropStateListener;", "deviceName", "ipAddress", "isCroppedWindow", "isCropping", "originalCropRectRatio", "originalIsCroppedWindow", "port", "", "unifiedCropStateListener", "windowHeight", "windowWidth", "applyCrop", "cropRatio", "applyDirectCrop", "cleanup", "createCropControlButtonsInParent", "dpToPx", "dp", "endCropMode", "isCancel", "getCropRectRatio", "getDeviceDisplayInfo", "isCroppingMode", "loadButtonPosition", "removeCropControlButtons", "resetCrop", "saveButtonPosition", "setCornerRadiusUpdateCallback", "callback", "setCropModeChangeCallback", "setCropRectRatio", "rectRatio", "setCropStateListener", "listener", "setDeviceInfo", "setWindowSize", "width", "height", "setupCropControlButtonsDrag", "setupCropControlButtonsEvents", "setupDeviceInfoDisplay", "startCropMode", "app_debug"})
public final class CropManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final android.widget.FrameLayout container = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String connectionId = null;
    private int windowWidth = 0;
    private int windowHeight = 0;
    @org.jetbrains.annotations.Nullable()
    private android.graphics.RectF cropRectRatio;
    private boolean isCroppedWindow = false;
    private boolean isCropping = false;
    @org.jetbrains.annotations.Nullable()
    private android.graphics.RectF originalCropRectRatio;
    private boolean originalIsCroppedWindow = false;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.view.CropOverlayView cropOverlay;
    @org.jetbrains.annotations.Nullable()
    private android.widget.LinearLayout cropControlButtons;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String deviceName;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String ipAddress = "";
    private int port = 0;
    private float buttonAbsoluteX = -1.0F;
    private float buttonAbsoluteY = -1.0F;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.windowsettings.interfaces.CropStateListener cropStateListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> cropModeChangeCallback;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super android.graphics.RectF, kotlin.Unit> unifiedCropStateListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function0<kotlin.Unit> cornerRadiusUpdateCallback;
    
    public CropManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    android.widget.FrameLayout container, @org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
        super();
    }
    
    /**
     * 🎯 新增：设置窗口尺寸（用于统一裁剪逻辑）
     */
    public final void setWindowSize(int width, int height) {
    }
    
    /**
     * 设置裁剪状态监听器
     */
    public final void setCropStateListener(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.windowsettings.interfaces.CropStateListener listener) {
    }
    
    /**
     * 🎯 新增：设置统一裁剪状态监听器（来自UnifiedCropManager）
     */
    public final void setCropStateListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super android.graphics.RectF, kotlin.Unit> listener) {
    }
    
    /**
     * 🎯 新增：设置圆角和边框更新回调（来自UnifiedCropManager）
     */
    public final void setCornerRadiusUpdateCallback(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> callback) {
    }
    
    /**
     * 设置裁剪模式变化回调
     */
    public final void setCropModeChangeCallback(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> callback) {
    }
    
    /**
     * 设置设备信息（用于裁剪模式显示）
     */
    public final void setDeviceInfo(@org.jetbrains.annotations.Nullable()
    java.lang.String deviceName, @org.jetbrains.annotations.NotNull()
    java.lang.String ipAddress, int port) {
    }
    
    /**
     * 获取设备显示信息（与调控面板格式一致）
     */
    private final java.lang.String getDeviceDisplayInfo() {
        return null;
    }
    
    /**
     * 开始裁剪模式
     */
    public final void startCropMode() {
    }
    
    /**
     * 结束裁剪模式
     * @param isCancel 是否为取消操作，取消时不保存当前裁剪区域且不应用智能恢复
     */
    public final void endCropMode(boolean isCancel) {
    }
    
    /**
     * 🎯 新增：统一裁剪应用方法（来自UnifiedCropManager）
     * 核心简化：使用clipBounds统一处理，消除特殊情况
     *
     * @param cropRatio 裁剪区域比例，null表示移除裁剪
     */
    public final void applyCrop(@org.jetbrains.annotations.Nullable()
    android.graphics.RectF cropRatio) {
    }
    
    /**
     * 应用裁剪设置（保持原有UI交互方法）
     */
    public final void applyCrop() {
    }
    
    /**
     * 重置裁剪区域（仅重置UI显示，不影响保存的裁剪状态）
     */
    public final void resetCrop() {
    }
    
    /**
     * 获取当前是否在裁剪模式
     */
    public final boolean isCroppingMode() {
        return false;
    }
    
    /**
     * 获取当前是否为裁剪窗口
     */
    public final boolean isCroppedWindow() {
        return false;
    }
    
    /**
     * 获取当前裁剪区域比例
     */
    @org.jetbrains.annotations.Nullable()
    public final android.graphics.RectF getCropRectRatio() {
        return null;
    }
    
    /**
     * 设置裁剪区域比例（用于布局恢复）
     * 🎯 修复：避免循环调用，直接设置状态而不触发监听器
     */
    public final void setCropRectRatio(@org.jetbrains.annotations.Nullable()
    android.graphics.RectF rectRatio) {
    }
    
    /**
     * 🎯 新增：直接应用裁剪（不触发监听器，避免循环调用）
     * 用于内部状态设置，如布局恢复等场景
     */
    private final void applyDirectCrop(android.graphics.RectF cropRatio) {
    }
    
    /**
     * 创建裁剪控制按钮（使用XML布局）
     */
    private final void createCropControlButtonsInParent() {
    }
    
    /**
     * 设置设备信息显示
     */
    private final void setupDeviceInfoDisplay() {
    }
    
    /**
     * 设置裁剪控制按钮拖动功能
     */
    private final void setupCropControlButtonsDrag() {
    }
    
    /**
     * 设置裁剪控制按钮事件
     */
    private final void setupCropControlButtonsEvents() {
    }
    
    /**
     * 移除裁剪控制按钮
     */
    private final void removeCropControlButtons() {
    }
    
    /**
     * 保存按钮组位置到SharedPreferences（绝对坐标）
     */
    private final void saveButtonPosition() {
    }
    
    /**
     * 从SharedPreferences加载按钮组位置（绝对坐标）
     */
    private final void loadButtonPosition() {
    }
    
    /**
     * dp转px工具方法
     */
    private final int dpToPx(int dp) {
        return 0;
    }
    
    /**
     * 清理资源
     */
    public final void cleanup() {
    }
}