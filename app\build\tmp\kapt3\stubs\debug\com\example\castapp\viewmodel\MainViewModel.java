package com.example.castapp.viewmodel;

/**
 * 主界面ViewModel
 * 管理主界面的状态和业务逻辑
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000T\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\"\n\u0002\b\u0013\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0010\u0018\u0000 @2\u00020\u0001:\u0001@B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u00100\u001a\u0002012\u0006\u00102\u001a\u00020\u0007J\u000e\u00103\u001a\u0002012\u0006\u00102\u001a\u00020\u0007J\u001e\u00104\u001a\u0002012\u0006\u00102\u001a\u00020\u00072\u0006\u00105\u001a\u00020\u000e2\u0006\u00106\u001a\u00020\u000eJ*\u00107\u001a\u0002012\u0006\u00102\u001a\u00020\u00072\u0006\u00108\u001a\u00020\u000e2\b\b\u0002\u00109\u001a\u00020\u000e2\b\b\u0002\u0010:\u001a\u00020\u000eJ\u0006\u0010;\u001a\u000201J\u0006\u0010<\u001a\u000201J\u0006\u0010=\u001a\u000201J\u000e\u0010>\u001a\u0002012\u0006\u00102\u001a\u00020\u0007J\b\u0010?\u001a\u000201H\u0014J\u0006\u0010$\u001a\u000201J\u0006\u0010&\u001a\u000201J\u0006\u0010(\u001a\u000201R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\n0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R&\u0010\f\u001a\u001a\u0012\u0016\u0012\u0014\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u000e0\r0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\n0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\n0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\n0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R2\u0010\u0013\u001a&\u0012\"\u0012 \u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u000e\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u000e0\u00140\r0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0015\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00170\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0017\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00070\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0019R\u0017\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00070\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0019R\u0017\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\n0\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0019R\u0017\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00070\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u0019R)\u0010\"\u001a\u001a\u0012\u0016\u0012\u0014\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u000e0\r0\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u0019R\u0017\u0010$\u001a\b\u0012\u0004\u0012\u00020\n0\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010\u0019R\u0017\u0010&\u001a\b\u0012\u0004\u0012\u00020\n0\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010\u0019R\u0017\u0010(\u001a\b\u0012\u0004\u0012\u00020\n0\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010\u0019R\u000e\u0010*\u001a\u00020+X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010,\u001a\b\u0012\u0004\u0012\u00020\u00070\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b-\u0010\u0019R5\u0010.\u001a&\u0012\"\u0012 \u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u000e\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u000e0\u00140\r0\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u0010\u0019\u00a8\u0006A"}, d2 = {"Lcom/example/castapp/viewmodel/MainViewModel;", "Landroidx/lifecycle/AndroidViewModel;", "application", "Landroid/app/Application;", "(Landroid/app/Application;)V", "_deviceInfoUpdatedEvent", "Landroidx/lifecycle/MutableLiveData;", "", "_newConnectionEvent", "_removeAllConnectionsEvent", "", "_removeConnectionEvent", "_screenResolutionEvent", "Lkotlin/Triple;", "", "_showReceiverDialog", "_showRemoteControlManagerDialog", "_showSenderDialog", "_toastMessage", "_videoOrientationChangedEvent", "Lkotlin/Pair;", "castingConnections", "Landroidx/lifecycle/LiveData;", "", "getCastingConnections", "()Landroidx/lifecycle/LiveData;", "deviceInfoUpdatedEvent", "getDeviceInfoUpdatedEvent", "newConnectionEvent", "getNewConnectionEvent", "removeAllConnectionsEvent", "getRemoveAllConnectionsEvent", "removeConnectionEvent", "getRemoveConnectionEvent", "screenResolutionEvent", "getScreenResolutionEvent", "showReceiverDialog", "getShowReceiverDialog", "showRemoteControlManagerDialog", "getShowRemoteControlManagerDialog", "showSenderDialog", "getShowSenderDialog", "stateManager", "Lcom/example/castapp/manager/StateManager;", "toastMessage", "getToastMessage", "videoOrientationChangedEvent", "getVideoOrientationChangedEvent", "handleConnectionDisconnected", "", "connectionId", "handleNewConnection", "handleScreenResolution", "width", "height", "handleVideoOrientationChanged", "orientation", "videoWidth", "videoHeight", "hideReceiverDialog", "hideRemoteControlManagerDialog", "hideSenderDialog", "notifyDeviceInfoUpdated", "onCleared", "Companion", "app_debug"})
public final class MainViewModel extends androidx.lifecycle.AndroidViewModel {
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile java.lang.ref.WeakReference<com.example.castapp.viewmodel.MainViewModel> instanceRef;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.StateManager stateManager = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.Boolean> _showSenderDialog = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.Boolean> showSenderDialog = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.Boolean> _showReceiverDialog = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.Boolean> showReceiverDialog = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.Boolean> _showRemoteControlManagerDialog = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.Boolean> showRemoteControlManagerDialog = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.String> _toastMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.String> toastMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.String> _newConnectionEvent = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.String> newConnectionEvent = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.String> _removeConnectionEvent = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.String> removeConnectionEvent = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.Boolean> _removeAllConnectionsEvent = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.Boolean> removeAllConnectionsEvent = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.util.Set<java.lang.String>> castingConnections = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<kotlin.Triple<java.lang.String, java.lang.Integer, java.lang.Integer>> _screenResolutionEvent = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<kotlin.Triple<java.lang.String, java.lang.Integer, java.lang.Integer>> screenResolutionEvent = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.String> _deviceInfoUpdatedEvent = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.String> deviceInfoUpdatedEvent = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<kotlin.Triple<java.lang.String, java.lang.Integer, kotlin.Pair<java.lang.Integer, java.lang.Integer>>> _videoOrientationChangedEvent = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<kotlin.Triple<java.lang.String, java.lang.Integer, kotlin.Pair<java.lang.Integer, java.lang.Integer>>> videoOrientationChangedEvent = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.viewmodel.MainViewModel.Companion Companion = null;
    
    public MainViewModel(@org.jetbrains.annotations.NotNull()
    android.app.Application application) {
        super(null);
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.Boolean> getShowSenderDialog() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.Boolean> getShowReceiverDialog() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.Boolean> getShowRemoteControlManagerDialog() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.String> getToastMessage() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.String> getNewConnectionEvent() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.String> getRemoveConnectionEvent() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.Boolean> getRemoveAllConnectionsEvent() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.Set<java.lang.String>> getCastingConnections() {
        return null;
    }
    
    /**
     * 显示发送端对话框
     */
    public final void showSenderDialog() {
    }
    
    /**
     * 隐藏发送端对话框
     */
    public final void hideSenderDialog() {
    }
    
    /**
     * 显示接收端对话框
     */
    public final void showReceiverDialog() {
    }
    
    /**
     * 隐藏接收端对话框
     */
    public final void hideReceiverDialog() {
    }
    
    /**
     * 显示遥控管理对话框
     */
    public final void showRemoteControlManagerDialog() {
    }
    
    /**
     * 隐藏遥控管理对话框
     */
    public final void hideRemoteControlManagerDialog() {
    }
    
    /**
     * 处理新连接事件
     */
    public final void handleNewConnection(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 处理连接断开事件
     */
    public final void handleConnectionDisconnected(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<kotlin.Triple<java.lang.String, java.lang.Integer, java.lang.Integer>> getScreenResolutionEvent() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.String> getDeviceInfoUpdatedEvent() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<kotlin.Triple<java.lang.String, java.lang.Integer, kotlin.Pair<java.lang.Integer, java.lang.Integer>>> getVideoOrientationChangedEvent() {
        return null;
    }
    
    /**
     * 处理屏幕分辨率事件
     */
    public final void handleScreenResolution(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, int width, int height) {
    }
    
    /**
     * 通知设备信息已更新
     */
    public final void notifyDeviceInfoUpdated(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 🎯 横竖屏适配：处理视频方向变化事件（包含分辨率信息）
     */
    public final void handleVideoOrientationChanged(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, int orientation, int videoWidth, int videoHeight) {
    }
    
    @java.lang.Override()
    protected void onCleared() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0004\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0006\u001a\u00020\u0007J\b\u0010\b\u001a\u0004\u0018\u00010\u0005J\u000e\u0010\t\u001a\u00020\u00072\u0006\u0010\n\u001a\u00020\u0005R\u0016\u0010\u0003\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/example/castapp/viewmodel/MainViewModel$Companion;", "", "()V", "instanceRef", "Ljava/lang/ref/WeakReference;", "Lcom/example/castapp/viewmodel/MainViewModel;", "clearInstance", "", "getInstance", "setInstance", "viewModel", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * 设置ViewModel实例（使用WeakReference）
         */
        public final void setInstance(@org.jetbrains.annotations.NotNull()
        com.example.castapp.viewmodel.MainViewModel viewModel) {
        }
        
        /**
         * 获取ViewModel实例（安全访问）
         * @return MainViewModel实例，如果已被GC回收则返回null
         */
        @org.jetbrains.annotations.Nullable()
        public final com.example.castapp.viewmodel.MainViewModel getInstance() {
            return null;
        }
        
        /**
         * 清理静态引用
         */
        public final void clearInstance() {
        }
    }
}