package com.example.castapp.ui.fragment;

/**
 * 发送端标签页Fragment
 * 显示和管理远程发送端设备列表
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0010\u0018\u0000 22\u00020\u0001:\u00012B\u0005\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0016\u001a\u00020\f2\u0006\u0010\u0017\u001a\u00020\tJ\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\t0\u0019J\u0010\u0010\u001a\u001a\u00020\f2\u0006\u0010\u001b\u001a\u00020\u001cH\u0002J&\u0010\u001d\u001a\u0004\u0018\u00010\u001c2\u0006\u0010\u001e\u001a\u00020\u001f2\b\u0010 \u001a\u0004\u0018\u00010!2\b\u0010\"\u001a\u0004\u0018\u00010#H\u0016J\u001a\u0010$\u001a\u00020\f2\u0006\u0010\u001b\u001a\u00020\u001c2\b\u0010\"\u001a\u0004\u0018\u00010#H\u0016J\u000e\u0010%\u001a\u00020\f2\u0006\u0010\u0017\u001a\u00020\tJ\u0014\u0010&\u001a\u00020\f2\f\u0010\'\u001a\b\u0012\u0004\u0012\u00020\f0\u000bJ\u001a\u0010(\u001a\u00020\f2\u0012\u0010\'\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\f0\u000eJ\u001a\u0010)\u001a\u00020\f2\u0012\u0010\'\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\f0\u000eJ\u001a\u0010*\u001a\u00020\f2\u0012\u0010\'\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\f0\u000eJ\u001a\u0010+\u001a\u00020\f2\u0012\u0010\'\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\f0\u000eJ\b\u0010,\u001a\u00020\fH\u0002J\b\u0010-\u001a\u00020\fH\u0002J\u000e\u0010.\u001a\u00020\f2\u0006\u0010\u0017\u001a\u00020\tJ\b\u0010/\u001a\u00020\fH\u0002J\u0014\u00100\u001a\u00020\f2\f\u00101\u001a\b\u0012\u0004\u0012\u00020\t0\u0019R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\n\u001a\n\u0012\u0004\u0012\u00020\f\u0018\u00010\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\r\u001a\u0010\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\f\u0018\u00010\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u000f\u001a\u0010\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\f\u0018\u00010\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u0010\u001a\u0010\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\f\u0018\u00010\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u0011\u001a\u0010\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\f\u0018\u00010\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0013X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0015X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u00063"}, d2 = {"Lcom/example/castapp/ui/fragment/RemoteSenderTabFragment;", "Landroidx/fragment/app/Fragment;", "()V", "addSenderConnectionButton", "Landroid/widget/ImageButton;", "connectionAdapter", "Lcom/example/castapp/ui/adapter/RemoteSenderDeviceAdapter;", "connections", "", "Lcom/example/castapp/model/RemoteSenderConnection;", "onAddConnectionClickListener", "Lkotlin/Function0;", "", "onConnectClickListener", "Lkotlin/Function1;", "onControlClickListener", "onDeleteClickListener", "onEditClickListener", "senderConnectionCountText", "Landroid/widget/TextView;", "senderConnectionsRecyclerView", "Landroidx/recyclerview/widget/RecyclerView;", "addConnection", "connection", "getAllConnections", "", "initViews", "view", "Landroid/view/View;", "onCreateView", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "savedInstanceState", "Landroid/os/Bundle;", "onViewCreated", "removeConnection", "setOnAddConnectionClickListener", "listener", "setOnConnectClickListener", "setOnControlClickListener", "setOnDeleteClickListener", "setOnEditClickListener", "setupClickListeners", "setupRecyclerView", "updateConnection", "updateConnectionCount", "updateConnections", "newConnections", "Companion", "app_debug"})
public final class RemoteSenderTabFragment extends androidx.fragment.app.Fragment {
    private android.widget.TextView senderConnectionCountText;
    private android.widget.ImageButton addSenderConnectionButton;
    private androidx.recyclerview.widget.RecyclerView senderConnectionsRecyclerView;
    private com.example.castapp.ui.adapter.RemoteSenderDeviceAdapter connectionAdapter;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.castapp.model.RemoteSenderConnection> connections = null;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super com.example.castapp.model.RemoteSenderConnection, kotlin.Unit> onConnectClickListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super com.example.castapp.model.RemoteSenderConnection, kotlin.Unit> onControlClickListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super com.example.castapp.model.RemoteSenderConnection, kotlin.Unit> onEditClickListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super com.example.castapp.model.RemoteSenderConnection, kotlin.Unit> onDeleteClickListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function0<kotlin.Unit> onAddConnectionClickListener;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.ui.fragment.RemoteSenderTabFragment.Companion Companion = null;
    
    public RemoteSenderTabFragment() {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void initViews(android.view.View view) {
    }
    
    private final void setupRecyclerView() {
    }
    
    private final void setupClickListeners() {
    }
    
    /**
     * 设置连接点击监听器
     */
    public final void setOnConnectClickListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.castapp.model.RemoteSenderConnection, kotlin.Unit> listener) {
    }
    
    /**
     * 设置控制点击监听器
     */
    public final void setOnControlClickListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.castapp.model.RemoteSenderConnection, kotlin.Unit> listener) {
    }
    
    /**
     * 设置编辑点击监听器
     */
    public final void setOnEditClickListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.castapp.model.RemoteSenderConnection, kotlin.Unit> listener) {
    }
    
    /**
     * 设置删除点击监听器
     */
    public final void setOnDeleteClickListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.castapp.model.RemoteSenderConnection, kotlin.Unit> listener) {
    }
    
    /**
     * 设置添加连接点击监听器
     */
    public final void setOnAddConnectionClickListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> listener) {
    }
    
    /**
     * 更新连接列表
     */
    public final void updateConnections(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.castapp.model.RemoteSenderConnection> newConnections) {
    }
    
    /**
     * 添加连接
     */
    public final void addConnection(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.RemoteSenderConnection connection) {
    }
    
    /**
     * 更新连接状态
     */
    public final void updateConnection(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.RemoteSenderConnection connection) {
    }
    
    /**
     * 删除连接
     */
    public final void removeConnection(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.RemoteSenderConnection connection) {
    }
    
    /**
     * 更新连接数量显示
     */
    private final void updateConnectionCount() {
    }
    
    /**
     * 获取所有连接
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.castapp.model.RemoteSenderConnection> getAllConnections() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0003\u001a\u00020\u0004\u00a8\u0006\u0005"}, d2 = {"Lcom/example/castapp/ui/fragment/RemoteSenderTabFragment$Companion;", "", "()V", "newInstance", "Lcom/example/castapp/ui/fragment/RemoteSenderTabFragment;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.ui.fragment.RemoteSenderTabFragment newInstance() {
            return null;
        }
    }
}