package com.example.castapp.manager;

/**
 * 🔥 新增：麦克风管理器
 * 类似MediaProjectionManager，管理麦克风AudioRecord的生命周期
 * 在APP启动时预创建实例，避免后台录音权限问题
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0002\u0010\u0012\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\r\u0018\u0000  2\u00020\u0001:\u0001 B\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\b\u0010\u0015\u001a\u00020\u000eH\u0002J\b\u0010\u0016\u001a\u00020\u000eH\u0002J\b\u0010\u0017\u001a\u00020\u0012H\u0002J\u0006\u0010\u0018\u001a\u00020\u000eJ\u0006\u0010\u0019\u001a\u00020\u0012J\u0006\u0010\u0011\u001a\u00020\u0012J\"\u0010\u001a\u001a\u00020\u00122\u0006\u0010\u001b\u001a\u00020\u000b2\u0012\u0010\u001c\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u000e0\fJ\b\u0010\u001d\u001a\u00020\u000eH\u0002J\u000e\u0010\u001e\u001a\u00020\u000e2\u0006\u0010\u001b\u001a\u00020\u000bJ\b\u0010\u001f\u001a\u00020\u000eH\u0002R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0007\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R&\u0010\t\u001a\u001a\u0012\u0004\u0012\u00020\u000b\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u000e0\f0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0013\u001a\u0004\u0018\u00010\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006!"}, d2 = {"Lcom/example/castapp/manager/MicrophoneManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "bufferSize", "", "captureThread", "Ljava/lang/Thread;", "dataCallbacks", "Ljava/util/concurrent/ConcurrentHashMap;", "", "Lkotlin/Function1;", "", "", "isCapturing", "Ljava/util/concurrent/atomic/AtomicBoolean;", "isMicrophoneReady", "", "microphoneAudioRecord", "Landroid/media/AudioRecord;", "captureMicrophoneAudio", "cleanupMicrophoneInstance", "createMicrophoneInstance", "destroy", "initialize", "startCapture", "connectionId", "onDataCallback", "startCaptureThread", "stopCapture", "stopCaptureThread", "Companion", "app_debug"})
public final class MicrophoneManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    public static final int SAMPLE_RATE = 48000;
    public static final int CHANNEL_CONFIG = android.media.AudioFormat.CHANNEL_IN_MONO;
    public static final int AUDIO_FORMAT = android.media.AudioFormat.ENCODING_PCM_16BIT;
    @kotlin.jvm.Volatile()
    @android.annotation.SuppressLint(value = {"StaticFieldLeak"})
    @org.jetbrains.annotations.Nullable()
    private static volatile com.example.castapp.manager.MicrophoneManager INSTANCE;
    @org.jetbrains.annotations.Nullable()
    private android.media.AudioRecord microphoneAudioRecord;
    private boolean isMicrophoneReady = false;
    private int bufferSize = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.atomic.AtomicBoolean isCapturing = null;
    @org.jetbrains.annotations.Nullable()
    private java.lang.Thread captureThread;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, kotlin.jvm.functions.Function1<byte[], kotlin.Unit>> dataCallbacks = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.manager.MicrophoneManager.Companion Companion = null;
    
    private MicrophoneManager(android.content.Context context) {
        super();
    }
    
    /**
     * 初始化麦克风管理器
     * 在APP启动时调用，预创建麦克风实例
     */
    public final boolean initialize() {
        return false;
    }
    
    /**
     * 创建麦克风AudioRecord实例
     */
    private final boolean createMicrophoneInstance() {
        return false;
    }
    
    /**
     * 检查麦克风是否准备就绪
     */
    public final boolean isMicrophoneReady() {
        return false;
    }
    
    /**
     * 开始麦克风捕获
     * 使用预创建的AudioRecord实例
     */
    public final boolean startCapture(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super byte[], kotlin.Unit> onDataCallback) {
        return false;
    }
    
    /**
     * 停止指定连接的麦克风捕获
     */
    public final void stopCapture(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 启动捕获线程
     */
    private final void startCaptureThread() {
    }
    
    /**
     * 停止捕获线程
     */
    private final void stopCaptureThread() {
    }
    
    /**
     * 麦克风音频捕获线程
     */
    private final void captureMicrophoneAudio() {
    }
    
    /**
     * 清理麦克风实例
     */
    private final void cleanupMicrophoneInstance() {
    }
    
    /**
     * 销毁麦克风管理器
     * 在APP销毁时调用
     */
    public final void destroy() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\t\u001a\u00020\u00072\u0006\u0010\n\u001a\u00020\u000bR\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0006\u001a\u0004\u0018\u00010\u00078\u0002@\u0002X\u0083\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/example/castapp/manager/MicrophoneManager$Companion;", "", "()V", "AUDIO_FORMAT", "", "CHANNEL_CONFIG", "INSTANCE", "Lcom/example/castapp/manager/MicrophoneManager;", "SAMPLE_RATE", "getInstance", "context", "Landroid/content/Context;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * 获取单例实例
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.manager.MicrophoneManager getInstance(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
            return null;
        }
    }
}