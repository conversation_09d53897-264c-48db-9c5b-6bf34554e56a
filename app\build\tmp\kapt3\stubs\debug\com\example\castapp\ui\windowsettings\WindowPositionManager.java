package com.example.castapp.ui.windowsettings;

/**
 * 窗口位置管理器（集中化版本）
 *
 * 职责：
 * 1. 管理窗口的基准位置（用户设置的位置）
 * 2. 计算裁剪偏移
 * 3. 提供清晰的位置设置和获取接口
 * 4. 🎯 新增：集中处理所有位置坐标更新操作
 *
 * 设计原则：
 * - 基准位置：用户拖拽设置的位置，不受裁剪影响
 * - 显示位置：基准位置 + 裁剪偏移
 * - 单一职责：只管理位置，不管理尺寸或其他变换
 * - 🎯 集中化：所有位置更新都通过此管理器进行
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\r\u0018\u00002\u00020\u0001B\u001d\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0007J\u0006\u0010\u000f\u001a\u00020\u0010J\u0006\u0010\u0011\u001a\u00020\u0010J\u0016\u0010\u0012\u001a\u00020\u00102\u0006\u0010\u0013\u001a\u00020\t2\u0006\u0010\u0014\u001a\u00020\tJ\u0016\u0010\u0015\u001a\u00020\u00102\u0006\u0010\u0013\u001a\u00020\t2\u0006\u0010\u0014\u001a\u00020\tJ\u0006\u0010\u0016\u001a\u00020\u0010J\u0010\u0010\u0017\u001a\u00020\u00102\b\u0010\u0018\u001a\u0004\u0018\u00010\fJ\b\u0010\u0019\u001a\u00020\u0010H\u0002J\u0016\u0010\u001a\u001a\u00020\u00102\u0006\u0010\u001b\u001a\u00020\t2\u0006\u0010\u001c\u001a\u00020\tR\u000e\u0010\b\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000b\u001a\u0004\u0018\u00010\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001d"}, d2 = {"Lcom/example/castapp/ui/windowsettings/WindowPositionManager;", "", "container", "Landroid/widget/FrameLayout;", "windowWidth", "", "windowHeight", "(Landroid/widget/FrameLayout;II)V", "baseTranslationX", "", "baseTranslationY", "currentCropRatio", "Landroid/graphics/RectF;", "savedBaseTranslationX", "savedBaseTranslationY", "resetPosition", "", "savePositionState", "setContainerPosition", "x", "y", "setPrecisionPosition", "syncCurrentAsBase", "syncFromCroppedPosition", "cropRatio", "updateDisplayPosition", "updatePosition", "deltaX", "deltaY", "app_debug"})
public final class WindowPositionManager {
    @org.jetbrains.annotations.NotNull()
    private final android.widget.FrameLayout container = null;
    private final int windowWidth = 0;
    private final int windowHeight = 0;
    private float baseTranslationX = 0.0F;
    private float baseTranslationY = 0.0F;
    @org.jetbrains.annotations.Nullable()
    private android.graphics.RectF currentCropRatio;
    private float savedBaseTranslationX = 0.0F;
    private float savedBaseTranslationY = 0.0F;
    
    public WindowPositionManager(@org.jetbrains.annotations.NotNull()
    android.widget.FrameLayout container, int windowWidth, int windowHeight) {
        super();
    }
    
    /**
     * 同步当前显示位置为基准位置（用于初始化或重置）
     */
    public final void syncCurrentAsBase() {
    }
    
    /**
     * 从裁剪位置反推基准位置（用于状态恢复）
     * 🎯 修复：区分图片窗口和其他窗口的位置反推方式
     */
    public final void syncFromCroppedPosition(@org.jetbrains.annotations.Nullable()
    android.graphics.RectF cropRatio) {
    }
    
    /**
     * 增量位置更新（用于拖动手势）
     */
    public final void updatePosition(float deltaX, float deltaY) {
    }
    
    /**
     * 精确位置设置（用于布局恢复、精准变换等）
     */
    public final void setPrecisionPosition(float x, float y) {
    }
    
    /**
     * 重置位置到原点
     */
    public final void resetPosition() {
    }
    
    /**
     * 保存当前基准位置状态
     */
    public final void savePositionState() {
    }
    
    /**
     * 直接设置容器位置（仅用于特殊情况，如TextureView布局恢复）
     */
    public final void setContainerPosition(float x, float y) {
    }
    
    /**
     * 更新显示位置（clipBounds裁剪适配版）
     * 🎯 关键修复：clipBounds裁剪模式下，不需要计算裁剪偏移
     */
    private final void updateDisplayPosition() {
    }
}