package com.example.castapp.ui.adapter;

/**
 * 远程接收端设备列表适配器
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0007\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0006\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001:\u0001\u001bBc\u0012\f\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u0012\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\b0\u0007\u0012\u0012\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\b0\u0007\u0012\u0012\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\b0\u0007\u0012\u0012\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\b0\u0007\u00a2\u0006\u0002\u0010\fJ\u000e\u0010\r\u001a\u00020\b2\u0006\u0010\u000e\u001a\u00020\u0005J\b\u0010\u000f\u001a\u00020\u0010H\u0016J\u0018\u0010\u0011\u001a\u00020\b2\u0006\u0010\u0012\u001a\u00020\u00022\u0006\u0010\u0013\u001a\u00020\u0010H\u0016J\u0018\u0010\u0014\u001a\u00020\u00022\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u0010H\u0016J\u000e\u0010\u0018\u001a\u00020\b2\u0006\u0010\u000e\u001a\u00020\u0005J\u000e\u0010\u0019\u001a\u00020\b2\u0006\u0010\u001a\u001a\u00020\u0005R\u001a\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\b0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\b0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\b0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\b0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001c"}, d2 = {"Lcom/example/castapp/ui/adapter/RemoteReceiverDeviceAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Lcom/example/castapp/ui/adapter/RemoteReceiverDeviceAdapter$ViewHolder;", "receivers", "", "Lcom/example/castapp/model/RemoteReceiverConnection;", "onConnectClick", "Lkotlin/Function1;", "", "onControlClick", "onEditClick", "onDeleteClick", "(Ljava/util/List;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V", "addReceiver", "receiver", "getItemCount", "", "onBindViewHolder", "holder", "position", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "removeReceiver", "updateReceiver", "updatedReceiver", "ViewHolder", "app_debug"})
public final class RemoteReceiverDeviceAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<com.example.castapp.ui.adapter.RemoteReceiverDeviceAdapter.ViewHolder> {
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.castapp.model.RemoteReceiverConnection> receivers = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<com.example.castapp.model.RemoteReceiverConnection, kotlin.Unit> onConnectClick = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<com.example.castapp.model.RemoteReceiverConnection, kotlin.Unit> onControlClick = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<com.example.castapp.model.RemoteReceiverConnection, kotlin.Unit> onEditClick = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<com.example.castapp.model.RemoteReceiverConnection, kotlin.Unit> onDeleteClick = null;
    
    public RemoteReceiverDeviceAdapter(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.castapp.model.RemoteReceiverConnection> receivers, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.castapp.model.RemoteReceiverConnection, kotlin.Unit> onConnectClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.castapp.model.RemoteReceiverConnection, kotlin.Unit> onControlClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.castapp.model.RemoteReceiverConnection, kotlin.Unit> onEditClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.castapp.model.RemoteReceiverConnection, kotlin.Unit> onDeleteClick) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.example.castapp.ui.adapter.RemoteReceiverDeviceAdapter.ViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    com.example.castapp.ui.adapter.RemoteReceiverDeviceAdapter.ViewHolder holder, int position) {
    }
    
    @java.lang.Override()
    public int getItemCount() {
        return 0;
    }
    
    /**
     * 更新接收端状态
     */
    public final void updateReceiver(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.RemoteReceiverConnection updatedReceiver) {
    }
    
    /**
     * 添加接收端
     */
    public final void addReceiver(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.RemoteReceiverConnection receiver) {
    }
    
    /**
     * 删除接收端
     */
    public final void removeReceiver(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.RemoteReceiverConnection receiver) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0007\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\r\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\bR\u0011\u0010\u000f\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\bR\u0011\u0010\u0011\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\fR\u0011\u0010\u0013\u001a\u00020\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\u0017\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\fR\u0011\u0010\u0019\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\b\u00a8\u0006\u001b"}, d2 = {"Lcom/example/castapp/ui/adapter/RemoteReceiverDeviceAdapter$ViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "(Landroid/view/View;)V", "connectButton", "Landroid/widget/Button;", "getConnectButton", "()Landroid/widget/Button;", "connectionStatusText", "Landroid/widget/TextView;", "getConnectionStatusText", "()Landroid/widget/TextView;", "controlButton", "getControlButton", "deleteButton", "getDeleteButton", "deviceAddressText", "getDeviceAddressText", "deviceIcon", "Landroid/widget/ImageView;", "getDeviceIcon", "()Landroid/widget/ImageView;", "deviceNameText", "getDeviceNameText", "editButton", "getEditButton", "app_debug"})
    public static final class ViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final android.widget.ImageView deviceIcon = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView deviceNameText = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView deviceAddressText = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView connectionStatusText = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.Button connectButton = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.Button controlButton = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.Button editButton = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.Button deleteButton = null;
        
        public ViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView) {
            super(null);
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.ImageView getDeviceIcon() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.TextView getDeviceNameText() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.TextView getDeviceAddressText() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.TextView getConnectionStatusText() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.Button getConnectButton() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.Button getControlButton() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.Button getEditButton() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.Button getDeleteButton() {
            return null;
        }
    }
}