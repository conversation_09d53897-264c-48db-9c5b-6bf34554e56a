package com.example.castapp.audio;

/**
 * AAC音频解码器 - 异步模式
 * 🚀 基于MediaCodec.Callback的高性能异步解码实现
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\u0012\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u000b\u0018\u0000 02\u00020\u0001:\u0003/01B=\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0003\u0012\u0018\u0010\u0005\u001a\u0014\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\b0\u0006\u0012\b\b\u0002\u0010\t\u001a\u00020\n\u00a2\u0006\u0002\u0010\u000bJ\b\u0010\u001c\u001a\u00020\bH\u0002J\u000e\u0010\u001d\u001a\u00020\b2\u0006\u0010\u001e\u001a\u00020\u0007J \u0010\u001f\u001a\u00020\b2\u0006\u0010 \u001a\u00020\u001b2\u0006\u0010!\u001a\u00020\u00032\u0006\u0010\"\u001a\u00020#H\u0002J\u0006\u0010\u0018\u001a\u00020\nJ\b\u0010$\u001a\u00020\bH\u0002J\u0018\u0010%\u001a\u00020\b2\u0006\u0010&\u001a\u00020\'2\u0006\u0010\u001e\u001a\u00020\u0007H\u0002J\u0018\u0010(\u001a\u00020\n2\u0006\u0010 \u001a\u00020\u001b2\u0006\u0010)\u001a\u00020\u0003H\u0002J\u000e\u0010*\u001a\u00020\b2\u0006\u0010+\u001a\u00020\u0012J\u0006\u0010,\u001a\u00020\nJ\u0006\u0010-\u001a\u00020\bJ\b\u0010.\u001a\u00020\bH\u0002R\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00030\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0011\u001a\u0004\u0018\u00010\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0003X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00070\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0017X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0017X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001a\u001a\u0004\u0018\u00010\u001bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R \u0010\u0005\u001a\u0014\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\b0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00062"}, d2 = {"Lcom/example/castapp/audio/AudioDecoder;", "", "sampleRate", "", "channelCount", "onDecodedData", "Lkotlin/Function2;", "Lcom/example/castapp/network/DataView;", "", "lowLatencyMode", "", "(IILkotlin/jvm/functions/Function2;Z)V", "availableInputBuffers", "Ljava/util/concurrent/ConcurrentLinkedQueue;", "bufferReuseHits", "", "bufferReuseMisses", "configData", "", "decodedBytes", "decodedFrames", "inputQueue", "isConfigured", "Ljava/util/concurrent/atomic/AtomicBoolean;", "isRunning", "lastLogTime", "mediaCodec", "Landroid/media/MediaCodec;", "configureDecoder", "decode", "aacDataView", "handleDecodedData", "codec", "index", "info", "Landroid/media/MediaCodec$BufferInfo;", "logStatisticsIfNeeded", "optimizedInputBufferWriteFromDataView", "inputBuffer", "Ljava/nio/ByteBuffer;", "processInputData", "bufferIndex", "setConfigData", "config", "start", "stop", "tryProcessPendingData", "AudioDecoderBufferReference", "Companion", "MediaCodecCallback", "app_debug"})
public final class AudioDecoder {
    private final int sampleRate = 0;
    private final int channelCount = 0;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function2<com.example.castapp.network.DataView, java.lang.Integer, kotlin.Unit> onDecodedData = null;
    private final boolean lowLatencyMode = false;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String MIME_TYPE = "audio/mp4a-latm";
    private static final long LOG_INTERVAL_MS = 60000L;
    @org.jetbrains.annotations.Nullable()
    private android.media.MediaCodec mediaCodec;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.atomic.AtomicBoolean isRunning = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.atomic.AtomicBoolean isConfigured = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentLinkedQueue<com.example.castapp.network.DataView> inputQueue = null;
    @org.jetbrains.annotations.Nullable()
    private byte[] configData;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentLinkedQueue<java.lang.Integer> availableInputBuffers = null;
    private long lastLogTime = 0L;
    private int decodedFrames = 0;
    private long decodedBytes = 0L;
    private long bufferReuseHits = 0L;
    private long bufferReuseMisses = 0L;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.audio.AudioDecoder.Companion Companion = null;
    
    public AudioDecoder(int sampleRate, int channelCount, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super com.example.castapp.network.DataView, ? super java.lang.Integer, kotlin.Unit> onDecodedData, boolean lowLatencyMode) {
        super();
    }
    
    /**
     * 设置配置数据（AudioSpecificConfig）
     */
    public final void setConfigData(@org.jetbrains.annotations.NotNull()
    byte[] config) {
    }
    
    /**
     * 配置异步解码器
     */
    private final void configureDecoder() {
    }
    
    /**
     * 启动异步解码器
     */
    public final boolean start() {
        return false;
    }
    
    /**
     * 停止异步解码器
     */
    public final void stop() {
    }
    
    /**
     * 🚀 零拷贝优化：输入AAC DataView进行解码 - 异步模式
     */
    public final void decode(@org.jetbrains.annotations.NotNull()
    com.example.castapp.network.DataView aacDataView) {
    }
    
    /**
     * 🚀 关键方法：尝试处理待处理的数据
     * 当新数据加入队列时，检查是否有可用的输入缓冲区并立即处理
     */
    private final void tryProcessPendingData() {
    }
    
    /**
     * 🚀 零拷贝优化：处理输入数据 - 支持DataView
     * @return true 如果成功处理了数据，false 如果没有数据可处理
     */
    private final boolean processInputData(android.media.MediaCodec codec, int bufferIndex) {
        return false;
    }
    
    /**
     * 处理解码后的数据 - 🚀 零拷贝优化版本
     */
    private final void handleDecodedData(android.media.MediaCodec codec, int index, android.media.MediaCodec.BufferInfo info) {
    }
    
    /**
     * 🚀 零拷贝优化：直接从DataView写入MediaCodec缓冲区
     */
    private final void optimizedInputBufferWriteFromDataView(java.nio.ByteBuffer inputBuffer, com.example.castapp.network.DataView aacDataView) {
    }
    
    /**
     * 🚀 CPU优化：简化统计信息计算，减少浮点运算
     */
    private final void logStatisticsIfNeeded() {
    }
    
    /**
     * 获取解码器状态
     */
    public final boolean isRunning() {
        return false;
    }
    
    /**
     * 🚀 零拷贝核心：音频解码缓冲区引用管理
     * 延迟释放MediaCodec缓冲区直到数据处理完成
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\b\u0002\u0018\u00002\u00020\u0001B%\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\tJ\u0006\u0010\u0012\u001a\u00020\u0013R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0014"}, d2 = {"Lcom/example/castapp/audio/AudioDecoder$AudioDecoderBufferReference;", "", "outputBuffer", "Ljava/nio/ByteBuffer;", "codec", "Landroid/media/MediaCodec;", "bufferIndex", "", "dataSize", "(Ljava/nio/ByteBuffer;Landroid/media/MediaCodec;II)V", "dataView", "Lcom/example/castapp/network/DataView;", "getDataView", "()Lcom/example/castapp/network/DataView;", "isReleased", "", "refCount", "Ljava/util/concurrent/atomic/AtomicInteger;", "release", "", "app_debug"})
    static final class AudioDecoderBufferReference {
        @org.jetbrains.annotations.NotNull()
        private final java.nio.ByteBuffer outputBuffer = null;
        @org.jetbrains.annotations.NotNull()
        private final android.media.MediaCodec codec = null;
        private final int bufferIndex = 0;
        private final int dataSize = 0;
        @org.jetbrains.annotations.NotNull()
        private final java.util.concurrent.atomic.AtomicInteger refCount = null;
        private boolean isReleased = false;
        
        /**
         * 获取只读数据视图，完全避免数据拷贝
         */
        @org.jetbrains.annotations.NotNull()
        private final com.example.castapp.network.DataView dataView = null;
        
        public AudioDecoderBufferReference(@org.jetbrains.annotations.NotNull()
        java.nio.ByteBuffer outputBuffer, @org.jetbrains.annotations.NotNull()
        android.media.MediaCodec codec, int bufferIndex, int dataSize) {
            super();
        }
        
        /**
         * 获取只读数据视图，完全避免数据拷贝
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.network.DataView getDataView() {
            return null;
        }
        
        /**
         * 减少引用计数，当计数为0时释放MediaCodec缓冲区
         */
        public final void release() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0007"}, d2 = {"Lcom/example/castapp/audio/AudioDecoder$Companion;", "", "()V", "LOG_INTERVAL_MS", "", "MIME_TYPE", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
    
    /**
     * MediaCodec异步回调处理器
     * 🚀 异步模式：系统驱动的高效解码处理
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0082\u0004\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0018\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0016J\u0018\u0010\t\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u000bH\u0016J \u0010\f\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\r\u001a\u00020\u000eH\u0016J\u0018\u0010\u000f\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0010\u001a\u00020\u0011H\u0016\u00a8\u0006\u0012"}, d2 = {"Lcom/example/castapp/audio/AudioDecoder$MediaCodecCallback;", "Landroid/media/MediaCodec$Callback;", "(Lcom/example/castapp/audio/AudioDecoder;)V", "onError", "", "codec", "Landroid/media/MediaCodec;", "e", "Landroid/media/MediaCodec$CodecException;", "onInputBufferAvailable", "index", "", "onOutputBufferAvailable", "info", "Landroid/media/MediaCodec$BufferInfo;", "onOutputFormatChanged", "format", "Landroid/media/MediaFormat;", "app_debug"})
    final class MediaCodecCallback extends android.media.MediaCodec.Callback {
        
        public MediaCodecCallback() {
            super();
        }
        
        @java.lang.Override()
        public void onInputBufferAvailable(@org.jetbrains.annotations.NotNull()
        android.media.MediaCodec codec, int index) {
        }
        
        @java.lang.Override()
        public void onOutputBufferAvailable(@org.jetbrains.annotations.NotNull()
        android.media.MediaCodec codec, int index, @org.jetbrains.annotations.NotNull()
        android.media.MediaCodec.BufferInfo info) {
        }
        
        @java.lang.Override()
        public void onError(@org.jetbrains.annotations.NotNull()
        android.media.MediaCodec codec, @org.jetbrains.annotations.NotNull()
        android.media.MediaCodec.CodecException e) {
        }
        
        @java.lang.Override()
        public void onOutputFormatChanged(@org.jetbrains.annotations.NotNull()
        android.media.MediaCodec codec, @org.jetbrains.annotations.NotNull()
        android.media.MediaFormat format) {
        }
    }
}