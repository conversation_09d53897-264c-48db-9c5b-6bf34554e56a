package com.example.castapp.websocket;

/**
 * WebSocket客户端
 * 用于发送端连接到接收端的WebSocket服务器
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000l\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0012\n\u0002\b\u0013\n\u0002\u0010\t\n\u0002\b\u0005\u0018\u0000 >2\u00020\u0001:\u0001>BA\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0014\b\u0002\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\t0\u0007\u0012\u0014\b\u0002\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\t0\u0007\u00a2\u0006\u0002\u0010\fJ\u0006\u0010\u0014\u001a\u00020\u000bJ\u0006\u0010\u0015\u001a\u00020\tJ \u0010\u0016\u001a\u00020\t2\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u00052\u0006\u0010\u001a\u001a\u00020\u000bH\u0016J\u0014\u0010\u001b\u001a\u00020\t2\n\u0010\u001c\u001a\u00060\u001dj\u0002`\u001eH\u0016J\u0010\u0010\u001f\u001a\u00020\t2\u0006\u0010 \u001a\u00020\u0005H\u0016J\u0010\u0010!\u001a\u00020\t2\u0006\u0010\"\u001a\u00020#H\u0016J\u0010\u0010$\u001a\u00020\u000b2\b\u0010%\u001a\u0004\u0018\u00010&J\u001a\u0010\'\u001a\u00020\u000b2\u0006\u0010(\u001a\u00020\u00182\n\b\u0002\u0010)\u001a\u0004\u0018\u00010\u0005J\u0018\u0010*\u001a\u00020\u000b2\u0006\u0010+\u001a\u00020\u000b2\b\b\u0002\u0010\u0019\u001a\u00020\u0005J\u0006\u0010,\u001a\u00020\u000bJ\u001a\u0010-\u001a\u00020\u000b2\b\u0010.\u001a\u0004\u0018\u00010&2\b\u0010/\u001a\u0004\u0018\u00010&J*\u00100\u001a\u00020\u000b2\b\u0010.\u001a\u0004\u0018\u00010&2\b\u0010/\u001a\u0004\u0018\u00010&2\u0006\u00101\u001a\u00020\u00182\u0006\u00102\u001a\u00020\u0018J2\u00103\u001a\u00020\u000b2\b\u0010.\u001a\u0004\u0018\u00010&2\b\u0010/\u001a\u0004\u0018\u00010&2\u0006\u00101\u001a\u00020\u00182\u0006\u00102\u001a\u00020\u00182\u0006\u00104\u001a\u00020\u0018J\u000e\u00105\u001a\u00020\u000b2\u0006\u0010 \u001a\u00020\bJ\u0010\u00106\u001a\u00020\u000b2\u0006\u0010 \u001a\u00020\bH\u0002J\u0016\u00107\u001a\u00020\u000b2\u0006\u00101\u001a\u00020\u00182\u0006\u00102\u001a\u00020\u0018J\u000e\u00108\u001a\u00020\u000b2\u0006\u00109\u001a\u00020:J\u0006\u0010;\u001a\u00020\u000bJ\b\u0010<\u001a\u00020\tH\u0002J\b\u0010=\u001a\u00020\tH\u0002R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000f\u001a\u0004\u0018\u00010\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\t0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\t0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0012X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006?"}, d2 = {"Lcom/example/castapp/websocket/WebSocketClient;", "Lorg/java_websocket/client/WebSocketClient;", "serverUri", "Ljava/net/URI;", "connectionId", "", "onMessageReceived", "Lkotlin/Function1;", "Lcom/example/castapp/websocket/ControlMessage;", "", "onConnectionStateChanged", "", "(Ljava/net/URI;Ljava/lang/String;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V", "connectionLatch", "Ljava/util/concurrent/CountDownLatch;", "heartbeatThread", "Ljava/lang/Thread;", "isConnected", "Ljava/util/concurrent/atomic/AtomicBoolean;", "shouldSendHeartbeat", "connectToServer", "disconnect", "onClose", "code", "", "reason", "remote", "onError", "ex", "Ljava/lang/Exception;", "Lkotlin/Exception;", "onMessage", "message", "onOpen", "handshake", "Lorg/java_websocket/handshake/ServerHandshake;", "sendAacConfig", "configData", "", "sendBitrateControl", "bitrate", "targetConnectionId", "sendCastingState", "isCasting", "sendDisconnect", "sendH264Config", "spsData", "ppsData", "sendH264ConfigWithResolution", "width", "height", "sendH264ConfigWithResolutionAndOrientation", "orientation", "sendMessage", "sendMessageInternal", "sendScreenResolution", "sendSsrcMapping", "ssrc", "", "sendVideoStreamStop", "startHeartbeat", "stopHeartbeat", "Companion", "app_debug"})
public final class WebSocketClient extends org.java_websocket.client.WebSocketClient {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String connectionId = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<com.example.castapp.websocket.ControlMessage, kotlin.Unit> onMessageReceived = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<java.lang.Boolean, kotlin.Unit> onConnectionStateChanged = null;
    private static final long CONNECTION_TIMEOUT = 10000L;
    private static final long HEARTBEAT_INTERVAL = 30000L;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.atomic.AtomicBoolean isConnected = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.CountDownLatch connectionLatch = null;
    @org.jetbrains.annotations.Nullable()
    private java.lang.Thread heartbeatThread;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.atomic.AtomicBoolean shouldSendHeartbeat = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.websocket.WebSocketClient.Companion Companion = null;
    
    public WebSocketClient(@org.jetbrains.annotations.NotNull()
    java.net.URI serverUri, @org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.castapp.websocket.ControlMessage, kotlin.Unit> onMessageReceived, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onConnectionStateChanged) {
        super(null);
    }
    
    @java.lang.Override()
    public void onOpen(@org.jetbrains.annotations.NotNull()
    org.java_websocket.handshake.ServerHandshake handshake) {
    }
    
    @java.lang.Override()
    public void onMessage(@org.jetbrains.annotations.NotNull()
    java.lang.String message) {
    }
    
    @java.lang.Override()
    public void onClose(int code, @org.jetbrains.annotations.NotNull()
    java.lang.String reason, boolean remote) {
    }
    
    @java.lang.Override()
    public void onError(@org.jetbrains.annotations.NotNull()
    java.lang.Exception ex) {
    }
    
    /**
     * 连接到服务器
     * 优化：减少连接超时时间，避免长时间阻塞
     */
    public final boolean connectToServer() {
        return false;
    }
    
    /**
     * 发送控制消息（公共方法）
     */
    public final boolean sendMessage(@org.jetbrains.annotations.NotNull()
    com.example.castapp.websocket.ControlMessage message) {
        return false;
    }
    
    /**
     * 发送控制消息（内部方法）
     */
    private final boolean sendMessageInternal(com.example.castapp.websocket.ControlMessage message) {
        return false;
    }
    
    /**
     * 发送屏幕分辨率信息（统一ID架构）
     */
    public final boolean sendScreenResolution(int width, int height) {
        return false;
    }
    
    /**
     * 发送断开连接消息（统一ID架构）
     */
    public final boolean sendDisconnect() {
        return false;
    }
    
    /**
     * 发送H.264配置数据（统一ID架构）
     */
    public final boolean sendH264Config(@org.jetbrains.annotations.Nullable()
    byte[] spsData, @org.jetbrains.annotations.Nullable()
    byte[] ppsData) {
        return false;
    }
    
    /**
     * 🚀 新增：发送包含分辨率信息的H.264配置数据（统一ID架构）
     * 提供WebSocket分辨率传递机制，作为SPS解析的补充验证
     */
    public final boolean sendH264ConfigWithResolution(@org.jetbrains.annotations.Nullable()
    byte[] spsData, @org.jetbrains.annotations.Nullable()
    byte[] ppsData, int width, int height) {
        return false;
    }
    
    /**
     * 🎯 横竖屏适配：发送包含分辨率和方向信息的H.264配置数据
     */
    public final boolean sendH264ConfigWithResolutionAndOrientation(@org.jetbrains.annotations.Nullable()
    byte[] spsData, @org.jetbrains.annotations.Nullable()
    byte[] ppsData, int width, int height, int orientation) {
        return false;
    }
    
    /**
     * 发送AAC配置数据（统一ID架构）
     */
    public final boolean sendAacConfig(@org.jetbrains.annotations.Nullable()
    byte[] configData) {
        return false;
    }
    
    /**
     * 发送SSRC映射信息（统一ID架构）
     */
    public final boolean sendSsrcMapping(long ssrc) {
        return false;
    }
    
    /**
     * 发送投屏状态同步消息（统一ID架构）
     */
    public final boolean sendCastingState(boolean isCasting, @org.jetbrains.annotations.NotNull()
    java.lang.String reason) {
        return false;
    }
    
    /**
     * 发送视频流停止消息（统一ID架构）
     */
    public final boolean sendVideoStreamStop() {
        return false;
    }
    
    /**
     * 发送码率控制消息（统一ID架构）
     */
    public final boolean sendBitrateControl(int bitrate, @org.jetbrains.annotations.Nullable()
    java.lang.String targetConnectionId) {
        return false;
    }
    
    /**
     * 启动心跳
     */
    private final void startHeartbeat() {
    }
    
    /**
     * 停止心跳
     */
    private final void stopHeartbeat() {
    }
    
    /**
     * 断开连接
     */
    public final void disconnect() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0006"}, d2 = {"Lcom/example/castapp/websocket/WebSocketClient$Companion;", "", "()V", "CONNECTION_TIMEOUT", "", "HEARTBEAT_INTERVAL", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}