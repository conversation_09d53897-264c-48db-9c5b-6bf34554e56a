package com.example.castapp.ui.view;

/**
 * 🎯 可视化窗口裁剪覆盖层
 * 为可视化窗口提供裁剪功能的UI覆盖层
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000l\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0006\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u000f\n\u0002\u0018\u0002\n\u0002\b\n\u0018\u00002\u00020\u0001:\u0002?@B-\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u00a2\u0006\u0002\u0010\nJ\b\u0010 \u001a\u00020!H\u0002J\u0018\u0010\"\u001a\u00020\u00142\u0006\u0010#\u001a\u00020\u001a2\u0006\u0010$\u001a\u00020\u001aH\u0002J\u0010\u0010%\u001a\u00020!2\u0006\u0010&\u001a\u00020\'H\u0002J8\u0010(\u001a\u00020!2\u0006\u0010&\u001a\u00020\'2\u0006\u0010#\u001a\u00020\u001a2\u0006\u0010$\u001a\u00020\u001a2\u0006\u0010)\u001a\u00020\u001a2\u0006\u0010*\u001a\u00020\u00172\u0006\u0010+\u001a\u00020\u0017H\u0002J\u0010\u0010,\u001a\u00020!2\u0006\u0010&\u001a\u00020\'H\u0002J\b\u0010-\u001a\u00020\u0012H\u0002J\b\u0010.\u001a\u00020!H\u0002J\u0010\u0010/\u001a\u00020!2\u0006\u0010&\u001a\u00020\'H\u0014J(\u00100\u001a\u00020!2\u0006\u00101\u001a\u00020\t2\u0006\u00102\u001a\u00020\t2\u0006\u00103\u001a\u00020\t2\u0006\u00104\u001a\u00020\tH\u0014J\u0010\u00105\u001a\u00020\u00172\u0006\u00106\u001a\u000207H\u0016J\u0018\u00108\u001a\u00020!2\u0006\u00109\u001a\u00020\u001a2\u0006\u0010:\u001a\u00020\u001aH\u0002J\b\u0010;\u001a\u00020!H\u0002J\u000e\u0010<\u001a\u00020!2\u0006\u0010=\u001a\u00020\u0012J\b\u0010>\u001a\u00020!H\u0002R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\r\u001a\u0004\u0018\u00010\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000f\u001a\u0004\u0018\u00010\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0017X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0017X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\u001aX\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001e\u001a\u00020\u001aX\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001f\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006A"}, d2 = {"Lcom/example/castapp/ui/view/CropVisualizationOverlay;", "Landroid/widget/FrameLayout;", "context", "Landroid/content/Context;", "windowData", "Lcom/example/castapp/model/WindowVisualizationData;", "attrs", "Landroid/util/AttributeSet;", "defStyleAttr", "", "(Landroid/content/Context;Lcom/example/castapp/model/WindowVisualizationData;Landroid/util/AttributeSet;I)V", "borderPaint", "Landroid/graphics/Paint;", "controlButtons", "Landroid/widget/LinearLayout;", "cropChangeListener", "Lcom/example/castapp/ui/view/CropVisualizationOverlay$CropChangeListener;", "cropRect", "Landroid/graphics/RectF;", "dragMode", "Lcom/example/castapp/ui/view/CropVisualizationOverlay$DragMode;", "handlePaint", "hasManuallySetInitialCrop", "", "isDragging", "lastTouchX", "", "lastTouchY", "maskPaint", "minCropSize", "touchThreshold", "windowBounds", "createControlButtons", "", "detectDragMode", "x", "y", "drawHandles", "canvas", "Landroid/graphics/Canvas;", "drawLHandle", "size", "isLeft", "isTop", "drawMask", "getCropRatio", "notifyCropChange", "onDraw", "onSizeChanged", "w", "h", "oldw", "oldh", "onTouchEvent", "event", "Landroid/view/MotionEvent;", "performCropResize", "deltaX", "deltaY", "resetCrop", "setInitialCropRatio", "cropRatio", "setupInitialCropRect", "CropChangeListener", "DragMode", "app_debug"})
public final class CropVisualizationOverlay extends android.widget.FrameLayout {
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.model.WindowVisualizationData windowData = null;
    @org.jetbrains.annotations.NotNull()
    private android.graphics.RectF cropRect;
    @org.jetbrains.annotations.NotNull()
    private android.graphics.RectF windowBounds;
    @org.jetbrains.annotations.NotNull()
    private final android.graphics.Paint borderPaint = null;
    @org.jetbrains.annotations.NotNull()
    private final android.graphics.Paint maskPaint = null;
    @org.jetbrains.annotations.NotNull()
    private final android.graphics.Paint handlePaint = null;
    private boolean isDragging = false;
    @org.jetbrains.annotations.NotNull()
    private com.example.castapp.ui.view.CropVisualizationOverlay.DragMode dragMode = com.example.castapp.ui.view.CropVisualizationOverlay.DragMode.NONE;
    private float lastTouchX = 0.0F;
    private float lastTouchY = 0.0F;
    private final float touchThreshold = 30.0F;
    private final float minCropSize = 50.0F;
    @org.jetbrains.annotations.Nullable()
    private android.widget.LinearLayout controlButtons;
    private boolean hasManuallySetInitialCrop = false;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.view.CropVisualizationOverlay.CropChangeListener cropChangeListener;
    
    @kotlin.jvm.JvmOverloads()
    public CropVisualizationOverlay(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.example.castapp.model.WindowVisualizationData windowData, @org.jetbrains.annotations.Nullable()
    android.util.AttributeSet attrs, int defStyleAttr) {
        super(null);
    }
    
    @java.lang.Override()
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
    }
    
    private final void setupInitialCropRect() {
    }
    
    /**
     * 🎯 设置初始裁剪区域（用于恢复之前的裁剪框位置）
     */
    public final void setInitialCropRatio(@org.jetbrains.annotations.NotNull()
    android.graphics.RectF cropRatio) {
    }
    
    @java.lang.Override()
    protected void onDraw(@org.jetbrains.annotations.NotNull()
    android.graphics.Canvas canvas) {
    }
    
    private final void drawMask(android.graphics.Canvas canvas) {
    }
    
    private final void drawHandles(android.graphics.Canvas canvas) {
    }
    
    private final void drawLHandle(android.graphics.Canvas canvas, float x, float y, float size, boolean isLeft, boolean isTop) {
    }
    
    @java.lang.Override()
    public boolean onTouchEvent(@org.jetbrains.annotations.NotNull()
    android.view.MotionEvent event) {
        return false;
    }
    
    private final com.example.castapp.ui.view.CropVisualizationOverlay.DragMode detectDragMode(float x, float y) {
        return null;
    }
    
    private final void performCropResize(float deltaX, float deltaY) {
    }
    
    private final void notifyCropChange() {
    }
    
    private final android.graphics.RectF getCropRatio() {
        return null;
    }
    
    private final void createControlButtons() {
    }
    
    private final void resetCrop() {
    }
    
    @kotlin.jvm.JvmOverloads()
    public CropVisualizationOverlay(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.example.castapp.model.WindowVisualizationData windowData) {
        super(null);
    }
    
    @kotlin.jvm.JvmOverloads()
    public CropVisualizationOverlay(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.example.castapp.model.WindowVisualizationData windowData, @org.jetbrains.annotations.Nullable()
    android.util.AttributeSet attrs) {
        super(null);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\b\u0010\u0006\u001a\u00020\u0003H&J\u0010\u0010\u0007\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\b\u0010\b\u001a\u00020\u0003H&\u00a8\u0006\t"}, d2 = {"Lcom/example/castapp/ui/view/CropVisualizationOverlay$CropChangeListener;", "", "onCropApplied", "", "cropRatio", "Landroid/graphics/RectF;", "onCropCancelled", "onCropChanged", "onCropReset", "app_debug"})
    public static abstract interface CropChangeListener {
        
        public abstract void onCropChanged(@org.jetbrains.annotations.NotNull()
        android.graphics.RectF cropRatio);
        
        public abstract void onCropApplied(@org.jetbrains.annotations.NotNull()
        android.graphics.RectF cropRatio);
        
        public abstract void onCropCancelled();
        
        public abstract void onCropReset();
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\f\b\u0082\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\f\u00a8\u0006\r"}, d2 = {"Lcom/example/castapp/ui/view/CropVisualizationOverlay$DragMode;", "", "(Ljava/lang/String;I)V", "NONE", "MOVE", "LEFT", "RIGHT", "TOP", "BOTTOM", "TOP_LEFT", "TOP_RIGHT", "BOTTOM_LEFT", "BOTTOM_RIGHT", "app_debug"})
    static enum DragMode {
        /*public static final*/ NONE /* = new NONE() */,
        /*public static final*/ MOVE /* = new MOVE() */,
        /*public static final*/ LEFT /* = new LEFT() */,
        /*public static final*/ RIGHT /* = new RIGHT() */,
        /*public static final*/ TOP /* = new TOP() */,
        /*public static final*/ BOTTOM /* = new BOTTOM() */,
        /*public static final*/ TOP_LEFT /* = new TOP_LEFT() */,
        /*public static final*/ TOP_RIGHT /* = new TOP_RIGHT() */,
        /*public static final*/ BOTTOM_LEFT /* = new BOTTOM_LEFT() */,
        /*public static final*/ BOTTOM_RIGHT /* = new BOTTOM_RIGHT() */;
        
        DragMode() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.example.castapp.ui.view.CropVisualizationOverlay.DragMode> getEntries() {
            return null;
        }
    }
}