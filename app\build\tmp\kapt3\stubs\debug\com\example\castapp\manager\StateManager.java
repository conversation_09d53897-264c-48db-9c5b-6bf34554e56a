package com.example.castapp.manager;

/**
 * 状态管理器 - 解决多层状态管理导致的状态不一致问题
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000l\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\"\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b%\u0018\u0000 K2\u00020\u0001:\u0001KB\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010(\u001a\u00020\u000b2\u0006\u0010)\u001a\u00020\b2\u0006\u0010*\u001a\u00020\u000fJ\u000e\u0010+\u001a\u00020\u000b2\u0006\u0010,\u001a\u00020\u000bJ(\u0010-\u001a\u00020\u00172\u0006\u0010.\u001a\u00020\b2\u0018\u0010/\u001a\u0014\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u00170!J\u0006\u00100\u001a\u00020\u0017J\u0010\u00101\u001a\u0004\u0018\u00010\u000b2\u0006\u00102\u001a\u00020\bJ\f\u00103\u001a\b\u0012\u0004\u0012\u00020\u000b0\nJ\u0010\u00104\u001a\u0004\u0018\u00010\u000b2\u0006\u00102\u001a\u00020\bJ\u0010\u00105\u001a\u0004\u0018\u00010\b2\u0006\u00102\u001a\u00020\bJ\u0018\u00106\u001a\u00020\u00172\u0006\u00102\u001a\u00020\b2\b\b\u0002\u00107\u001a\u00020\bJ\u000e\u00108\u001a\u00020\u00172\u0006\u00102\u001a\u00020\bJ\b\u00109\u001a\u00020\u0017H\u0002J(\u0010:\u001a\u00020\u00172\u0006\u00102\u001a\u00020\b2\u0018\u0010/\u001a\u0014\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u00170!J\u000e\u0010;\u001a\u00020\u00172\u0006\u0010,\u001a\u00020\u000bJ\u000e\u0010<\u001a\u00020\u00172\u0006\u00102\u001a\u00020\bJ\u000e\u0010=\u001a\u00020\u00172\u0006\u0010.\u001a\u00020\bJ\b\u0010>\u001a\u00020\u0017H\u0002J\u000e\u0010?\u001a\u00020\u00172\u0006\u00102\u001a\u00020\bJ\"\u0010@\u001a\u00020\u00172\u0006\u00102\u001a\u00020\b2\u0012\u0010A\u001a\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u000b0\u0016J\u0016\u0010B\u001a\u00020\u00172\u0006\u00102\u001a\u00020\b2\u0006\u0010C\u001a\u00020\bJ$\u0010D\u001a\u00020\u00172\u0006\u00102\u001a\u00020\b2\u0012\u0010A\u001a\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u000b0\u0016H\u0002J\u000e\u0010E\u001a\u00020\u00172\u0006\u0010,\u001a\u00020\u000bJ\b\u0010F\u001a\u00020\u0017H\u0002J\'\u0010G\u001a\u00020\u00172\u0006\u0010H\u001a\u00020\b2\u0006\u0010I\u001a\u00020\r2\n\b\u0002\u0010*\u001a\u0004\u0018\u00010\u000f\u00a2\u0006\u0002\u0010JR\u001a\u0010\u0005\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\t\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\n0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\r0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0010\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R&\u0010\u0014\u001a\u001a\u0012\u0004\u0012\u00020\b\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u00170\u00160\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0018\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\n0\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0013R\u0014\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u000b0\u001bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u001c\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\b0\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\r0\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0013R\u000e\u0010\u001e\u001a\u00020\u001fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R,\u0010 \u001a \u0012\u0004\u0012\u00020\b\u0012\u0016\u0012\u0014\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u00170!0\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u0013R,\u0010$\u001a \u0012\u0004\u0012\u00020\b\u0012\u0016\u0012\u0014\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u00170!0\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010%\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\r0\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010&\u001a\u00020\'X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006L"}, d2 = {"Lcom/example/castapp/manager/StateManager;", "", "context", "Landroid/app/Application;", "(Landroid/app/Application;)V", "_castingConnections", "Landroidx/lifecycle/MutableLiveData;", "", "", "_connections", "", "Lcom/example/castapp/model/Connection;", "_isReceivingServiceRunning", "", "_receivingPort", "", "castingConnections", "Landroidx/lifecycle/LiveData;", "getCastingConnections", "()Landroidx/lifecycle/LiveData;", "connectionChangeListeners", "Ljava/util/concurrent/ConcurrentHashMap;", "Lkotlin/Function1;", "", "connections", "getConnections", "connectionsList", "Ljava/util/concurrent/CopyOnWriteArrayList;", "deviceNames", "isReceivingServiceRunning", "managerScope", "Lkotlinx/coroutines/CoroutineScope;", "preciseStateChangeListeners", "Lkotlin/Function2;", "receivingPort", "getReceivingPort", "serviceStateListeners", "serviceStates", "sharedPreferences", "Landroid/content/SharedPreferences;", "addConnection", "ipAddress", "port", "addExistingConnection", "connection", "addServiceStateListener", "listenerId", "listener", "cleanup", "findConnectionById", "connectionId", "getAllConnections", "getConnection", "getConnectionDeviceName", "handleConnectionDisconnected", "reason", "handleWebSocketDisconnected", "loadState", "registerPreciseStateChangeListener", "removeConnection", "removeConnectionById", "removeServiceStateListener", "saveState", "unregisterPreciseStateChangeListener", "updateConnection", "updateBlock", "updateConnectionDeviceName", "deviceName", "updateConnectionInternal", "updateExistingConnection", "updateLiveData", "updateServiceState", "serviceName", "isRunning", "(Ljava/lang/String;ZLjava/lang/Integer;)V", "Companion", "app_debug"})
public final class StateManager {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String PREFS_NAME = "state_prefs";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_CONNECTIONS = "connections";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_SERVICE_STATES = "service_states";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_DEVICE_NAMES = "device_names";
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.example.castapp.manager.StateManager INSTANCE;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.CoroutineScope managerScope = null;
    @org.jetbrains.annotations.NotNull()
    private final android.content.SharedPreferences sharedPreferences = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.CopyOnWriteArrayList<com.example.castapp.model.Connection> connectionsList = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, java.lang.String> deviceNames = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, java.lang.Boolean> serviceStates = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.util.List<com.example.castapp.model.Connection>> _connections = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.util.List<com.example.castapp.model.Connection>> connections = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.util.Set<java.lang.String>> _castingConnections = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.util.Set<java.lang.String>> castingConnections = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.Boolean> _isReceivingServiceRunning = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.Boolean> isReceivingServiceRunning = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.Integer> _receivingPort = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.Integer> receivingPort = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, kotlin.jvm.functions.Function1<com.example.castapp.model.Connection, kotlin.Unit>> connectionChangeListeners = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, kotlin.jvm.functions.Function2<java.lang.String, java.lang.Boolean, kotlin.Unit>> serviceStateListeners = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, kotlin.jvm.functions.Function2<java.lang.String, com.example.castapp.model.Connection, kotlin.Unit>> preciseStateChangeListeners = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.manager.StateManager.Companion Companion = null;
    
    private StateManager(android.app.Application context) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.List<com.example.castapp.model.Connection>> getConnections() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.Set<java.lang.String>> getCastingConnections() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.Boolean> isReceivingServiceRunning() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.Integer> getReceivingPort() {
        return null;
    }
    
    /**
     * 添加连接 - 原子性操作（同步版本，避免阻塞主线程）
     */
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.model.Connection addConnection(@org.jetbrains.annotations.NotNull()
    java.lang.String ipAddress, int port) {
        return null;
    }
    
    /**
     * 添加已存在的连接对象到StateManager - 简化版
     */
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.model.Connection addExistingConnection(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.Connection connection) {
        return null;
    }
    
    /**
     * 更新已存在的连接对象
     */
    public final void updateExistingConnection(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.Connection connection) {
    }
    
    /**
     * 移除连接 - 原子性操作
     */
    public final void removeConnection(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.Connection connection) {
    }
    
    /**
     * 通过connectionId移除连接 - 简化版
     */
    public final void removeConnectionById(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 更新连接状态 - 简化版（直接操作Connection对象）
     */
    public final void updateConnection(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.castapp.model.Connection, com.example.castapp.model.Connection> updateBlock) {
    }
    
    /**
     * 内部连接更新方法 - 🚀 优化：添加精准状态变化通知
     */
    private final void updateConnectionInternal(java.lang.String connectionId, kotlin.jvm.functions.Function1<? super com.example.castapp.model.Connection, com.example.castapp.model.Connection> updateBlock) {
    }
    
    /**
     * 统一的连接断开处理 - 🚀 根源优化：移除防重复机制，从源头避免重复调用
     */
    public final void handleConnectionDisconnected(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    java.lang.String reason) {
    }
    
    /**
     * 处理WebSocket断开连接事件 - 简化版（调用统一处理）
     */
    public final void handleWebSocketDisconnected(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 获取连接（替代getConnectionState）
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.model.Connection getConnection(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
        return null;
    }
    
    /**
     * 通过连接ID查找连接
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.model.Connection findConnectionById(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
        return null;
    }
    
    /**
     * 获取所有连接
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.castapp.model.Connection> getAllConnections() {
        return null;
    }
    
    /**
     * 更新连接的设备名称
     */
    public final void updateConnectionDeviceName(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    java.lang.String deviceName) {
    }
    
    /**
     * 获取连接的设备名称
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getConnectionDeviceName(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
        return null;
    }
    
    /**
     * 更新服务状态
     */
    public final void updateServiceState(@org.jetbrains.annotations.NotNull()
    java.lang.String serviceName, boolean isRunning, @org.jetbrains.annotations.Nullable()
    java.lang.Integer port) {
    }
    
    /**
     * 更新LiveData（线程安全版本）
     */
    private final void updateLiveData() {
    }
    
    /**
     * 保存状态到SharedPreferences
     */
    private final void saveState() {
    }
    
    /**
     * 从SharedPreferences加载状态
     */
    private final void loadState() {
    }
    
    /**
     * 🚀 新增：注册精准状态变化监听器
     */
    public final void registerPreciseStateChangeListener(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super com.example.castapp.model.Connection, kotlin.Unit> listener) {
    }
    
    /**
     * 🚀 新增：移除精准状态变化监听器
     */
    public final void unregisterPreciseStateChangeListener(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 🚀 新增：注册服务状态监听器
     */
    public final void addServiceStateListener(@org.jetbrains.annotations.NotNull()
    java.lang.String listenerId, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.Boolean, kotlin.Unit> listener) {
    }
    
    /**
     * 🚀 新增：移除服务状态监听器
     */
    public final void removeServiceStateListener(@org.jetbrains.annotations.NotNull()
    java.lang.String listenerId) {
    }
    
    /**
     * 清理资源
     */
    public final void cleanup() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\n\u001a\u00020\u00042\u0006\u0010\u000b\u001a\u00020\fR\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/example/castapp/manager/StateManager$Companion;", "", "()V", "INSTANCE", "Lcom/example/castapp/manager/StateManager;", "KEY_CONNECTIONS", "", "KEY_DEVICE_NAMES", "KEY_SERVICE_STATES", "PREFS_NAME", "getInstance", "context", "Landroid/app/Application;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.manager.StateManager getInstance(@org.jetbrains.annotations.NotNull()
        android.app.Application context) {
            return null;
        }
    }
}