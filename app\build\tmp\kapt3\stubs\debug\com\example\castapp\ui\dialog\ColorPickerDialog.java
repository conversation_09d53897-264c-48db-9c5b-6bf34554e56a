package com.example.castapp.ui.dialog;

/**
 * 🎨 HSV色轮颜色选择对话框
 * 使用 skydoves/ColorPickerView 实现专业级颜色选择
 * 支持自定义色板、透明度控制和数值输入
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u008c\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0017\u0018\u00002\u00020\u0001B\u00a9\u0001\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u0012\b\b\u0002\u0010\n\u001a\u00020\u000b\u0012\b\b\u0002\u0010\f\u001a\u00020\t\u0012\b\b\u0002\u0010\r\u001a\u00020\t\u0012\b\b\u0002\u0010\u000e\u001a\u00020\t\u0012\u0012\u0010\u000f\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00110\u0010\u0012\"\b\u0002\u0010\u0012\u001a\u001c\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u0011\u0018\u00010\u0013\u0012\u001c\b\u0002\u0010\u0014\u001a\u0016\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u0011\u0018\u00010\u0015\u00a2\u0006\u0002\u0010\u0016J\b\u0010<\u001a\u00020\u0011H\u0002J\b\u0010=\u001a\u00020\u0011H\u0002J\u0010\u0010>\u001a\u00020\u00112\u0006\u0010?\u001a\u000209H\u0002J\b\u0010@\u001a\u00020\u0011H\u0002J\b\u0010A\u001a\u00020\u0011H\u0002J\u0010\u0010B\u001a\u00020\u00112\u0006\u0010?\u001a\u000209H\u0002J\b\u0010C\u001a\u00020\u0011H\u0002J\b\u0010D\u001a\u00020\u0011H\u0002J\u0006\u0010E\u001a\u00020\u0011J\u0010\u0010F\u001a\u00020\u00112\u0006\u0010G\u001a\u00020\u0005H\u0002J$\u0010H\u001a\u00020\u00112\u0006\u0010G\u001a\u00020\u00052\b\b\u0002\u0010I\u001a\u00020\t2\b\b\u0002\u0010J\u001a\u00020\tH\u0002J\b\u0010K\u001a\u00020\u0011H\u0002J\u0010\u0010L\u001a\u00020\u00112\u0006\u0010G\u001a\u00020\u0005H\u0002J\b\u0010M\u001a\u00020\u0011H\u0002J\u0010\u0010N\u001a\u00020\u00112\u0006\u0010O\u001a\u00020\tH\u0002R\u000e\u0010\u0017\u001a\u00020\u0018X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u0018X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u001bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\u001dX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001e\u001a\u00020\u001fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010 \u001a\u00020!X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\"\u001a\u00020!X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010#\u001a\u00020!X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010$\u001a\u00020!X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010%\u001a\u00020!X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010&\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\'\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u000f\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00110\u0010X\u0082\u0004\u00a2\u0006\u0002\n\u0000R(\u0010\u0012\u001a\u001c\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u0011\u0018\u00010\u0013X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\"\u0010\u0014\u001a\u0016\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u0011\u0018\u00010\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010(\u001a\u00020)X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010*\u001a\u00020\u0005X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010+\u001a\u00020,X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010-\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010.\u001a\u00020\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010/\u001a\u00020,X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u00100\u001a\u000201X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u00102\u001a\u000201X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u00103\u001a\u000204X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u00105\u001a\u000204X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u00106\u001a\u000204X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u00107\u001a\u000204X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u00108\u001a\u000209X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010:\u001a\u00020,X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010;\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006P"}, d2 = {"Lcom/example/castapp/ui/dialog/ColorPickerDialog;", "", "context", "Landroid/content/Context;", "currentColor", "", "title", "", "isStrokeMode", "", "currentStrokeWidth", "", "currentStrokeEnabled", "isWindowColorMode", "currentWindowColorEnabled", "onColorSelected", "Lkotlin/Function1;", "", "onStrokeConfigChanged", "Lkotlin/Function3;", "onWindowColorConfigChanged", "Lkotlin/Function2;", "(Landroid/content/Context;ILjava/lang/String;ZFZZZLkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function3;Lkotlin/jvm/functions/Function2;)V", "btnDeleteColor", "Landroid/widget/Button;", "btnSaveColor", "colorPaletteManager", "Lcom/example/castapp/utils/ColorPaletteManager;", "colorPickerView", "Lcom/skydoves/colorpickerview/ColorPickerView;", "customPaletteAdapter", "Lcom/example/castapp/ui/adapter/CustomColorPaletteAdapter;", "etBInput", "Landroid/widget/EditText;", "etGInput", "etHexInput", "etRInput", "etStrokeWidth", "isUpdatingFromCode", "isUserInputting", "seekbarAlpha", "Landroid/widget/SeekBar;", "selectedColor", "strokeControlContainer", "Landroid/widget/LinearLayout;", "strokeEnabled", "strokeWidth", "strokeWidthContainer", "switchStrokeEnabled", "Landroidx/appcompat/widget/SwitchCompat;", "switchWindowColorEnabled", "tvAlphaValue", "Landroid/widget/TextView;", "tvDialogTitle", "tvHexValue", "tvRgbValue", "viewColorPreview", "Landroid/view/View;", "windowColorControlContainer", "windowColorEnabled", "deleteSelectedColor", "initializeAlphaControl", "initializeCustomPalette", "dialogView", "initializeStrokeControls", "initializeValueInputs", "initializeViews", "initializeWindowColorControls", "saveCurrentColor", "show", "showDeleteColorConfirmation", "color", "updateAllColorDisplays", "skipHexInput", "skipRgbInputs", "updateColorFromRgbInputs", "updateColorPickerSelection", "updatePaletteButtons", "updateStrokeWidthContainerState", "enabled", "app_debug"})
public final class ColorPickerDialog {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    private final int currentColor = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String title = null;
    private final boolean isStrokeMode = false;
    private final boolean isWindowColorMode = false;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<java.lang.Integer, kotlin.Unit> onColorSelected = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlin.jvm.functions.Function3<java.lang.Boolean, java.lang.Float, java.lang.Integer, kotlin.Unit> onStrokeConfigChanged = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlin.jvm.functions.Function2<java.lang.Boolean, java.lang.Integer, kotlin.Unit> onWindowColorConfigChanged = null;
    private int selectedColor;
    private com.example.castapp.utils.ColorPaletteManager colorPaletteManager;
    private com.example.castapp.ui.adapter.CustomColorPaletteAdapter customPaletteAdapter;
    private android.widget.TextView tvDialogTitle;
    private com.skydoves.colorpickerview.ColorPickerView colorPickerView;
    private android.view.View viewColorPreview;
    private android.widget.TextView tvHexValue;
    private android.widget.TextView tvRgbValue;
    private android.widget.SeekBar seekbarAlpha;
    private android.widget.TextView tvAlphaValue;
    private android.widget.EditText etHexInput;
    private android.widget.EditText etRInput;
    private android.widget.EditText etGInput;
    private android.widget.EditText etBInput;
    private android.widget.Button btnSaveColor;
    private android.widget.Button btnDeleteColor;
    private android.widget.LinearLayout strokeControlContainer;
    private androidx.appcompat.widget.SwitchCompat switchStrokeEnabled;
    private android.widget.LinearLayout strokeWidthContainer;
    private android.widget.EditText etStrokeWidth;
    private android.widget.LinearLayout windowColorControlContainer;
    private androidx.appcompat.widget.SwitchCompat switchWindowColorEnabled;
    private boolean isUpdatingFromCode = false;
    private boolean isUserInputting = false;
    private boolean strokeEnabled;
    private float strokeWidth;
    private boolean windowColorEnabled;
    
    public ColorPickerDialog(@org.jetbrains.annotations.NotNull()
    android.content.Context context, int currentColor, @org.jetbrains.annotations.NotNull()
    java.lang.String title, boolean isStrokeMode, float currentStrokeWidth, boolean currentStrokeEnabled, boolean isWindowColorMode, boolean currentWindowColorEnabled, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onColorSelected, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function3<? super java.lang.Boolean, ? super java.lang.Float, ? super java.lang.Integer, kotlin.Unit> onStrokeConfigChanged, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function2<? super java.lang.Boolean, ? super java.lang.Integer, kotlin.Unit> onWindowColorConfigChanged) {
        super();
    }
    
    public final void show() {
    }
    
    /**
     * 🎨 初始化控件引用
     */
    private final void initializeViews(android.view.View dialogView) {
    }
    
    /**
     * 🎨 初始化自定义色板
     */
    private final void initializeCustomPalette(android.view.View dialogView) {
    }
    
    /**
     * 🎨 初始化透明度控制
     */
    private final void initializeAlphaControl() {
    }
    
    /**
     * 🎨 初始化数值输入
     */
    private final void initializeValueInputs() {
    }
    
    /**
     * 🎨 更新所有颜色显示
     */
    private final void updateAllColorDisplays(int color, boolean skipHexInput, boolean skipRgbInputs) {
    }
    
    /**
     * 🎨 从RGB输入框更新颜色
     */
    private final void updateColorFromRgbInputs() {
    }
    
    /**
     * 🎨 更新色轮选择器位置
     */
    private final void updateColorPickerSelection(int color) {
    }
    
    /**
     * 🎨 初始化描边控件
     */
    private final void initializeStrokeControls() {
    }
    
    /**
     * 🎨 更新描边宽度容器状态
     */
    private final void updateStrokeWidthContainerState(boolean enabled) {
    }
    
    /**
     * 🎨 初始化窗口颜色控件
     */
    private final void initializeWindowColorControls() {
    }
    
    /**
     * 🎨 保存当前颜色
     */
    private final void saveCurrentColor() {
    }
    
    /**
     * 🎨 删除选中的颜色
     */
    private final void deleteSelectedColor() {
    }
    
    /**
     * 🎨 显示删除颜色确认对话框
     */
    private final void showDeleteColorConfirmation(int color) {
    }
    
    /**
     * 🎨 更新色板按钮状态
     */
    private final void updatePaletteButtons() {
    }
}