<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="8dp">

    <!-- 连接列表标题和添加按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="6dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="远程接收端设备列表"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="#333333" />

        <TextView
            android:id="@+id/receiver_connection_count_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0个设备"
            android:textSize="12sp"
            android:textColor="#666666"
            android:background="@drawable/rounded_background"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:paddingTop="2dp"
            android:paddingBottom="2dp"
            android:layout_marginEnd="8dp" />

        <!-- 添加连接图标按钮 -->
        <ImageButton
            android:id="@+id/add_receiver_connection_button"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_add"
            android:contentDescription="添加接收端连接"
            android:scaleType="centerInside"
            android:alpha="0.7"
            android:tint="#666666" />

    </LinearLayout>

    <!-- 连接列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/receiver_connections_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:scrollbars="vertical"
        android:scrollbarStyle="insideOverlay"
        android:fadeScrollbars="false"
        android:padding="4dp"
        android:clipToPadding="false" />

</LinearLayout>
