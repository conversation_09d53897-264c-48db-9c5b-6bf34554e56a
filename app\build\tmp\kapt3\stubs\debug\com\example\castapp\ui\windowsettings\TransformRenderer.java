package com.example.castapp.ui.windowsettings;

/**
 * 变换渲染器
 * 负责Matrix变换的具体应用和UI效果渲染
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000n\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u000f\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u001f\u0018\u00002\u00020\u0001B%\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\tJ:\u0010 \u001a\u00020!2\u0006\u0010\"\u001a\u00020\u00102\b\u0010#\u001a\u0004\u0018\u00010\u000e2\u0006\u0010$\u001a\u00020\u00122\u0006\u0010%\u001a\u00020\u00122\u0006\u0010&\u001a\u00020\u00122\u0006\u0010\'\u001a\u00020\u0012H\u0002J\b\u0010(\u001a\u00020!H\u0002J\u0006\u0010)\u001a\u00020!J\u0018\u0010*\u001a\u00020!2\u0006\u0010+\u001a\u00020\u00152\u0006\u0010,\u001a\u00020\u0010H\u0002J\u001a\u0010-\u001a\u00020!2\u0006\u0010\"\u001a\u00020\u00102\b\u0010#\u001a\u0004\u0018\u00010\u000eH\u0002J\b\u0010.\u001a\u00020!H\u0002J,\u0010/\u001a\u00020!2\b\u00100\u001a\u0004\u0018\u0001012\u0006\u0010,\u001a\u00020\u00102\u0006\u0010\"\u001a\u00020\u00102\b\u0010#\u001a\u0004\u0018\u00010\u000eH\u0002J^\u00102\u001a\u00020!2\b\u00100\u001a\u0004\u0018\u0001012\n\b\u0002\u00103\u001a\u0004\u0018\u00010\u00152\u0006\u00104\u001a\u0002052\u0006\u0010\"\u001a\u00020\u00102\b\u0010#\u001a\u0004\u0018\u00010\u000e2\u0006\u0010,\u001a\u00020\u00102\u0006\u0010$\u001a\u00020\u00122\u0006\u0010%\u001a\u00020\u00122\u0006\u0010&\u001a\u00020\u00122\u0006\u0010\'\u001a\u00020\u0012J\u0006\u00106\u001a\u00020!J\u0006\u00107\u001a\u00020!J\u0014\u00108\u001a\u00020!2\n\b\u0002\u00109\u001a\u0004\u0018\u00010:H\u0002J\u0010\u0010;\u001a\u00020\u00072\u0006\u0010<\u001a\u00020\u0007H\u0002J\u0010\u0010=\u001a\u00020\u00122\u0006\u0010<\u001a\u00020\u0012H\u0002J\u0006\u0010>\u001a\u00020\u0007J\u0006\u0010?\u001a\u00020\u0012J\u0006\u0010@\u001a\u00020\u0012J\u0010\u0010A\u001a\u0002052\u0006\u0010B\u001a\u00020\u0007H\u0002J\u0006\u0010C\u001a\u00020\u001cJ\u0006\u0010D\u001a\u00020\u0012J\u0018\u0010E\u001a\u00020!2\u0006\u0010\"\u001a\u00020\u00102\b\u0010#\u001a\u0004\u0018\u00010\u000eJ\u0006\u0010\u0019\u001a\u00020\u0010J\u0006\u0010F\u001a\u00020!J\u0006\u0010G\u001a\u00020!J\b\u0010H\u001a\u00020!H\u0002J\u000e\u0010I\u001a\u00020!2\u0006\u0010J\u001a\u00020\u0007J\u000e\u0010K\u001a\u00020!2\u0006\u0010L\u001a\u00020\u0010J\u000e\u0010M\u001a\u00020!2\u0006\u0010N\u001a\u00020\u0012J\u000e\u0010O\u001a\u00020!2\u0006\u0010P\u001a\u00020\u0012J\"\u0010Q\u001a\u00020!2\u0006\u0010B\u001a\u00020\u00072\b\b\u0002\u0010R\u001a\u00020\u00072\b\b\u0002\u0010S\u001a\u00020\u0007J\u000e\u0010T\u001a\u00020!2\u0006\u0010U\u001a\u00020\u0012J\u000e\u0010V\u001a\u00020!2\u0006\u0010W\u001a\u00020\u0007J\u0006\u0010X\u001a\u00020!R\u000e\u0010\n\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0014\u001a\u0004\u0018\u00010\u0015X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u001cX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\u001eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001f\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006Y"}, d2 = {"Lcom/example/castapp/ui/windowsettings/TransformRenderer;", "", "context", "Landroid/content/Context;", "container", "Landroid/widget/FrameLayout;", "windowWidth", "", "windowHeight", "(Landroid/content/Context;Landroid/widget/FrameLayout;II)V", "borderColor", "borderPaint", "Landroid/graphics/Paint;", "borderRect", "Landroid/graphics/RectF;", "borderStateBeforeCrop", "", "borderWidth", "", "cornerRadius", "cropBorderView", "Landroid/view/View;", "currentVideoHeight", "currentVideoOrientation", "currentVideoWidth", "isBorderEnabled", "isCropBorderExpanded", "positionManager", "Lcom/example/castapp/ui/windowsettings/WindowPositionManager;", "transformMatrix", "Landroid/graphics/Matrix;", "windowAlpha", "applyContainerTransforms", "", "isCroppedWindow", "cropRectRatio", "currentScaleFactor", "currentRotation", "currentPivotX", "currentPivotY", "applyCornerRadius", "applyCornerRadiusForCrop", "applyImageViewTransforms", "imageView", "isMirrored", "applyMirrorTransform", "applyOrientationTransform", "applyTextureViewTransforms", "textureView", "Landroid/view/TextureView;", "applyTransforms", "targetView", "connectionId", "", "bringBorderToFront", "cleanup", "createUnifiedBorderView", "clipBounds", "Landroid/graphics/Rect;", "dpToPx", "dp", "dpToPxFloat", "getBorderColor", "getBorderWidth", "getCornerRadius", "getOrientationName", "orientation", "getPositionManager", "getWindowAlpha", "initializePositionManager", "onEnterCropMode", "onExitCropMode", "removeUnifiedBorderView", "setBorderColor", "color", "setBorderEnabled", "enabled", "setBorderWidth", "width", "setCornerRadius", "radius", "setVideoOrientation", "videoWidth", "videoHeight", "setWindowAlpha", "alphaValue", "syncBorderVisibility", "visibility", "updateUnifiedBorderPosition", "app_debug"})
public final class TransformRenderer {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final android.widget.FrameLayout container = null;
    private final int windowWidth = 0;
    private final int windowHeight = 0;
    @org.jetbrains.annotations.NotNull()
    private final android.graphics.Matrix transformMatrix = null;
    private float cornerRadius = 16.0F;
    private float windowAlpha = 1.0F;
    private boolean isBorderEnabled = false;
    private int borderColor = -5465;
    private float borderWidth = 2.0F;
    @org.jetbrains.annotations.Nullable()
    private android.view.View cropBorderView;
    private boolean isCropBorderExpanded = false;
    @org.jetbrains.annotations.NotNull()
    private final android.graphics.Paint borderPaint = null;
    @org.jetbrains.annotations.NotNull()
    private final android.graphics.RectF borderRect = null;
    private boolean borderStateBeforeCrop = false;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.ui.windowsettings.WindowPositionManager positionManager = null;
    private int currentVideoOrientation = android.content.res.Configuration.ORIENTATION_PORTRAIT;
    private int currentVideoWidth = 0;
    private int currentVideoHeight = 0;
    
    public TransformRenderer(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    android.widget.FrameLayout container, int windowWidth, int windowHeight) {
        super();
    }
    
    /**
     * 应用所有变换（简化版：移除裁剪处理，由统一裁剪管理器处理）
     */
    public final void applyTransforms(@org.jetbrains.annotations.Nullable()
    android.view.TextureView textureView, @org.jetbrains.annotations.Nullable()
    android.view.View targetView, @org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean isCroppedWindow, @org.jetbrains.annotations.Nullable()
    android.graphics.RectF cropRectRatio, boolean isMirrored, float currentScaleFactor, float currentRotation, float currentPivotX, float currentPivotY) {
    }
    
    /**
     * 应用容器变换（简化版：移除复杂偏移计算）
     */
    private final void applyContainerTransforms(boolean isCroppedWindow, android.graphics.RectF cropRectRatio, float currentScaleFactor, float currentRotation, float currentPivotX, float currentPivotY) {
    }
    
    /**
     * 应用 TextureView Matrix 变换（裁剪适配版：支持裁剪窗口的镜像变换）
     * 🎯 关键修复：完全移除裁剪相关的Matrix变换，避免与clipBounds冲突
     */
    private final void applyTextureViewTransforms(android.view.TextureView textureView, boolean isMirrored, boolean isCroppedWindow, android.graphics.RectF cropRectRatio) {
    }
    
    /**
     * 应用图片视图变换（ImageView专用，简化版：移除裁剪处理）
     * 🎯 关键修复：ImageView也移除裁剪处理，统一由clipBounds处理
     */
    private final void applyImageViewTransforms(android.view.View imageView, boolean isMirrored) {
    }
    
    /**
     * 应用镜像变换（裁剪适配版：根据裁剪状态使用正确的镜像轴心点）
     */
    private final void applyMirrorTransform(boolean isCroppedWindow, android.graphics.RectF cropRectRatio) {
    }
    
    /**
     * 设置圆角半径
     */
    public final void setCornerRadius(float radius) {
    }
    
    /**
     * 获取当前圆角半径
     */
    public final float getCornerRadius() {
        return 0.0F;
    }
    
    /**
     * 🎯 新增：为裁剪状态应用圆角和边框（公共方法）
     */
    public final void applyCornerRadiusForCrop() {
    }
    
    /**
     * 应用圆角效果（统一版：所有窗口都使用父容器边框法）
     * 🎯 统一边框法：移除复杂的分支逻辑，统一使用父容器边框法
     */
    private final void applyCornerRadius() {
    }
    
    /**
     * 设置窗口透明度
     */
    public final void setWindowAlpha(float alphaValue) {
    }
    
    /**
     * 获取当前窗口透明度
     */
    public final float getWindowAlpha() {
        return 0.0F;
    }
    
    /**
     * 设置边框显示状态
     * 🎯 统一边框法：所有窗口都使用父容器边框法
     */
    public final void setBorderEnabled(boolean enabled) {
    }
    
    /**
     * 获取当前边框显示状态
     */
    public final boolean isBorderEnabled() {
        return false;
    }
    
    /**
     * 设置边框颜色
     */
    public final void setBorderColor(int color) {
    }
    
    /**
     * 获取当前边框颜色
     */
    public final int getBorderColor() {
        return 0;
    }
    
    /**
     * 设置边框宽度
     * 🎯 统一边框法：边框宽度变化时重新创建边框视图
     */
    public final void setBorderWidth(float width) {
    }
    
    /**
     * 获取当前边框宽度
     */
    public final float getBorderWidth() {
        return 0.0F;
    }
    
    /**
     * 清理前景和其他UI状态
     */
    public final void cleanup() {
    }
    
    /**
     * dp转px工具方法
     */
    private final int dpToPx(int dp) {
        return 0;
    }
    
    /**
     * 🎯 修复：dp转px工具方法（Float版本，避免精度损失）
     * 与遥控端WindowVisualizationContainerView保持一致
     */
    private final float dpToPxFloat(float dp) {
        return 0.0F;
    }
    
    /**
     * 初始化位置管理器（在窗口创建后调用）
     */
    public final void initializePositionManager(boolean isCroppedWindow, @org.jetbrains.annotations.Nullable()
    android.graphics.RectF cropRectRatio) {
    }
    
    /**
     * 获取位置管理器实例（用于其他组件直接访问）
     */
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.ui.windowsettings.WindowPositionManager getPositionManager() {
        return null;
    }
    
    /**
     * 🎯 统一边框法：创建统一边框视图（支持裁剪和未裁剪窗口）
     */
    private final void createUnifiedBorderView(android.graphics.Rect clipBounds) {
    }
    
    /**
     * 🎯 统一边框法：移除统一边框视图
     */
    private final void removeUnifiedBorderView() {
    }
    
    /**
     * 🎯 统一边框法：更新边框视图位置和变换（当容器位置或变换变化时调用）
     */
    public final void updateUnifiedBorderPosition() {
    }
    
    /**
     * 🎯 统一边框法：将边框视图移到前台（层级管理时调用）
     */
    public final void bringBorderToFront() {
    }
    
    /**
     * 🎯 统一边框法：进入裁剪模式时临时移除边框
     */
    public final void onEnterCropMode() {
    }
    
    /**
     * 🎯 统一边框法：退出裁剪模式时恢复边框
     */
    public final void onExitCropMode() {
    }
    
    /**
     * 🎯 统一边框法：同步边框视图的可见性（当窗口显示/隐藏时调用）
     */
    public final void syncBorderVisibility(int visibility) {
    }
    
    /**
     * 🎯 横屏适配：应用方向变换（动态分辨率版）
     */
    private final void applyOrientationTransform() {
    }
    
    /**
     * 设置视频方向和分辨率
     */
    public final void setVideoOrientation(int orientation, int videoWidth, int videoHeight) {
    }
    
    /**
     * 获取方向名称（用于日志）
     */
    private final java.lang.String getOrientationName(int orientation) {
        return null;
    }
}