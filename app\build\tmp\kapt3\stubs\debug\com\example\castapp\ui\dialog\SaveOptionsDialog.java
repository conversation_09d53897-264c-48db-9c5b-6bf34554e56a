package com.example.castapp.ui.dialog;

/**
 * 保存选项对话框
 * 用于选择保存布局参数的模式
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u00002\u00020\u0001B0\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012!\u0010\u0004\u001a\u001d\u0012\u0013\u0012\u00110\u0006\u00a2\u0006\f\b\u0007\u0012\b\b\b\u0012\u0004\b\b(\t\u0012\u0004\u0012\u00020\n0\u0005\u00a2\u0006\u0002\u0010\u000bJ\b\u0010\u0016\u001a\u00020\nH\u0002J\b\u0010\u0017\u001a\u00020\nH\u0002J\u0010\u0010\u0018\u001a\u00020\n2\u0006\u0010\u0019\u001a\u00020\u001aH\u0002J\b\u0010\u001b\u001a\u00020\nH\u0002R\u000e\u0010\f\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R)\u0010\u0004\u001a\u001d\u0012\u0013\u0012\u00110\u0006\u00a2\u0006\f\b\u0007\u0012\b\b\b\u0012\u0004\b\b(\t\u0012\u0004\u0012\u00020\n0\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0014X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001c"}, d2 = {"Lcom/example/castapp/ui/dialog/SaveOptionsDialog;", "Landroid/app/Dialog;", "context", "Landroid/content/Context;", "onSaveOptionSelected", "Lkotlin/Function1;", "", "Lkotlin/ParameterName;", "name", "isUpdateMode", "", "(Landroid/content/Context;Lkotlin/jvm/functions/Function1;)V", "btnCancel", "Landroid/widget/Button;", "btnClose", "Landroid/widget/ImageButton;", "btnConfirm", "radioGroupSaveMode", "Landroid/widget/RadioGroup;", "radioReplaceAll", "Landroid/widget/RadioButton;", "radioUpdateExisting", "handleConfirm", "initDialog", "initViews", "view", "Landroid/view/View;", "setupClickListeners", "app_debug"})
public final class SaveOptionsDialog extends android.app.Dialog {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<java.lang.Boolean, kotlin.Unit> onSaveOptionSelected = null;
    private android.widget.RadioGroup radioGroupSaveMode;
    private android.widget.RadioButton radioUpdateExisting;
    private android.widget.RadioButton radioReplaceAll;
    private android.widget.Button btnConfirm;
    private android.widget.Button btnCancel;
    private android.widget.ImageButton btnClose;
    
    public SaveOptionsDialog(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onSaveOptionSelected) {
        super(null);
    }
    
    /**
     * 初始化对话框
     */
    private final void initDialog() {
    }
    
    /**
     * 初始化视图组件
     */
    private final void initViews(android.view.View view) {
    }
    
    /**
     * 设置点击监听器
     */
    private final void setupClickListeners() {
    }
    
    /**
     * 处理确认操作
     */
    private final void handleConfirm() {
    }
}