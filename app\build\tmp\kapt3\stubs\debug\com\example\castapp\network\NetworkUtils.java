package com.example.castapp.network;

/**
 * 网络工具类
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\b\n\u0000\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u0004\u0018\u00010\u0004J\u000e\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u0004J\u000e\u0010\b\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\n\u00a8\u0006\u000b"}, d2 = {"Lcom/example/castapp/network/NetworkUtils;", "", "()V", "getLocalIpAddress", "", "isValidIpAddress", "", "ip", "isValidPort", "port", "", "app_debug"})
public final class NetworkUtils {
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.network.NetworkUtils INSTANCE = null;
    
    private NetworkUtils() {
        super();
    }
    
    /**
     * 获取本机IP地址
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getLocalIpAddress() {
        return null;
    }
    
    /**
     * 验证IP地址格式
     */
    public final boolean isValidIpAddress(@org.jetbrains.annotations.NotNull()
    java.lang.String ip) {
        return false;
    }
    
    /**
     * 验证端口号
     */
    public final boolean isValidPort(int port) {
        return false;
    }
}