package com.example.castapp.manager.windowsettings;

/**
 * 窗口信息模块
 * 负责提供窗口信息和状态查询服务
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0007\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0003\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J(\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\b2\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\rH\u0002J(\u0010\u000e\u001a\u00020\u00062\u0006\u0010\u000f\u001a\u00020\b2\u0006\u0010\u0010\u001a\u00020\b2\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\rH\u0002J(\u0010\u0011\u001a\u00020\u00062\u0006\u0010\u0012\u001a\u00020\b2\u0006\u0010\u0013\u001a\u00020\b2\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\rH\u0002J\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00060\u0015J\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00060\u0015J\u0010\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\bH\u0002J\u0010\u0010\u001a\u001a\u00020\b2\u0006\u0010\n\u001a\u00020\u000bH\u0002R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001b"}, d2 = {"Lcom/example/castapp/manager/windowsettings/WindowInfoModule;", "", "dataModule", "Lcom/example/castapp/manager/windowsettings/WindowDataModule;", "(Lcom/example/castapp/manager/windowsettings/WindowDataModule;)V", "createCameraWindowInfo", "Lcom/example/castapp/model/CastWindowInfo;", "cameraId", "", "cameraName", "transformHandler", "Lcom/example/castapp/ui/windowsettings/TransformHandler;", "zOrder", "", "createMediaWindowInfo", "mediaId", "mediaType", "createTextWindowInfo", "textId", "textContent", "getActiveWindowInfoList", "", "getCurrentWindowInfoList", "getLandscapeModeEnabled", "", "connectionId", "getTextContentFromTransformHandler", "app_debug"})
public final class WindowInfoModule {
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.windowsettings.WindowDataModule dataModule = null;
    
    public WindowInfoModule(@org.jetbrains.annotations.NotNull()
    com.example.castapp.manager.windowsettings.WindowDataModule dataModule) {
        super();
    }
    
    /**
     * 创建媒体窗口信息（视频/图片）
     */
    private final com.example.castapp.model.CastWindowInfo createMediaWindowInfo(java.lang.String mediaId, java.lang.String mediaType, com.example.castapp.ui.windowsettings.TransformHandler transformHandler, int zOrder) {
        return null;
    }
    
    /**
     * 创建文本窗口信息
     */
    private final com.example.castapp.model.CastWindowInfo createTextWindowInfo(java.lang.String textId, java.lang.String textContent, com.example.castapp.ui.windowsettings.TransformHandler transformHandler, int zOrder) {
        return null;
    }
    
    /**
     * 创建摄像头窗口信息
     */
    private final com.example.castapp.model.CastWindowInfo createCameraWindowInfo(java.lang.String cameraId, java.lang.String cameraName, com.example.castapp.ui.windowsettings.TransformHandler transformHandler, int zOrder) {
        return null;
    }
    
    /**
     * 获取当前活跃的投屏窗口信息列表
     * 按照实际的视图层级顺序返回（从底层到顶层）
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.castapp.model.CastWindowInfo> getActiveWindowInfoList() {
        return null;
    }
    
    /**
     * 获取当前活跃的投屏窗口信息列表（公共方法）
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.castapp.model.CastWindowInfo> getCurrentWindowInfoList() {
        return null;
    }
    
    /**
     * 🎯 获取横屏模式启用状态
     * 从WindowSettingsManager获取本地存储的横屏状态
     */
    private final boolean getLandscapeModeEnabled(java.lang.String connectionId) {
        return false;
    }
    
    /**
     * 从TransformHandler获取文本内容
     */
    private final java.lang.String getTextContentFromTransformHandler(com.example.castapp.ui.windowsettings.TransformHandler transformHandler) {
        return null;
    }
}