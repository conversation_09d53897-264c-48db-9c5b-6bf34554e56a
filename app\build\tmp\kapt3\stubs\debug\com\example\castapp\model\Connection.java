package com.example.castapp.model;

/**
 * 统一连接管理模型 - 集成版
 * 合并了Connection、ConnectionState的所有功能
 * 统一ID架构：使用connectionId作为唯一标识符
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0010\t\n\u0002\b6\b\u0086\b\u0018\u0000 C2\u00020\u0001:\u0001CB\u0083\u0001\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u0007\u001a\u00020\b\u0012\b\b\u0002\u0010\t\u001a\u00020\b\u0012\b\b\u0002\u0010\n\u001a\u00020\b\u0012\b\b\u0002\u0010\u000b\u001a\u00020\b\u0012\b\b\u0002\u0010\f\u001a\u00020\b\u0012\b\b\u0002\u0010\r\u001a\u00020\u000e\u0012\b\b\u0002\u0010\u000f\u001a\u00020\b\u0012\b\b\u0002\u0010\u0010\u001a\u00020\b\u0012\b\b\u0002\u0010\u0011\u001a\u00020\b\u0012\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0002\u0010\u0013J\t\u0010!\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\"\u001a\u00020\bH\u00c6\u0003J\t\u0010#\u001a\u00020\bH\u00c6\u0003J\t\u0010$\u001a\u00020\bH\u00c6\u0003J\u000b\u0010%\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010&\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\'\u001a\u00020\u0006H\u00c6\u0003J\t\u0010(\u001a\u00020\bH\u00c6\u0003J\t\u0010)\u001a\u00020\bH\u00c6\u0003J\t\u0010*\u001a\u00020\bH\u00c6\u0003J\t\u0010+\u001a\u00020\bH\u00c6\u0003J\t\u0010,\u001a\u00020\bH\u00c6\u0003J\t\u0010-\u001a\u00020\u000eH\u00c6\u0003J\u008d\u0001\u0010.\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\b2\b\b\u0002\u0010\n\u001a\u00020\b2\b\b\u0002\u0010\u000b\u001a\u00020\b2\b\b\u0002\u0010\f\u001a\u00020\b2\b\b\u0002\u0010\r\u001a\u00020\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\b2\b\b\u0002\u0010\u0010\u001a\u00020\b2\b\b\u0002\u0010\u0011\u001a\u00020\b2\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010/\u001a\u00020\b2\b\u00100\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u0006\u00101\u001a\u00020\u0003J\u0006\u00102\u001a\u00020\u0006J\u0006\u00103\u001a\u00020\u0006J\u0006\u00104\u001a\u00020\u000eJ\u0006\u00105\u001a\u00020\u0003J\u0006\u00106\u001a\u00020\u0006J\t\u00107\u001a\u00020\u0006H\u00d6\u0001J\t\u00108\u001a\u00020\u0003H\u00d6\u0001J\u000e\u00109\u001a\u00020\u00002\u0006\u0010:\u001a\u00020\bJ\u000e\u0010;\u001a\u00020\u00002\u0006\u0010<\u001a\u00020\bJ\u0010\u0010=\u001a\u00020\u00002\b\u0010>\u001a\u0004\u0018\u00010\u0003J\u000e\u0010?\u001a\u00020\u00002\u0006\u0010@\u001a\u00020\bJ\u000e\u0010A\u001a\u00020\u00002\u0006\u0010@\u001a\u00020\bJ\u000e\u0010B\u001a\u00020\u00002\u0006\u0010<\u001a\u00020\bR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0013\u0010\u0012\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0015R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0015R\u0011\u0010\t\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\u0018R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\u0018R\u0011\u0010\n\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u0018R\u0011\u0010\u000b\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\u0018R\u0011\u0010\r\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u0011\u0010\u0011\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0018R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001dR\u0011\u0010\u000f\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0018R\u0011\u0010\u0010\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0018R\u0011\u0010\f\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u0018\u00a8\u0006D"}, d2 = {"Lcom/example/castapp/model/Connection;", "", "connectionId", "", "ipAddress", "port", "", "isConnected", "", "isCasting", "isMediaAudioEnabled", "isMicAudioEnabled", "webSocketConnected", "lastUpdateTime", "", "rtpSenderActive", "videoEncoderActive", "mediaProjectionActive", "errorMessage", "(Ljava/lang/String;Ljava/lang/String;IZZZZZJZZZLjava/lang/String;)V", "getConnectionId", "()Ljava/lang/String;", "getErrorMessage", "getIpAddress", "()Z", "getLastUpdateTime", "()J", "getMediaProjectionActive", "getPort", "()I", "getRtpSenderActive", "getVideoEncoderActive", "getWebSocketConnected", "component1", "component10", "component11", "component12", "component13", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "getDisplayText", "getMediaAudioPort", "getMicAudioPort", "getSSRC", "getStatusSummary", "getWebSocketPort", "hashCode", "toString", "withCasting", "casting", "withConnection", "connected", "withError", "error", "withMediaAudio", "enabled", "withMicAudio", "withWebSocketConnection", "Companion", "app_debug"})
public final class Connection {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String connectionId = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String ipAddress = null;
    private final int port = 0;
    private final boolean isConnected = false;
    private final boolean isCasting = false;
    private final boolean isMediaAudioEnabled = false;
    private final boolean isMicAudioEnabled = false;
    private final boolean webSocketConnected = false;
    private final long lastUpdateTime = 0L;
    private final boolean rtpSenderActive = false;
    private final boolean videoEncoderActive = false;
    private final boolean mediaProjectionActive = false;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String errorMessage = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.model.Connection.Companion Companion = null;
    
    public Connection(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    java.lang.String ipAddress, int port, boolean isConnected, boolean isCasting, boolean isMediaAudioEnabled, boolean isMicAudioEnabled, boolean webSocketConnected, long lastUpdateTime, boolean rtpSenderActive, boolean videoEncoderActive, boolean mediaProjectionActive, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getConnectionId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getIpAddress() {
        return null;
    }
    
    public final int getPort() {
        return 0;
    }
    
    public final boolean isConnected() {
        return false;
    }
    
    public final boolean isCasting() {
        return false;
    }
    
    public final boolean isMediaAudioEnabled() {
        return false;
    }
    
    public final boolean isMicAudioEnabled() {
        return false;
    }
    
    public final boolean getWebSocketConnected() {
        return false;
    }
    
    public final long getLastUpdateTime() {
        return 0L;
    }
    
    public final boolean getRtpSenderActive() {
        return false;
    }
    
    public final boolean getVideoEncoderActive() {
        return false;
    }
    
    public final boolean getMediaProjectionActive() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getErrorMessage() {
        return null;
    }
    
    /**
     * 获取显示文本
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDisplayText() {
        return null;
    }
    
    /**
     * 获取状态摘要文本
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getStatusSummary() {
        return null;
    }
    
    /**
     * 统一SSRC生成方法
     * 直接从connectionId生成SSRC，无需映射表
     */
    public final long getSSRC() {
        return 0L;
    }
    
    /**
     * 获取WebSocket端口（UDP端口+1）
     */
    public final int getWebSocketPort() {
        return 0;
    }
    
    /**
     * 获取媒体音频端口（UDP端口+2）
     */
    public final int getMediaAudioPort() {
        return 0;
    }
    
    /**
     * 获取麦克风音频端口（UDP端口+3）
     */
    public final int getMicAudioPort() {
        return 0;
    }
    
    /**
     * 更新投屏状态
     */
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.model.Connection withCasting(boolean casting) {
        return null;
    }
    
    /**
     * 更新媒体音频状态
     */
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.model.Connection withMediaAudio(boolean enabled) {
        return null;
    }
    
    /**
     * 更新麦克风音频状态
     */
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.model.Connection withMicAudio(boolean enabled) {
        return null;
    }
    
    /**
     * 更新WebSocket连接状态
     */
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.model.Connection withWebSocketConnection(boolean connected) {
        return null;
    }
    
    /**
     * 更新错误信息
     */
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.model.Connection withError(@org.jetbrains.annotations.Nullable()
    java.lang.String error) {
        return null;
    }
    
    /**
     * 更新基础连接状态
     */
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.model.Connection withConnection(boolean connected) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    public final boolean component10() {
        return false;
    }
    
    public final boolean component11() {
        return false;
    }
    
    public final boolean component12() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component13() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    public final int component3() {
        return 0;
    }
    
    public final boolean component4() {
        return false;
    }
    
    public final boolean component5() {
        return false;
    }
    
    public final boolean component6() {
        return false;
    }
    
    public final boolean component7() {
        return false;
    }
    
    public final boolean component8() {
        return false;
    }
    
    public final long component9() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.model.Connection copy(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    java.lang.String ipAddress, int port, boolean isConnected, boolean isCasting, boolean isMediaAudioEnabled, boolean isMicAudioEnabled, boolean webSocketConnected, long lastUpdateTime, boolean rtpSenderActive, boolean videoEncoderActive, boolean mediaProjectionActive, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0016\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bJ\u0006\u0010\t\u001a\u00020\u0006\u00a8\u0006\n"}, d2 = {"Lcom/example/castapp/model/Connection$Companion;", "", "()V", "create", "Lcom/example/castapp/model/Connection;", "ipAddress", "", "port", "", "generateConnectionId", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * 生成连接ID
         */
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String generateConnectionId() {
            return null;
        }
        
        /**
         * 创建新连接实例
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.model.Connection create(@org.jetbrains.annotations.NotNull()
        java.lang.String ipAddress, int port) {
            return null;
        }
    }
}