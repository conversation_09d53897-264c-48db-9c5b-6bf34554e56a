package com.example.castapp.utils;

/**
 * 🪟 投屏窗口容器缩放计算工具类
 * 负责计算远程接收端控制窗口中投屏窗口容器的可视化参数
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0007\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\"\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u00042\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00070\u00042\u0006\u0010\b\u001a\u00020\tJ*\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\f0\u000b2\u0006\u0010\r\u001a\u00020\f2\u0006\u0010\u000e\u001a\u00020\f2\u0006\u0010\b\u001a\u00020\t\u00a8\u0006\u000f"}, d2 = {"Lcom/example/castapp/utils/WindowScaleCalculator;", "", "()V", "calculateWindowVisualizationData", "", "Lcom/example/castapp/model/WindowVisualizationData;", "windowInfoList", "Lcom/example/castapp/model/CastWindowInfo;", "remoteControlScale", "", "convertRemoteToActualCoordinates", "Lkotlin/Pair;", "", "remoteX", "remoteY", "app_debug"})
public final class WindowScaleCalculator {
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.utils.WindowScaleCalculator INSTANCE = null;
    
    private WindowScaleCalculator() {
        super();
    }
    
    /**
     * 批量计算投屏窗口容器的可视化数据
     *
     * @param windowInfoList 投屏窗口信息列表
     * @param remoteControlScale 远程控制窗口的缩放比例
     * @return 可视化数据列表
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.castapp.model.WindowVisualizationData> calculateWindowVisualizationData(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.castapp.model.CastWindowInfo> windowInfoList, double remoteControlScale) {
        return null;
    }
    
    /**
     * 🎯 将遥控端坐标转换为接收端实际屏幕坐标
     * @param remoteX 遥控端X坐标（远程控制窗口坐标系）
     * @param remoteY 遥控端Y坐标（远程控制窗口坐标系）
     * @param remoteControlScale 远程控制窗口的缩放比例
     * @return 接收端实际屏幕坐标 Pair(actualX, actualY)
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlin.Pair<java.lang.Float, java.lang.Float> convertRemoteToActualCoordinates(float remoteX, float remoteY, double remoteControlScale) {
        return null;
    }
}