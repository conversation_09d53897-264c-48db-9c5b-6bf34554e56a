<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/bottom_sheet_background"
    android:paddingTop="8dp"
    android:paddingBottom="8dp">

    <!-- 拖拽指示器 -->
    <View
        android:layout_width="40dp"
        android:layout_height="4dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="4dp"
        android:background="#CCCCCC"
        android:alpha="0.6" />

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:paddingBottom="4dp">

        <!-- 标题图标 -->
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            app:tint="#333333"
            android:src="@drawable/ic_director" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="导播台"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginStart="8dp"
            android:textColor="#333333" />

        <!-- 关闭按钮 -->
        <ImageButton
            android:id="@+id/btn_close"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_close"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="关闭" />

    </LinearLayout>

    <!-- 分割线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#E0E0E0"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="8dp" />

    <!-- 内容区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="450dp"
        android:orientation="horizontal"
        android:baselineAligned="false"
        android:paddingStart="4dp"
        android:paddingEnd="4dp">

        <!-- 左侧布局列表 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginEnd="6dp">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/layout_list"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="#F5F5F5"
                    android:padding="2dp"
                    android:clipToPadding="false"/>
            </androidx.cardview.widget.CardView>
        </LinearLayout>

        <!-- 右侧布局详情 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="2"
            android:orientation="vertical">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/detail_list"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="#F5F5F5"
                        android:padding="4dp"
                        android:clipToPadding="false"/>

                    <TextView
                        android:id="@+id/empty_view"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:text="请选择左侧的布局查看详情"
                        android:textColor="#9E9E9E"
                        android:textSize="14sp"
                        android:visibility="visible" />
                </FrameLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>
    </LinearLayout>

    <!-- 底部按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end"
        android:layout_marginTop="8dp"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <!-- 批量选择按钮 -->
        <Button
            android:id="@+id/selection_button"
            android:layout_width="40dp"
            android:layout_height="32dp"
            android:text="选择"
            android:textSize="12sp"
            android:enabled="false"
            android:background="@drawable/button_background"
            android:textColor="#FFFFFF"/>

        <!-- 批量删除按钮 -->
        <Button
            android:id="@+id/batch_delete_button"
            android:layout_width="40dp"
            android:layout_height="32dp"
            android:text="删除"
            android:textSize="12sp"
            android:layout_marginStart="8dp"
            android:enabled="false"
            android:background="@drawable/button_delete_background"
            android:textColor="#FFFFFF"/>

        <Button
            android:id="@+id/save_button"
            android:layout_width="40dp"
            android:layout_height="32dp"
            android:text="保存"
            android:textSize="12sp"
            android:layout_marginStart="8dp"
            android:background="@drawable/button_background"
            android:textColor="#FFFFFF"/>

        <Button
            android:id="@+id/edit_button"
            android:layout_width="40dp"
            android:layout_height="32dp"
            android:text="编辑"
            android:textSize="12sp"
            android:layout_marginStart="8dp"
            android:enabled="false"
            android:background="@drawable/button_background"
            android:textColor="#FFFFFF"/>

        <Button
            android:id="@+id/apply_button"
            android:layout_width="60dp"
            android:layout_height="32dp"
            android:text="应用布局"
            android:textSize="12sp"
            android:layout_marginStart="8dp"
            android:enabled="false"
            android:background="@drawable/button_background"
            android:textColor="#FFFFFF"/>


    </LinearLayout>
</LinearLayout>