<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res/auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="8dp">

    <!-- 添加标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="8dp"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/dialog_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="远程发送端设置"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@android:color/black" />

        <ImageButton
            android:id="@+id/close_dialog_button"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_close"
            android:contentDescription="关闭窗口"
            android:scaleType="centerInside"
            android:alpha="0.7"/>
    </LinearLayout>

    <!-- 投屏设置区域（紧凑型布局） -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/rounded_background"
        android:padding="6dp"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="4dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="投屏设置"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="#333333"
            android:layout_marginBottom="8dp" />

        <!-- 码率设置行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="码率:"
                android:textSize="12sp"
                android:textColor="#666666"
                android:minWidth="40dp" />

            <SeekBar
                android:id="@+id/bitrate_seekbar"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:max="35"
                android:progress="15"/>

            <TextView
                android:id="@+id/bitrate_value_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="20 Mbps"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="#333333"
                android:minWidth="50dp"
                android:gravity="center" />

        </LinearLayout>

        <!-- 分辨率设置行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="6dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="分辨率:"
                android:textSize="12sp"
                android:textColor="#666666"
                android:minWidth="40dp" />

            <SeekBar
                android:id="@+id/resolution_seekbar"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:max="149"
                android:progress="99"/>

            <TextView
                android:id="@+id/resolution_value_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="100%"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="#333333"
                android:minWidth="50dp"
                android:gravity="center" />

        </LinearLayout>

        <!-- 分辨率信息显示 -->
        <TextView
            android:id="@+id/resolution_info_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="当前: 1080×1920 (100%)"
            android:textSize="11sp"
            android:textColor="#999999"
            android:gravity="start"/>

        <!-- 媒体音频音量设置行 -->
        <LinearLayout
            android:id="@+id/media_audio_volume_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:visibility="visible">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="媒体音量:"
                android:textSize="12sp"
                android:textColor="#666666"
                android:minWidth="40dp" />

            <SeekBar
                android:id="@+id/media_audio_volume_seekbar"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:max="100"
                android:progress="80"/>

            <TextView
                android:id="@+id/media_audio_volume_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="80%"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="#333333"
                android:minWidth="50dp"
                android:gravity="center" />

        </LinearLayout>

        <!-- 麦克风音频音量设置行 -->
        <LinearLayout
            android:id="@+id/mic_audio_volume_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="6dp"
            android:visibility="visible">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="麦克风音量:"
                android:textSize="12sp"
                android:textColor="#666666"
                android:minWidth="40dp" />

            <SeekBar
                android:id="@+id/mic_audio_volume_seekbar"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:max="100"
                android:progress="80"/>

            <TextView
                android:id="@+id/mic_audio_volume_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="80%"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="#333333"
                android:minWidth="50dp"
                android:gravity="center" />

        </LinearLayout>

        <!-- 远程被控设置行（禁用状态） -->
        <LinearLayout
            android:id="@+id/remote_control_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="6dp"
            android:visibility="visible"
            android:alpha="0.5">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="远程被控:"
                android:textSize="12sp"
                android:textColor="#666666"
                android:minWidth="40dp" />

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/remote_control_switch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:checked="false"
                android:enabled="false" />

            <TextView
                android:id="@+id/remote_control_status_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="192.168.8.104:9999"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="#333333"
                android:gravity="start" />

        </LinearLayout>

    </LinearLayout>

    <!-- 连接列表区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginTop="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="6dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="连接列表"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="#333333" />

            <TextView
                android:id="@+id/connection_count_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0个连接"
                android:textSize="12sp"
                android:textColor="#666666"
                android:background="@drawable/rounded_background"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginEnd="8dp" />

            <ImageButton
                android:id="@+id/add_connection_button"
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_add"
                android:contentDescription="添加连接"
                android:scaleType="centerInside"
                android:alpha="0.7"
                android:tint="#666666" />

        </LinearLayout>

        <!-- RecyclerView替代原来的LinearLayout -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/connections_container"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:scrollbars="vertical"
            android:scrollbarStyle="insideOverlay"
            android:fadeScrollbars="false"
            android:padding="4dp"
            android:clipToPadding="false" />

    </LinearLayout>

</LinearLayout>
