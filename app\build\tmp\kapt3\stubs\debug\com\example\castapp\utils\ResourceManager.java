package com.example.castapp.utils;

/**
 * 资源管理工具类
 * 提供统一的try-with-resources模式支持，确保资源正确释放
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u008c\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0011\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001a\u0010\u0003\u001a\u00020\u00042\b\u0010\u0005\u001a\u0004\u0018\u00010\u00062\b\b\u0002\u0010\u0007\u001a\u00020\bJ\u001a\u0010\t\u001a\u00020\u00042\b\u0010\n\u001a\u0004\u0018\u00010\u000b2\b\b\u0002\u0010\u0007\u001a\u00020\bJ\u001a\u0010\f\u001a\u00020\u00042\b\u0010\r\u001a\u0004\u0018\u00010\u000e2\b\b\u0002\u0010\u0007\u001a\u00020\bJ\u001a\u0010\u000f\u001a\u00020\u00042\b\u0010\r\u001a\u0004\u0018\u00010\u00102\b\b\u0002\u0010\u0007\u001a\u00020\bJK\u0010\u0011\u001a\u0004\u0018\u0001H\u0012\"\u0004\b\u0000\u0010\u00122\u0006\u0010\u0013\u001a\u00020\b2\u001a\b\n\u0010\u0014\u001a\u0014\u0012\b\u0012\u00060\u0016j\u0002`\u0017\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00152\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u0002H\u00120\u0019H\u0086\b\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u001aJ;\u0010\u001b\u001a\u00020\u00042.\u0010\u001c\u001a\u0018\u0012\u0014\b\u0001\u0012\u0010\u0012\u0006\u0012\u0004\u0018\u00010\u0001\u0012\u0004\u0012\u00020\b0\u001e0\u001d\"\u0010\u0012\u0006\u0012\u0004\u0018\u00010\u0001\u0012\u0004\u0012\u00020\b0\u001e\u00a2\u0006\u0002\u0010\u001fJ\u001a\u0010 \u001a\u00020\u00042\b\u0010!\u001a\u0004\u0018\u00010\"2\b\b\u0002\u0010\u0007\u001a\u00020\bJ\u001a\u0010#\u001a\u00020\u00042\b\u0010$\u001a\u0004\u0018\u00010%2\b\b\u0002\u0010\u0007\u001a\u00020\bJ\u001a\u0010&\u001a\u00020\u00042\b\u0010\'\u001a\u0004\u0018\u00010(2\b\b\u0002\u0010\u0007\u001a\u00020\bJ$\u0010)\u001a\u00020\u00042\b\u0010*\u001a\u0004\u0018\u00010+2\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010,\u001a\u00020-J\u001a\u0010.\u001a\u00020\u00042\b\u0010/\u001a\u0004\u0018\u0001002\b\b\u0002\u0010\u0007\u001a\u00020\bJ$\u00101\u001a\u00020\u00042\b\u00102\u001a\u0004\u0018\u0001032\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010,\u001a\u00020-\u0082\u0002\u0007\n\u0005\b\u009920\u0001\u00a8\u00064"}, d2 = {"Lcom/example/castapp/utils/ResourceManager;", "", "()V", "safeCancelCoroutineScope", "", "scope", "Lkotlinx/coroutines/CoroutineScope;", "type", "", "safeClose", "closeable", "Ljava/io/Closeable;", "safeCloseDatagramSocket", "socket", "Ljava/net/DatagramSocket;", "safeCloseSocket", "Ljava/net/Socket;", "safeExecute", "T", "operation", "onError", "Lkotlin/Function1;", "Ljava/lang/Exception;", "Lkotlin/Exception;", "block", "Lkotlin/Function0;", "(Ljava/lang/String;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;)Ljava/lang/Object;", "safeReleaseAll", "resources", "", "Lkotlin/Pair;", "([Lkotlin/Pair;)V", "safeReleaseAudioRecord", "audioRecord", "Landroid/media/AudioRecord;", "safeReleaseMediaCodec", "codec", "Landroid/media/MediaCodec;", "safeReleaseSurface", "surface", "Landroid/view/Surface;", "safeShutdownExecutor", "executor", "Ljava/util/concurrent/ExecutorService;", "timeoutMs", "", "safeStopMediaProjection", "projection", "Landroid/media/projection/MediaProjection;", "safeStopThread", "thread", "Ljava/lang/Thread;", "app_debug"})
public final class ResourceManager {
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.utils.ResourceManager INSTANCE = null;
    
    private ResourceManager() {
        super();
    }
    
    /**
     * 安全执行资源操作的扩展函数
     * 提供统一的异常处理和日志记录
     */
    @org.jetbrains.annotations.Nullable()
    public final <T extends java.lang.Object>T safeExecute(@org.jetbrains.annotations.NotNull()
    java.lang.String operation, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super java.lang.Exception, kotlin.Unit> onError, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<? extends T> block) {
        return null;
    }
    
    /**
     * 安全释放MediaCodec资源 - 🚀 修复：移除延迟，避免音频中断
     */
    public final void safeReleaseMediaCodec(@org.jetbrains.annotations.Nullable()
    android.media.MediaCodec codec, @org.jetbrains.annotations.NotNull()
    java.lang.String type) {
    }
    
    /**
     * 安全释放AudioRecord资源
     */
    public final void safeReleaseAudioRecord(@org.jetbrains.annotations.Nullable()
    android.media.AudioRecord audioRecord, @org.jetbrains.annotations.NotNull()
    java.lang.String type) {
    }
    
    /**
     * 安全释放Surface资源
     */
    public final void safeReleaseSurface(@org.jetbrains.annotations.Nullable()
    android.view.Surface surface, @org.jetbrains.annotations.NotNull()
    java.lang.String type) {
    }
    
    /**
     * 安全关闭Socket资源
     */
    public final void safeCloseSocket(@org.jetbrains.annotations.Nullable()
    java.net.Socket socket, @org.jetbrains.annotations.NotNull()
    java.lang.String type) {
    }
    
    /**
     * 安全关闭DatagramSocket资源
     */
    public final void safeCloseDatagramSocket(@org.jetbrains.annotations.Nullable()
    java.net.DatagramSocket socket, @org.jetbrains.annotations.NotNull()
    java.lang.String type) {
    }
    
    /**
     * 安全停止MediaProjection资源
     */
    public final void safeStopMediaProjection(@org.jetbrains.annotations.Nullable()
    android.media.projection.MediaProjection projection, @org.jetbrains.annotations.NotNull()
    java.lang.String type) {
    }
    
    /**
     * 安全关闭Closeable资源
     */
    public final void safeClose(@org.jetbrains.annotations.Nullable()
    java.io.Closeable closeable, @org.jetbrains.annotations.NotNull()
    java.lang.String type) {
    }
    
    /**
     * 安全停止线程
     */
    public final void safeStopThread(@org.jetbrains.annotations.Nullable()
    java.lang.Thread thread, @org.jetbrains.annotations.NotNull()
    java.lang.String type, long timeoutMs) {
    }
    
    /**
     * 安全取消协程作用域
     */
    public final void safeCancelCoroutineScope(@org.jetbrains.annotations.Nullable()
    kotlinx.coroutines.CoroutineScope scope, @org.jetbrains.annotations.NotNull()
    java.lang.String type) {
    }
    
    /**
     * 安全关闭ExecutorService
     */
    public final void safeShutdownExecutor(@org.jetbrains.annotations.Nullable()
    java.util.concurrent.ExecutorService executor, @org.jetbrains.annotations.NotNull()
    java.lang.String type, long timeoutMs) {
    }
    
    /**
     * 批量释放资源
     */
    public final void safeReleaseAll(@org.jetbrains.annotations.NotNull()
    kotlin.Pair<? extends java.lang.Object, java.lang.String>... resources) {
    }
}