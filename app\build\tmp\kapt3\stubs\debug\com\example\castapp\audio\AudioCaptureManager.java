package com.example.castapp.audio;

/**
 * 音频捕获管理器
 * 负责麦克风和媒体音频的捕获
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000Z\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u0012\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\t\u0018\u0000 +2\u00020\u0001:\u0001+B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0010\u0010\u0015\u001a\u00020\u00132\u0006\u0010\u0016\u001a\u00020\u0017H\u0002J \u0010\u0018\u001a\u00020\u00122\u0006\u0010\u0019\u001a\u00020\u00122\u0006\u0010\u001a\u001a\u00020\u00172\u0006\u0010\u001b\u001a\u00020\u001cH\u0002J\u0006\u0010\u0007\u001a\u00020\u001dJ\u0006\u0010\t\u001a\u00020\u001dJ\u0018\u0010\u001e\u001a\u00020\u00132\u0006\u0010\u001f\u001a\u00020\u000b2\u0006\u0010 \u001a\u00020\u0006H\u0002J$\u0010!\u001a\u00020\u001d2\b\u0010\"\u001a\u0004\u0018\u00010#2\u0012\u0010$\u001a\u000e\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u00130\u0011J\u001a\u0010%\u001a\u00020\u001d2\u0012\u0010$\u001a\u000e\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u00130\u0011J\u001c\u0010&\u001a\u00020\u001d2\u0012\u0010$\u001a\u000e\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u00130\u0011H\u0002J\u0006\u0010\'\u001a\u00020\u0013J\u0006\u0010(\u001a\u00020\u0013J\u0006\u0010)\u001a\u00020\u0013J\b\u0010*\u001a\u00020\u0013H\u0002R\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\n\u001a\u0004\u0018\u00010\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\f\u001a\u0004\u0018\u00010\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000e\u001a\u0004\u0018\u00010\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000f\u001a\u0004\u0018\u00010\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u0010\u001a\u0010\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u0013\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u0014\u001a\u0010\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u0013\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006,"}, d2 = {"Lcom/example/castapp/audio/AudioCaptureManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "connectionId", "", "isMediaCapturing", "Ljava/util/concurrent/atomic/AtomicBoolean;", "isMicCapturing", "mediaAudioRecord", "Landroid/media/AudioRecord;", "mediaCaptureThread", "Ljava/lang/Thread;", "micAudioRecord", "micCaptureThread", "onMediaDataCallback", "Lkotlin/Function1;", "", "", "onMicDataCallback", "captureMediaAudio", "bufferSize", "", "getOptimalCaptureBuffer", "sourceBuffer", "actualSize", "bufferPool", "Lcom/example/castapp/audio/AudioBufferPool;", "", "safeReleaseAudioRecord", "audioRecord", "type", "startMediaCapture", "mediaProjection", "Landroid/media/projection/MediaProjection;", "onDataCallback", "startMicCapture", "startMicCaptureWithManager", "stopAll", "stopMediaCapture", "stopMicCapture", "stopMicCaptureWithAudioRecord", "Companion", "app_debug"})
public final class AudioCaptureManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    public static final int SAMPLE_RATE = 48000;
    public static final int MEDIA_CHANNEL_CONFIG = android.media.AudioFormat.CHANNEL_IN_STEREO;
    public static final int AUDIO_FORMAT = android.media.AudioFormat.ENCODING_PCM_16BIT;
    @org.jetbrains.annotations.Nullable()
    private android.media.AudioRecord micAudioRecord;
    @org.jetbrains.annotations.Nullable()
    private android.media.AudioRecord mediaAudioRecord;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.atomic.AtomicBoolean isMicCapturing = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.atomic.AtomicBoolean isMediaCapturing = null;
    @org.jetbrains.annotations.Nullable()
    private java.lang.Thread micCaptureThread;
    @org.jetbrains.annotations.Nullable()
    private java.lang.Thread mediaCaptureThread;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super byte[], kotlin.Unit> onMicDataCallback;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super byte[], kotlin.Unit> onMediaDataCallback;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String connectionId;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.audio.AudioCaptureManager.Companion Companion = null;
    
    public AudioCaptureManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    /**
     * 开始麦克风音频捕获 - 🔥 修改：使用MicrophoneManager
     */
    public final boolean startMicCapture(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super byte[], kotlin.Unit> onDataCallback) {
        return false;
    }
    
    /**
     * 🔥 新增：使用MicrophoneManager启动麦克风捕获
     */
    private final boolean startMicCaptureWithManager(kotlin.jvm.functions.Function1<? super byte[], kotlin.Unit> onDataCallback) {
        return false;
    }
    
    /**
     * 开始媒体音频捕获（需要MediaProjection）
     */
    public final boolean startMediaCapture(@org.jetbrains.annotations.Nullable()
    android.media.projection.MediaProjection mediaProjection, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super byte[], kotlin.Unit> onDataCallback) {
        return false;
    }
    
    /**
     * 媒体音频捕获线程 - 🚀 零拷贝优化版本
     */
    private final void captureMediaAudio(int bufferSize) {
    }
    
    /**
     * 🚀 零拷贝音频缓冲区：智能捕获缓冲区重用策略
     */
    private final byte[] getOptimalCaptureBuffer(byte[] sourceBuffer, int actualSize, com.example.castapp.audio.AudioBufferPool bufferPool) {
        return null;
    }
    
    /**
     * 停止麦克风捕获 - 🔥 修改：使用MicrophoneManager
     */
    public final void stopMicCapture() {
    }
    
    /**
     * 🔥 备用方案：停止AudioRecord捕获
     */
    private final void stopMicCaptureWithAudioRecord() {
    }
    
    /**
     * 停止媒体音频捕获
     */
    public final void stopMediaCapture() {
    }
    
    /**
     * 安全释放AudioRecord资源
     */
    private final void safeReleaseAudioRecord(android.media.AudioRecord audioRecord, java.lang.String type) {
    }
    
    /**
     * 停止所有音频捕获
     */
    public final void stopAll() {
    }
    
    /**
     * 检查是否正在捕获
     */
    public final boolean isMicCapturing() {
        return false;
    }
    
    public final boolean isMediaCapturing() {
        return false;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0007"}, d2 = {"Lcom/example/castapp/audio/AudioCaptureManager$Companion;", "", "()V", "AUDIO_FORMAT", "", "MEDIA_CHANNEL_CONFIG", "SAMPLE_RATE", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}