package com.example.castapp.service;

/**
 * 🎛️ 远程接收端控制服务
 * 独立的前台服务，专门管理7777端口WebSocket连接
 * 完全独立于音视频服务的生命周期
 *
 * 主要功能：
 * - 管理7777端口WebSocket服务器
 * - 处理远程接收端设置控制消息
 * - 提供持续的双向控制同步
 * - 不受音视频服务状态影响
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0006\u0018\u0000 \u00172\u00020\u0001:\u0001\u0017B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\t\u001a\u0004\u0018\u00010\u0006J\u0014\u0010\n\u001a\u0004\u0018\u00010\u000b2\b\u0010\f\u001a\u0004\u0018\u00010\rH\u0016J\b\u0010\u000e\u001a\u00020\u000fH\u0016J\b\u0010\u0010\u001a\u00020\u000fH\u0016J\"\u0010\u0011\u001a\u00020\u00122\b\u0010\f\u001a\u0004\u0018\u00010\r2\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0014\u001a\u00020\u0012H\u0016J\b\u0010\u0015\u001a\u00020\u000fH\u0002J\b\u0010\u0016\u001a\u00020\u000fH\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0018"}, d2 = {"Lcom/example/castapp/service/RemoteReceiverService;", "Landroid/app/Service;", "()V", "isRunning", "", "remoteControlServer", "Lcom/example/castapp/remote/RemoteReceiverControlServer;", "stateManager", "Lcom/example/castapp/manager/StateManager;", "getRemoteControlServer", "onBind", "Landroid/os/IBinder;", "intent", "Landroid/content/Intent;", "onCreate", "", "onDestroy", "onStartCommand", "", "flags", "startId", "startRemoteControlServer", "stopRemoteControlServer", "Companion", "app_debug"})
public final class RemoteReceiverService extends android.app.Service {
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_START_REMOTE_CONTROL = "com.example.castapp.START_REMOTE_CONTROL";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_STOP_REMOTE_CONTROL = "com.example.castapp.STOP_REMOTE_CONTROL";
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.example.castapp.service.RemoteReceiverService serviceInstance;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.remote.RemoteReceiverControlServer remoteControlServer;
    private com.example.castapp.manager.StateManager stateManager;
    private boolean isRunning = false;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.service.RemoteReceiverService.Companion Companion = null;
    
    public RemoteReceiverService() {
        super();
    }
    
    @java.lang.Override()
    public void onCreate() {
    }
    
    @java.lang.Override()
    public int onStartCommand(@org.jetbrains.annotations.Nullable()
    android.content.Intent intent, int flags, int startId) {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.os.IBinder onBind(@org.jetbrains.annotations.Nullable()
    android.content.Intent intent) {
        return null;
    }
    
    /**
     * 启动远程控制服务器
     */
    private final void startRemoteControlServer() {
    }
    
    /**
     * 停止远程控制服务器
     */
    private final void stopRemoteControlServer() {
    }
    
    @java.lang.Override()
    public void onDestroy() {
    }
    
    /**
     * 获取远程控制服务器实例
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.remote.RemoteReceiverControlServer getRemoteControlServer() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\b\u0010\b\u001a\u0004\u0018\u00010\u0007J\u0006\u0010\t\u001a\u00020\nR\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0006\u001a\u0004\u0018\u00010\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/example/castapp/service/RemoteReceiverService$Companion;", "", "()V", "ACTION_START_REMOTE_CONTROL", "", "ACTION_STOP_REMOTE_CONTROL", "serviceInstance", "Lcom/example/castapp/service/RemoteReceiverService;", "getInstance", "isServiceRunning", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * 检查服务是否运行中
         */
        public final boolean isServiceRunning() {
            return false;
        }
        
        /**
         * 获取服务实例
         */
        @org.jetbrains.annotations.Nullable()
        public final com.example.castapp.service.RemoteReceiverService getInstance() {
            return null;
        }
    }
}