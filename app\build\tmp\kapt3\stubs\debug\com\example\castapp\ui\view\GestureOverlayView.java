package com.example.castapp.ui.view;

/**
 * 手势覆盖层视图 - 简化版
 * 专门用于检测"C"字母手势
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000Z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\u0018\u00002\u00020\u0001:\u0001&B%\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u001e\u0010\u0010\u001a\u00020\u000f2\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\r0\u00122\u0006\u0010\u0013\u001a\u00020\u0014H\u0002J\u0006\u0010\u0015\u001a\u00020\u0016J\u0018\u0010\u0017\u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u00142\u0006\u0010\u0019\u001a\u00020\u0014H\u0002J\b\u0010\u001a\u001a\u00020\u000fH\u0002J\u0018\u0010\u001b\u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u00142\u0006\u0010\u0019\u001a\u00020\u0014H\u0002J\u0010\u0010\u001c\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u001eH\u0014J\u0010\u0010\u001f\u001a\u00020\u000f2\u0006\u0010 \u001a\u00020!H\u0016J\b\u0010\"\u001a\u00020\u000fH\u0016J\u000e\u0010#\u001a\u00020\u00162\u0006\u0010$\u001a\u00020\nJ\u0018\u0010%\u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u00142\u0006\u0010\u0019\u001a\u00020\u0014H\u0002R\u0010\u0010\t\u001a\u0004\u0018\u00010\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\'"}, d2 = {"Lcom/example/castapp/ui/view/GestureOverlayView;", "Landroid/view/View;", "context", "Landroid/content/Context;", "attrs", "Landroid/util/AttributeSet;", "defStyleAttr", "", "(Landroid/content/Context;Landroid/util/AttributeSet;I)V", "gestureListener", "Lcom/example/castapp/ui/view/GestureOverlayView$OnGestureListener;", "gesturePoints", "", "Landroid/graphics/PointF;", "isDrawing", "", "checkSimpleCurve", "points", "", "centerX", "", "clearGesture", "", "continueGesture", "x", "y", "detectCLetter", "endGesture", "onDraw", "canvas", "Landroid/graphics/Canvas;", "onTouchEvent", "event", "Landroid/view/MotionEvent;", "performClick", "setOnGestureListener", "listener", "startGesture", "OnGestureListener", "app_debug"})
public final class GestureOverlayView extends android.view.View {
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<android.graphics.PointF> gesturePoints = null;
    private boolean isDrawing = false;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.view.GestureOverlayView.OnGestureListener gestureListener;
    
    @kotlin.jvm.JvmOverloads()
    public GestureOverlayView(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    android.util.AttributeSet attrs, int defStyleAttr) {
        super(null);
    }
    
    public final void setOnGestureListener(@org.jetbrains.annotations.NotNull()
    com.example.castapp.ui.view.GestureOverlayView.OnGestureListener listener) {
    }
    
    @java.lang.Override()
    protected void onDraw(@org.jetbrains.annotations.NotNull()
    android.graphics.Canvas canvas) {
    }
    
    @java.lang.Override()
    public boolean onTouchEvent(@org.jetbrains.annotations.NotNull()
    android.view.MotionEvent event) {
        return false;
    }
    
    @java.lang.Override()
    public boolean performClick() {
        return false;
    }
    
    private final void startGesture(float x, float y) {
    }
    
    private final void continueGesture(float x, float y) {
    }
    
    private final void endGesture(float x, float y) {
    }
    
    /**
     * 平衡的C字母检测算法 - 既准确又不过分严格
     */
    private final boolean detectCLetter() {
        return false;
    }
    
    /**
     * 简化的弧形检查
     */
    private final boolean checkSimpleCurve(java.util.List<? extends android.graphics.PointF> points, float centerX) {
        return false;
    }
    
    /**
     * 清除手势数据
     */
    public final void clearGesture() {
    }
    
    @kotlin.jvm.JvmOverloads()
    public GestureOverlayView(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super(null);
    }
    
    @kotlin.jvm.JvmOverloads()
    public GestureOverlayView(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    android.util.AttributeSet attrs) {
        super(null);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\bf\u0018\u00002\u00020\u0001J\b\u0010\u0002\u001a\u00020\u0003H&\u00a8\u0006\u0004"}, d2 = {"Lcom/example/castapp/ui/view/GestureOverlayView$OnGestureListener;", "", "onCLetterDetected", "", "app_debug"})
    public static abstract interface OnGestureListener {
        
        public abstract void onCLetterDetected();
    }
}