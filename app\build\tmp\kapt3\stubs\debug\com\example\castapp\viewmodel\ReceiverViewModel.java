package com.example.castapp.viewmodel;

/**
 * 接收端ViewModel
 * 管理接收端服务状态和网络信息
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0013\u0018\u0000 62\u00020\u0001:\u00016B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\b\u0010#\u001a\u00020$H\u0002J\b\u0010%\u001a\u00020$H\u0002J\b\u0010&\u001a\u00020$H\u0014J\u0010\u0010\'\u001a\u00020$2\u0006\u0010(\u001a\u00020\u0007H\u0002J\u000e\u0010)\u001a\u00020$2\u0006\u0010*\u001a\u00020\u0007J\u000e\u0010+\u001a\u00020$2\u0006\u0010\u0018\u001a\u00020\rJ\b\u0010,\u001a\u00020$H\u0002J\u0006\u0010-\u001a\u00020$J\b\u0010.\u001a\u00020$H\u0002J\b\u0010/\u001a\u00020$H\u0002J\u0006\u00100\u001a\u00020$J\b\u00101\u001a\u00020$H\u0002J\u0006\u00102\u001a\u00020$J\b\u00103\u001a\u00020$H\u0002J\u000e\u00104\u001a\u00020\u00072\u0006\u00105\u001a\u00020\u000bR\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\r0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00070\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0012R\u0017\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00070\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0012R\u0017\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00070\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0012R\u0017\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00070\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0012R\u0017\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0012R\u0017\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\r0\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0012R\u0014\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\r0\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0012R\u0014\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00070\u001eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001f\u001a\u00020 X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010!\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u0012\u00a8\u00067"}, d2 = {"Lcom/example/castapp/viewmodel/ReceiverViewModel;", "Landroidx/lifecycle/AndroidViewModel;", "application", "Landroid/app/Application;", "(Landroid/app/Application;)V", "_isFixedWebSocketLoading", "Landroidx/lifecycle/MutableLiveData;", "", "_isFixedWebSocketRunning", "_isLoading", "_localIpAddress", "", "_port", "", "_serverStatusText", "_toastMessage", "isFixedWebSocketLoading", "Landroidx/lifecycle/LiveData;", "()Landroidx/lifecycle/LiveData;", "isFixedWebSocketRunning", "isLoading", "isReceivingServiceRunning", "localIpAddress", "getLocalIpAddress", "port", "getPort", "receivingPort", "serverStatusText", "getServerStatusText", "serviceStatusObserver", "Landroidx/lifecycle/Observer;", "stateManager", "Lcom/example/castapp/manager/StateManager;", "toastMessage", "getToastMessage", "loadFixedWebSocketState", "", "loadLocalIpAddress", "onCleared", "saveFixedWebSocketState", "enabled", "setAudioOutputMode", "isSpeakerMode", "setPort", "startFixedWebSocketServer", "startFixedWebSocketServerFromUI", "startServer", "stopFixedWebSocketServer", "stopFixedWebSocketServerFromUI", "stopServer", "toggleServer", "updateServerStatus", "validatePort", "portText", "Companion", "app_debug"})
public final class ReceiverViewModel extends androidx.lifecycle.AndroidViewModel {
    private static final int DEFAULT_PORT = 8888;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.StateManager stateManager = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.Boolean> isReceivingServiceRunning = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.Integer> receivingPort = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.String> _localIpAddress = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.String> localIpAddress = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.Integer> _port = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.Integer> port = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.String> _toastMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.String> toastMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.Boolean> _isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.Boolean> isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.String> _serverStatusText = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.String> serverStatusText = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.Boolean> _isFixedWebSocketRunning = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.Boolean> isFixedWebSocketRunning = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.Boolean> _isFixedWebSocketLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.Boolean> isFixedWebSocketLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.Observer<java.lang.Boolean> serviceStatusObserver = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.viewmodel.ReceiverViewModel.Companion Companion = null;
    
    public ReceiverViewModel(@org.jetbrains.annotations.NotNull()
    android.app.Application application) {
        super(null);
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.Boolean> isReceivingServiceRunning() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.String> getLocalIpAddress() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.Integer> getPort() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.String> getToastMessage() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.Boolean> isLoading() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.String> getServerStatusText() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.Boolean> isFixedWebSocketRunning() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.Boolean> isFixedWebSocketLoading() {
        return null;
    }
    
    /**
     * 加载本地IP地址
     */
    private final void loadLocalIpAddress() {
    }
    
    /**
     * 更新服务器状态显示
     */
    private final void updateServerStatus() {
    }
    
    /**
     * 设置端口
     */
    public final void setPort(int port) {
    }
    
    /**
     * 设置音频输出模式
     */
    public final void setAudioOutputMode(boolean isSpeakerMode) {
    }
    
    /**
     * 切换服务器状态
     */
    public final void toggleServer() {
    }
    
    /**
     * 启动服务器
     */
    private final void startServer() {
    }
    
    /**
     * 停止服务器
     */
    private final void stopServer() {
    }
    
    /**
     * 加载固定端口WebSocket服务器状态
     */
    private final void loadFixedWebSocketState() {
    }
    
    /**
     * 从UI启动固定端口WebSocket服务器
     */
    public final void startFixedWebSocketServerFromUI() {
    }
    
    /**
     * 从UI停止固定端口WebSocket服务器
     */
    public final void stopFixedWebSocketServerFromUI() {
    }
    
    /**
     * 启动固定端口WebSocket服务器
     */
    private final void startFixedWebSocketServer() {
    }
    
    /**
     * 停止固定端口WebSocket服务器
     */
    private final void stopFixedWebSocketServer() {
    }
    
    /**
     * 保存固定端口WebSocket服务器状态
     */
    private final void saveFixedWebSocketState(boolean enabled) {
    }
    
    /**
     * 验证端口号
     */
    public final boolean validatePort(@org.jetbrains.annotations.NotNull()
    java.lang.String portText) {
        return false;
    }
    
    @java.lang.Override()
    protected void onCleared() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/example/castapp/viewmodel/ReceiverViewModel$Companion;", "", "()V", "DEFAULT_PORT", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}