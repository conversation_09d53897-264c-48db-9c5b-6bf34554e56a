package com.example.castapp.database;

/**
 * CastApp应用的Room数据库
 * 管理窗口布局相关的数据存储
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\'\u0018\u0000 \u00052\u00020\u0001:\u0001\u0005B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H&\u00a8\u0006\u0006"}, d2 = {"Lcom/example/castapp/database/CastAppDatabase;", "Landroidx/room/RoomDatabase;", "()V", "windowLayoutDao", "Lcom/example/castapp/database/dao/WindowLayoutDao;", "Companion", "app_debug"})
@androidx.room.Database(entities = {com.example.castapp.database.entity.WindowLayoutEntity.class, com.example.castapp.database.entity.WindowLayoutItemEntity.class}, version = 11, exportSchema = false)
@androidx.room.TypeConverters(value = {com.example.castapp.database.converter.DateConverter.class})
public abstract class CastAppDatabase extends androidx.room.RoomDatabase {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String DATABASE_NAME = "cast_app_database";
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.example.castapp.database.CastAppDatabase INSTANCE;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.database.CastAppDatabase.Companion Companion = null;
    
    public CastAppDatabase() {
        super();
    }
    
    /**
     * 获取窗口布局数据访问对象
     */
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.castapp.database.dao.WindowLayoutDao windowLayoutDao();
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0007\u001a\u00020\u00062\u0006\u0010\b\u001a\u00020\tR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/example/castapp/database/CastAppDatabase$Companion;", "", "()V", "DATABASE_NAME", "", "INSTANCE", "Lcom/example/castapp/database/CastAppDatabase;", "getDatabase", "context", "Landroid/content/Context;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * 获取数据库实例（单例模式）
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.database.CastAppDatabase getDatabase(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
            return null;
        }
    }
}