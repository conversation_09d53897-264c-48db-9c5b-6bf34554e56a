package com.example.castapp.utils;

/**
 * 字体预设管理器
 * 负责管理系统预设字体和用户自定义字体
 * 提供字体的添加、删除、获取等功能
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000V\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010#\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0016\b\u00c6\u0002\u0018\u00002\u00020\u0001:\u0003/01B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0016\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u00042\u0006\u0010\u0017\u001a\u00020\u0018J\u000e\u0010\u0019\u001a\u00020\u00152\u0006\u0010\u001a\u001a\u00020\tJ\b\u0010\u001b\u001a\u00020\u001cH\u0002J\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\t0\bJ\n\u0010\u001e\u001a\u0004\u0018\u00010\fH\u0002J\u000e\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\t0\bH\u0002J\u0010\u0010 \u001a\u0004\u0018\u00010\t2\u0006\u0010\u0016\u001a\u00020\u0004J\n\u0010!\u001a\u0004\u0018\u00010\u0013H\u0002J\u000e\u0010\"\u001a\u00020\u001c2\u0006\u0010#\u001a\u00020\fJ\u0010\u0010$\u001a\u00020\u001c2\u0006\u0010\u001a\u001a\u00020\tH\u0002J\u0010\u0010%\u001a\u00020\u001c2\u0006\u0010\u001a\u001a\u00020\tH\u0002J\b\u0010&\u001a\u00020\u001cH\u0002J\u0018\u0010\'\u001a\u00020\u001c2\u0006\u0010(\u001a\u00020\t2\u0006\u0010)\u001a\u00020\tH\u0002J\u0006\u0010*\u001a\u00020\u001cJ\u0016\u0010+\u001a\u00020\u001c2\f\u0010,\u001a\b\u0012\u0004\u0012\u00020\t0\bH\u0002J\u0016\u0010-\u001a\u00020\u00152\u0006\u0010(\u001a\u00020\t2\u0006\u0010.\u001a\u00020\u0004R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\n\u001a\n\u0012\u0004\u0012\u00020\f\u0018\u00010\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00110\u0010X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0012\u001a\u0004\u0018\u00010\u0013X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u00062"}, d2 = {"Lcom/example/castapp/utils/FontPresetManager;", "", "()V", "FONTS_DIR", "", "KEY_CUSTOM_FONTS", "PREFS_NAME", "PRESET_FONTS", "", "Lcom/example/castapp/utils/FontPresetManager$FontItem;", "contextRef", "Ljava/lang/ref/WeakReference;", "Landroid/content/Context;", "gson", "Lcom/google/gson/Gson;", "listeners", "", "Lcom/example/castapp/utils/FontPresetManager$FontPresetListener;", "sharedPreferences", "Landroid/content/SharedPreferences;", "addCustomFont", "", "name", "fontFile", "Ljava/io/File;", "deleteCustomFont", "fontItem", "ensureFontsDirectoryExists", "", "getAllFonts", "getContext", "getCustomFonts", "getFontByName", "getSharedPreferences", "initialize", "context", "notifyFontAdded", "notifyFontDeleted", "notifyFontListReset", "notifyFontNameUpdated", "oldFontItem", "newFontItem", "resetToDefault", "saveCustomFonts", "fonts", "updateCustomFontName", "newName", "FontItem", "FontPresetListener", "SerializableFontItem", "app_debug"})
public final class FontPresetManager {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String PREFS_NAME = "font_preset_prefs";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_CUSTOM_FONTS = "custom_fonts";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String FONTS_DIR = "custom_fonts";
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<com.example.castapp.utils.FontPresetManager.FontItem> PRESET_FONTS = null;
    @org.jetbrains.annotations.Nullable()
    private static java.lang.ref.WeakReference<android.content.Context> contextRef;
    @org.jetbrains.annotations.Nullable()
    private static android.content.SharedPreferences sharedPreferences;
    @org.jetbrains.annotations.NotNull()
    private static final com.google.gson.Gson gson = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.util.Set<com.example.castapp.utils.FontPresetManager.FontPresetListener> listeners = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.utils.FontPresetManager INSTANCE = null;
    
    private FontPresetManager() {
        super();
    }
    
    /**
     * 安全获取Context
     */
    private final android.content.Context getContext() {
        return null;
    }
    
    /**
     * 安全获取SharedPreferences
     */
    private final android.content.SharedPreferences getSharedPreferences() {
        return null;
    }
    
    /**
     * 初始化管理器
     */
    public final void initialize(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    /**
     * 获取完整的字体列表（预设 + 自定义）
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.castapp.utils.FontPresetManager.FontItem> getAllFonts() {
        return null;
    }
    
    /**
     * 根据字体名称获取字体项
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.utils.FontPresetManager.FontItem getFontByName(@org.jetbrains.annotations.NotNull()
    java.lang.String name) {
        return null;
    }
    
    /**
     * 获取自定义字体列表
     */
    private final java.util.List<com.example.castapp.utils.FontPresetManager.FontItem> getCustomFonts() {
        return null;
    }
    
    /**
     * 保存自定义字体列表
     */
    private final void saveCustomFonts(java.util.List<com.example.castapp.utils.FontPresetManager.FontItem> fonts) {
    }
    
    /**
     * 添加自定义字体
     */
    public final boolean addCustomFont(@org.jetbrains.annotations.NotNull()
    java.lang.String name, @org.jetbrains.annotations.NotNull()
    java.io.File fontFile) {
        return false;
    }
    
    /**
     * 删除自定义字体
     */
    public final boolean deleteCustomFont(@org.jetbrains.annotations.NotNull()
    com.example.castapp.utils.FontPresetManager.FontItem fontItem) {
        return false;
    }
    
    /**
     * 更新自定义字体名称
     */
    public final boolean updateCustomFontName(@org.jetbrains.annotations.NotNull()
    com.example.castapp.utils.FontPresetManager.FontItem oldFontItem, @org.jetbrains.annotations.NotNull()
    java.lang.String newName) {
        return false;
    }
    
    /**
     * 重置为默认字体设置（清除所有自定义字体）
     */
    public final void resetToDefault() {
    }
    
    /**
     * 确保字体目录存在
     */
    private final void ensureFontsDirectoryExists() {
    }
    
    /**
     * 通知字体添加
     */
    private final void notifyFontAdded(com.example.castapp.utils.FontPresetManager.FontItem fontItem) {
    }
    
    /**
     * 通知字体删除
     */
    private final void notifyFontDeleted(com.example.castapp.utils.FontPresetManager.FontItem fontItem) {
    }
    
    /**
     * 通知字体名称更新
     */
    private final void notifyFontNameUpdated(com.example.castapp.utils.FontPresetManager.FontItem oldFontItem, com.example.castapp.utils.FontPresetManager.FontItem newFontItem) {
    }
    
    /**
     * 通知字体列表重置
     */
    private final void notifyFontListReset() {
    }
    
    /**
     * 字体数据项
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u000f\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\b\u0018\u00002\u00020\u0001B5\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t\u00a2\u0006\u0002\u0010\nJ\t\u0010\u0010\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0011\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0006H\u00c6\u0003J\u000b\u0010\u0013\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0014\u001a\u0004\u0018\u00010\tH\u00c2\u0003J?\u0010\u0015\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\tH\u00c6\u0001J\u0013\u0010\u0016\u001a\u00020\u00062\b\u0010\u0017\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0018\u001a\u00020\u0019H\u00d6\u0001J\b\u0010\u001a\u001a\u0004\u0018\u00010\tJ\t\u0010\u001b\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\fR\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u000eR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\fR\u0010\u0010\b\u001a\u0004\u0018\u00010\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001c"}, d2 = {"Lcom/example/castapp/utils/FontPresetManager$FontItem;", "", "name", "", "fontFamily", "isPreset", "", "filePath", "presetTypeface", "Landroid/graphics/Typeface;", "(Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;Landroid/graphics/Typeface;)V", "getFilePath", "()Ljava/lang/String;", "getFontFamily", "()Z", "getName", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "other", "hashCode", "", "loadTypeface", "toString", "app_debug"})
    public static final class FontItem {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String name = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String fontFamily = null;
        private final boolean isPreset = false;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String filePath = null;
        @kotlin.jvm.Transient()
        @org.jetbrains.annotations.Nullable()
        private final transient android.graphics.Typeface presetTypeface = null;
        
        public FontItem(@org.jetbrains.annotations.NotNull()
        java.lang.String name, @org.jetbrains.annotations.NotNull()
        java.lang.String fontFamily, boolean isPreset, @org.jetbrains.annotations.Nullable()
        java.lang.String filePath, @org.jetbrains.annotations.Nullable()
        android.graphics.Typeface presetTypeface) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getName() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getFontFamily() {
            return null;
        }
        
        public final boolean isPreset() {
            return false;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getFilePath() {
            return null;
        }
        
        /**
         * 获取字体对象（延迟加载）
         */
        @org.jetbrains.annotations.Nullable()
        public final android.graphics.Typeface loadTypeface() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component2() {
            return null;
        }
        
        public final boolean component3() {
            return false;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component4() {
            return null;
        }
        
        private final android.graphics.Typeface component5() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.utils.FontPresetManager.FontItem copy(@org.jetbrains.annotations.NotNull()
        java.lang.String name, @org.jetbrains.annotations.NotNull()
        java.lang.String fontFamily, boolean isPreset, @org.jetbrains.annotations.Nullable()
        java.lang.String filePath, @org.jetbrains.annotations.Nullable()
        android.graphics.Typeface presetTypeface) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    /**
     * 字体预设列表变化监听器接口
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\u0010\u0010\u0006\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\b\u0010\u0007\u001a\u00020\u0003H&J\u0018\u0010\b\u001a\u00020\u00032\u0006\u0010\t\u001a\u00020\u00052\u0006\u0010\n\u001a\u00020\u0005H&\u00a8\u0006\u000b"}, d2 = {"Lcom/example/castapp/utils/FontPresetManager$FontPresetListener;", "", "onFontAdded", "", "fontItem", "Lcom/example/castapp/utils/FontPresetManager$FontItem;", "onFontDeleted", "onFontListReset", "onFontNameUpdated", "oldFontItem", "newFontItem", "app_debug"})
    public static abstract interface FontPresetListener {
        
        /**
         * 字体被添加时调用
         */
        public abstract void onFontAdded(@org.jetbrains.annotations.NotNull()
        com.example.castapp.utils.FontPresetManager.FontItem fontItem);
        
        /**
         * 字体被删除时调用
         */
        public abstract void onFontDeleted(@org.jetbrains.annotations.NotNull()
        com.example.castapp.utils.FontPresetManager.FontItem fontItem);
        
        /**
         * 字体名称被更新时调用
         */
        public abstract void onFontNameUpdated(@org.jetbrains.annotations.NotNull()
        com.example.castapp.utils.FontPresetManager.FontItem oldFontItem, @org.jetbrains.annotations.NotNull()
        com.example.castapp.utils.FontPresetManager.FontItem newFontItem);
        
        /**
         * 字体列表被重置时调用
         */
        public abstract void onFontListReset();
    }
    
    /**
     * 用于序列化的简化字体数据
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0082\b\u0018\u00002\u00020\u0001B\u001d\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0006J\t\u0010\u000b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\r\u001a\u00020\u0003H\u00c6\u0003J\'\u0010\u000e\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u000f\u001a\u00020\u00102\b\u0010\u0011\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\bR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\b\u00a8\u0006\u0015"}, d2 = {"Lcom/example/castapp/utils/FontPresetManager$SerializableFontItem;", "", "name", "", "fontFamily", "filePath", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "getFilePath", "()Ljava/lang/String;", "getFontFamily", "getName", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
    static final class SerializableFontItem {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String name = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String fontFamily = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String filePath = null;
        
        public SerializableFontItem(@org.jetbrains.annotations.NotNull()
        java.lang.String name, @org.jetbrains.annotations.NotNull()
        java.lang.String fontFamily, @org.jetbrains.annotations.NotNull()
        java.lang.String filePath) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getName() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getFontFamily() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getFilePath() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component3() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.utils.FontPresetManager.SerializableFontItem copy(@org.jetbrains.annotations.NotNull()
        java.lang.String name, @org.jetbrains.annotations.NotNull()
        java.lang.String fontFamily, @org.jetbrains.annotations.NotNull()
        java.lang.String filePath) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}