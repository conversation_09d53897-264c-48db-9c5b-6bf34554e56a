package com.example.castapp.database;

import androidx.annotation.NonNull;
import androidx.room.DatabaseConfiguration;
import androidx.room.InvalidationTracker;
import androidx.room.RoomDatabase;
import androidx.room.RoomOpenHelper;
import androidx.room.migration.AutoMigrationSpec;
import androidx.room.migration.Migration;
import androidx.room.util.DBUtil;
import androidx.room.util.TableInfo;
import androidx.sqlite.db.SupportSQLiteDatabase;
import androidx.sqlite.db.SupportSQLiteOpenHelper;
import com.example.castapp.database.dao.WindowLayoutDao;
import com.example.castapp.database.dao.WindowLayoutDao_Impl;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class CastAppDatabase_Impl extends CastAppDatabase {
  private volatile WindowLayoutDao _windowLayoutDao;

  @Override
  @NonNull
  protected SupportSQLiteOpenHelper createOpenHelper(@NonNull final DatabaseConfiguration config) {
    final SupportSQLiteOpenHelper.Callback _openCallback = new RoomOpenHelper(config, new RoomOpenHelper.Delegate(11) {
      @Override
      public void createAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("CREATE TABLE IF NOT EXISTS `window_layouts` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `layoutName` TEXT NOT NULL, `description` TEXT, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, `windowCount` INTEGER NOT NULL, `sortOrder` INTEGER NOT NULL, `isApplied` INTEGER NOT NULL)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `window_layout_items` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `layoutId` INTEGER NOT NULL, `orderIndex` INTEGER NOT NULL, `deviceName` TEXT, `deviceId` TEXT NOT NULL, `ipAddress` TEXT NOT NULL, `port` INTEGER NOT NULL, `positionX` REAL NOT NULL, `positionY` REAL NOT NULL, `scaleFactor` REAL NOT NULL, `rotationAngle` REAL NOT NULL, `isCropping` INTEGER NOT NULL, `isDragEnabled` INTEGER NOT NULL, `isScaleEnabled` INTEGER NOT NULL, `isRotationEnabled` INTEGER NOT NULL, `isVisible` INTEGER NOT NULL, `isMirrored` INTEGER NOT NULL, `cornerRadius` REAL NOT NULL, `alpha` REAL NOT NULL, `isControlEnabled` INTEGER NOT NULL, `isBorderEnabled` INTEGER NOT NULL, `borderColor` INTEGER NOT NULL, `borderWidth` REAL NOT NULL, `cropRect` TEXT, `baseWindowWidth` INTEGER NOT NULL, `baseWindowHeight` INTEGER NOT NULL, `note` TEXT, `textContent` TEXT, `textIsBold` INTEGER NOT NULL, `textIsItalic` INTEGER NOT NULL, `textFontSize` INTEGER NOT NULL, `textFontName` TEXT, `textFontFamily` TEXT, `textLineSpacing` REAL NOT NULL, `textAlignment` INTEGER NOT NULL, `richTextData` TEXT, `windowColorEnabled` INTEGER NOT NULL, `windowBackgroundColor` INTEGER NOT NULL, `mediaFileUri` TEXT, `mediaFileName` TEXT, `mediaContentType` TEXT, `videoPlayEnabled` INTEGER NOT NULL, `videoLoopCount` INTEGER NOT NULL, `videoVolume` INTEGER NOT NULL, `isLandscapeModeEnabled` INTEGER NOT NULL, FOREIGN KEY(`layoutId`) REFERENCES `window_layouts`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_window_layout_items_layoutId` ON `window_layout_items` (`layoutId`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)");
        db.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '73faa3c0cf738db5123eca80c543e024')");
      }

      @Override
      public void dropAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("DROP TABLE IF EXISTS `window_layouts`");
        db.execSQL("DROP TABLE IF EXISTS `window_layout_items`");
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onDestructiveMigration(db);
          }
        }
      }

      @Override
      public void onCreate(@NonNull final SupportSQLiteDatabase db) {
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onCreate(db);
          }
        }
      }

      @Override
      public void onOpen(@NonNull final SupportSQLiteDatabase db) {
        mDatabase = db;
        db.execSQL("PRAGMA foreign_keys = ON");
        internalInitInvalidationTracker(db);
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onOpen(db);
          }
        }
      }

      @Override
      public void onPreMigrate(@NonNull final SupportSQLiteDatabase db) {
        DBUtil.dropFtsSyncTriggers(db);
      }

      @Override
      public void onPostMigrate(@NonNull final SupportSQLiteDatabase db) {
      }

      @Override
      @NonNull
      public RoomOpenHelper.ValidationResult onValidateSchema(
          @NonNull final SupportSQLiteDatabase db) {
        final HashMap<String, TableInfo.Column> _columnsWindowLayouts = new HashMap<String, TableInfo.Column>(8);
        _columnsWindowLayouts.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayouts.put("layoutName", new TableInfo.Column("layoutName", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayouts.put("description", new TableInfo.Column("description", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayouts.put("createdAt", new TableInfo.Column("createdAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayouts.put("updatedAt", new TableInfo.Column("updatedAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayouts.put("windowCount", new TableInfo.Column("windowCount", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayouts.put("sortOrder", new TableInfo.Column("sortOrder", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayouts.put("isApplied", new TableInfo.Column("isApplied", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysWindowLayouts = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesWindowLayouts = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoWindowLayouts = new TableInfo("window_layouts", _columnsWindowLayouts, _foreignKeysWindowLayouts, _indicesWindowLayouts);
        final TableInfo _existingWindowLayouts = TableInfo.read(db, "window_layouts");
        if (!_infoWindowLayouts.equals(_existingWindowLayouts)) {
          return new RoomOpenHelper.ValidationResult(false, "window_layouts(com.example.castapp.database.entity.WindowLayoutEntity).\n"
                  + " Expected:\n" + _infoWindowLayouts + "\n"
                  + " Found:\n" + _existingWindowLayouts);
        }
        final HashMap<String, TableInfo.Column> _columnsWindowLayoutItems = new HashMap<String, TableInfo.Column>(45);
        _columnsWindowLayoutItems.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("layoutId", new TableInfo.Column("layoutId", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("orderIndex", new TableInfo.Column("orderIndex", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("deviceName", new TableInfo.Column("deviceName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("deviceId", new TableInfo.Column("deviceId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("ipAddress", new TableInfo.Column("ipAddress", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("port", new TableInfo.Column("port", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("positionX", new TableInfo.Column("positionX", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("positionY", new TableInfo.Column("positionY", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("scaleFactor", new TableInfo.Column("scaleFactor", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("rotationAngle", new TableInfo.Column("rotationAngle", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("isCropping", new TableInfo.Column("isCropping", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("isDragEnabled", new TableInfo.Column("isDragEnabled", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("isScaleEnabled", new TableInfo.Column("isScaleEnabled", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("isRotationEnabled", new TableInfo.Column("isRotationEnabled", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("isVisible", new TableInfo.Column("isVisible", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("isMirrored", new TableInfo.Column("isMirrored", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("cornerRadius", new TableInfo.Column("cornerRadius", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("alpha", new TableInfo.Column("alpha", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("isControlEnabled", new TableInfo.Column("isControlEnabled", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("isBorderEnabled", new TableInfo.Column("isBorderEnabled", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("borderColor", new TableInfo.Column("borderColor", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("borderWidth", new TableInfo.Column("borderWidth", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("cropRect", new TableInfo.Column("cropRect", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("baseWindowWidth", new TableInfo.Column("baseWindowWidth", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("baseWindowHeight", new TableInfo.Column("baseWindowHeight", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("note", new TableInfo.Column("note", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("textContent", new TableInfo.Column("textContent", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("textIsBold", new TableInfo.Column("textIsBold", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("textIsItalic", new TableInfo.Column("textIsItalic", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("textFontSize", new TableInfo.Column("textFontSize", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("textFontName", new TableInfo.Column("textFontName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("textFontFamily", new TableInfo.Column("textFontFamily", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("textLineSpacing", new TableInfo.Column("textLineSpacing", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("textAlignment", new TableInfo.Column("textAlignment", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("richTextData", new TableInfo.Column("richTextData", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("windowColorEnabled", new TableInfo.Column("windowColorEnabled", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("windowBackgroundColor", new TableInfo.Column("windowBackgroundColor", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("mediaFileUri", new TableInfo.Column("mediaFileUri", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("mediaFileName", new TableInfo.Column("mediaFileName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("mediaContentType", new TableInfo.Column("mediaContentType", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("videoPlayEnabled", new TableInfo.Column("videoPlayEnabled", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("videoLoopCount", new TableInfo.Column("videoLoopCount", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("videoVolume", new TableInfo.Column("videoVolume", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWindowLayoutItems.put("isLandscapeModeEnabled", new TableInfo.Column("isLandscapeModeEnabled", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysWindowLayoutItems = new HashSet<TableInfo.ForeignKey>(1);
        _foreignKeysWindowLayoutItems.add(new TableInfo.ForeignKey("window_layouts", "CASCADE", "NO ACTION", Arrays.asList("layoutId"), Arrays.asList("id")));
        final HashSet<TableInfo.Index> _indicesWindowLayoutItems = new HashSet<TableInfo.Index>(1);
        _indicesWindowLayoutItems.add(new TableInfo.Index("index_window_layout_items_layoutId", false, Arrays.asList("layoutId"), Arrays.asList("ASC")));
        final TableInfo _infoWindowLayoutItems = new TableInfo("window_layout_items", _columnsWindowLayoutItems, _foreignKeysWindowLayoutItems, _indicesWindowLayoutItems);
        final TableInfo _existingWindowLayoutItems = TableInfo.read(db, "window_layout_items");
        if (!_infoWindowLayoutItems.equals(_existingWindowLayoutItems)) {
          return new RoomOpenHelper.ValidationResult(false, "window_layout_items(com.example.castapp.database.entity.WindowLayoutItemEntity).\n"
                  + " Expected:\n" + _infoWindowLayoutItems + "\n"
                  + " Found:\n" + _existingWindowLayoutItems);
        }
        return new RoomOpenHelper.ValidationResult(true, null);
      }
    }, "73faa3c0cf738db5123eca80c543e024", "c93b7c145f9cc9163b1854850eaaf9ac");
    final SupportSQLiteOpenHelper.Configuration _sqliteConfig = SupportSQLiteOpenHelper.Configuration.builder(config.context).name(config.name).callback(_openCallback).build();
    final SupportSQLiteOpenHelper _helper = config.sqliteOpenHelperFactory.create(_sqliteConfig);
    return _helper;
  }

  @Override
  @NonNull
  protected InvalidationTracker createInvalidationTracker() {
    final HashMap<String, String> _shadowTablesMap = new HashMap<String, String>(0);
    final HashMap<String, Set<String>> _viewTables = new HashMap<String, Set<String>>(0);
    return new InvalidationTracker(this, _shadowTablesMap, _viewTables, "window_layouts","window_layout_items");
  }

  @Override
  public void clearAllTables() {
    super.assertNotMainThread();
    final SupportSQLiteDatabase _db = super.getOpenHelper().getWritableDatabase();
    final boolean _supportsDeferForeignKeys = android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP;
    try {
      if (!_supportsDeferForeignKeys) {
        _db.execSQL("PRAGMA foreign_keys = FALSE");
      }
      super.beginTransaction();
      if (_supportsDeferForeignKeys) {
        _db.execSQL("PRAGMA defer_foreign_keys = TRUE");
      }
      _db.execSQL("DELETE FROM `window_layouts`");
      _db.execSQL("DELETE FROM `window_layout_items`");
      super.setTransactionSuccessful();
    } finally {
      super.endTransaction();
      if (!_supportsDeferForeignKeys) {
        _db.execSQL("PRAGMA foreign_keys = TRUE");
      }
      _db.query("PRAGMA wal_checkpoint(FULL)").close();
      if (!_db.inTransaction()) {
        _db.execSQL("VACUUM");
      }
    }
  }

  @Override
  @NonNull
  protected Map<Class<?>, List<Class<?>>> getRequiredTypeConverters() {
    final HashMap<Class<?>, List<Class<?>>> _typeConvertersMap = new HashMap<Class<?>, List<Class<?>>>();
    _typeConvertersMap.put(WindowLayoutDao.class, WindowLayoutDao_Impl.getRequiredConverters());
    return _typeConvertersMap;
  }

  @Override
  @NonNull
  public Set<Class<? extends AutoMigrationSpec>> getRequiredAutoMigrationSpecs() {
    final HashSet<Class<? extends AutoMigrationSpec>> _autoMigrationSpecsSet = new HashSet<Class<? extends AutoMigrationSpec>>();
    return _autoMigrationSpecsSet;
  }

  @Override
  @NonNull
  public List<Migration> getAutoMigrations(
      @NonNull final Map<Class<? extends AutoMigrationSpec>, AutoMigrationSpec> autoMigrationSpecs) {
    final List<Migration> _autoMigrations = new ArrayList<Migration>();
    return _autoMigrations;
  }

  @Override
  public WindowLayoutDao windowLayoutDao() {
    if (_windowLayoutDao != null) {
      return _windowLayoutDao;
    } else {
      synchronized(this) {
        if(_windowLayoutDao == null) {
          _windowLayoutDao = new WindowLayoutDao_Impl(this);
        }
        return _windowLayoutDao;
      }
    }
  }
}
