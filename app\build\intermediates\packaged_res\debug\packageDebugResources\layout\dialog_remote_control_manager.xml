<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="8dp">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="8dp"
        android:gravity="center_vertical">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="遥控管理"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@android:color/black" />

        <ImageButton
            android:id="@+id/close_dialog_button"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_close"
            android:contentDescription="关闭窗口"
            android:scaleType="centerInside"
            android:alpha="0.7"/>
    </LinearLayout>

    <!-- 标签页导航 -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        app:tabMode="fixed"
        app:tabGravity="fill"
        app:tabIndicatorColor="@color/primary_blue"
        app:tabSelectedTextColor="@color/primary_blue"
        app:tabTextColor="#666666"
        app:tabIndicatorHeight="2dp"
        app:tabTextAppearance="@style/TabTextStyle" />

    <!-- 标签页内容容器 -->
    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/view_pager"
        android:layout_width="match_parent"
        android:layout_height="350dp" />

    <!-- 底部说明文字 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="提示：确保目标设备已开启相应功能"
        android:textSize="11sp"
        android:textColor="#999999"
        android:gravity="center"
        android:layout_marginTop="8dp"
        android:padding="4dp" />

</LinearLayout>
