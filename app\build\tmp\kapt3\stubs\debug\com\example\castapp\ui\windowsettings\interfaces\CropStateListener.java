package com.example.castapp.ui.windowsettings.interfaces;

/**
 * 裁剪状态监听接口
 * 用于监听投屏窗口的裁剪状态变化
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\u0012\u0010\u0006\u001a\u00020\u00032\b\u0010\u0007\u001a\u0004\u0018\u00010\bH&\u00a8\u0006\t"}, d2 = {"Lcom/example/castapp/ui/windowsettings/interfaces/CropStateListener;", "", "onCropModeChanged", "", "isCropping", "", "onCropRectChanged", "cropRect", "Landroid/graphics/RectF;", "app_debug"})
public abstract interface CropStateListener {
    
    /**
     * 裁剪模式状态发生变化时的回调
     * @param isCropping 是否正在裁剪模式中
     */
    public abstract void onCropModeChanged(boolean isCropping);
    
    /**
     * 裁剪区域发生变化时的回调
     * @param cropRect 裁剪区域比例，null表示无裁剪
     */
    public abstract void onCropRectChanged(@org.jetbrains.annotations.Nullable()
    android.graphics.RectF cropRect);
}