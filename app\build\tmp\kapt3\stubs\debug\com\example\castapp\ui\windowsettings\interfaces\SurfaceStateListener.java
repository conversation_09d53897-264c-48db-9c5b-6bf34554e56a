package com.example.castapp.ui.windowsettings.interfaces;

/**
 * Surface状态监听接口
 * 用于监听TextureView Surface的生命周期
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J\u001a\u0010\u0002\u001a\u00020\u00032\b\u0010\u0004\u001a\u0004\u0018\u00010\u00052\u0006\u0010\u0006\u001a\u00020\u0007H&J\u0010\u0010\b\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H&\u00a8\u0006\t"}, d2 = {"Lcom/example/castapp/ui/windowsettings/interfaces/SurfaceStateListener;", "", "onSurfaceAvailable", "", "surface", "Landroid/view/Surface;", "connectionId", "", "onSurfaceDestroyed", "app_debug"})
public abstract interface SurfaceStateListener {
    
    /**
     * Surface可用时的回调
     * @param surface Surface对象，null表示Surface不可用
     * @param connectionId 连接ID
     */
    public abstract void onSurfaceAvailable(@org.jetbrains.annotations.Nullable()
    android.view.Surface surface, @org.jetbrains.annotations.NotNull()
    java.lang.String connectionId);
    
    /**
     * Surface销毁时的回调
     * @param connectionId 连接ID
     */
    public abstract void onSurfaceDestroyed(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId);
}