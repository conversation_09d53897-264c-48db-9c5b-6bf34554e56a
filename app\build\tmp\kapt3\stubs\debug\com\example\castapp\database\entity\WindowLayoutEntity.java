package com.example.castapp.database.entity;

/**
 * 窗口布局实体类
 * 用于存储保存的窗口布局信息
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u001c\b\u0087\b\u0018\u00002\u00020\u0001BO\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\u0006\u0010\t\u001a\u00020\b\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\b\b\u0002\u0010\f\u001a\u00020\u000b\u0012\b\b\u0002\u0010\r\u001a\u00020\u000e\u00a2\u0006\u0002\u0010\u000fJ\t\u0010\u001c\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010\u001e\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\bH\u00c6\u0003J\t\u0010 \u001a\u00020\bH\u00c6\u0003J\t\u0010!\u001a\u00020\u000bH\u00c6\u0003J\t\u0010\"\u001a\u00020\u000bH\u00c6\u0003J\t\u0010#\u001a\u00020\u000eH\u00c6\u0003J[\u0010$\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\b2\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\u000b2\b\b\u0002\u0010\r\u001a\u00020\u000eH\u00c6\u0001J\u0013\u0010%\u001a\u00020\u000e2\b\u0010&\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u0006\u0010\'\u001a\u00020\u0005J\t\u0010(\u001a\u00020\u000bH\u00d6\u0001J\t\u0010)\u001a\u00020\u0005H\u00d6\u0001R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0013\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0011\u0010\r\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u0016R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0013R\u0011\u0010\f\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0011\u0010\t\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0011R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0019\u00a8\u0006*"}, d2 = {"Lcom/example/castapp/database/entity/WindowLayoutEntity;", "", "id", "", "layoutName", "", "description", "createdAt", "Ljava/util/Date;", "updatedAt", "windowCount", "", "sortOrder", "isApplied", "", "(JLjava/lang/String;Ljava/lang/String;Ljava/util/Date;Ljava/util/Date;IIZ)V", "getCreatedAt", "()Ljava/util/Date;", "getDescription", "()Ljava/lang/String;", "getId", "()J", "()Z", "getLayoutName", "getSortOrder", "()I", "getUpdatedAt", "getWindowCount", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "equals", "other", "getFormattedCreatedTime", "hashCode", "toString", "app_debug"})
@androidx.room.Entity(tableName = "window_layouts")
public final class WindowLayoutEntity {
    @androidx.room.PrimaryKey(autoGenerate = true)
    private final long id = 0L;
    
    /**
     * 布局名称（用户输入的名称，如"会议室布局1"）
     */
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String layoutName = null;
    
    /**
     * 布局描述（可选）
     */
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String description = null;
    
    /**
     * 创建时间
     */
    @org.jetbrains.annotations.NotNull()
    private final java.util.Date createdAt = null;
    
    /**
     * 最后修改时间
     */
    @org.jetbrains.annotations.NotNull()
    private final java.util.Date updatedAt = null;
    
    /**
     * 布局中包含的窗口数量
     */
    private final int windowCount = 0;
    
    /**
     * 排序顺序（用于拖拽排序，数值越小越靠前）
     */
    private final int sortOrder = 0;
    
    /**
     * 🐾 是否为当前应用的布局（用于状态持久化）
     */
    private final boolean isApplied = false;
    
    public WindowLayoutEntity(long id, @org.jetbrains.annotations.NotNull()
    java.lang.String layoutName, @org.jetbrains.annotations.Nullable()
    java.lang.String description, @org.jetbrains.annotations.NotNull()
    java.util.Date createdAt, @org.jetbrains.annotations.NotNull()
    java.util.Date updatedAt, int windowCount, int sortOrder, boolean isApplied) {
        super();
    }
    
    public final long getId() {
        return 0L;
    }
    
    /**
     * 布局名称（用户输入的名称，如"会议室布局1"）
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getLayoutName() {
        return null;
    }
    
    /**
     * 布局描述（可选）
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getDescription() {
        return null;
    }
    
    /**
     * 创建时间
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Date getCreatedAt() {
        return null;
    }
    
    /**
     * 最后修改时间
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Date getUpdatedAt() {
        return null;
    }
    
    /**
     * 布局中包含的窗口数量
     */
    public final int getWindowCount() {
        return 0;
    }
    
    /**
     * 排序顺序（用于拖拽排序，数值越小越靠前）
     */
    public final int getSortOrder() {
        return 0;
    }
    
    /**
     * 🐾 是否为当前应用的布局（用于状态持久化）
     */
    public final boolean isApplied() {
        return false;
    }
    
    /**
     * 获取格式化的创建时间字符串
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getFormattedCreatedTime() {
        return null;
    }
    
    public final long component1() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Date component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Date component5() {
        return null;
    }
    
    public final int component6() {
        return 0;
    }
    
    public final int component7() {
        return 0;
    }
    
    public final boolean component8() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.database.entity.WindowLayoutEntity copy(long id, @org.jetbrains.annotations.NotNull()
    java.lang.String layoutName, @org.jetbrains.annotations.Nullable()
    java.lang.String description, @org.jetbrains.annotations.NotNull()
    java.util.Date createdAt, @org.jetbrains.annotations.NotNull()
    java.util.Date updatedAt, int windowCount, int sortOrder, boolean isApplied) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}