package com.example.castapp.ui.dialog;

/**
 * 远程发送端控制对话框
 * 复制SenderDialogFragment的功能，用于远程控制
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00a8\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010$\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u001b\u0018\u00002\u00020\u0001B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u000e\u0010%\u001a\u00020\u001d2\u0006\u0010&\u001a\u00020\'J\u0010\u0010(\u001a\u00020\u001d2\u0006\u0010&\u001a\u00020\'H\u0002J\u000e\u0010)\u001a\u00020\u001d2\u0006\u0010&\u001a\u00020\'J\u000e\u0010*\u001a\u00020\u001d2\u0006\u0010&\u001a\u00020\'J\u0018\u0010+\u001a\u00020\u001d2\u0006\u0010,\u001a\u00020-2\u0006\u0010.\u001a\u00020/H\u0002J\u0018\u00100\u001a\u00020\u001d2\u0006\u0010,\u001a\u00020-2\u0006\u0010.\u001a\u00020/H\u0002J\u0018\u00101\u001a\u00020\u001d2\u0006\u0010,\u001a\u00020-2\u0006\u0010.\u001a\u00020/H\u0002J\u000e\u00102\u001a\u00020\u001d2\u0006\u0010&\u001a\u00020\'J\u0010\u00103\u001a\u00020\u001d2\u0006\u00104\u001a\u000205H\u0002J\u0012\u00106\u001a\u00020\u001d2\b\u00107\u001a\u0004\u0018\u000108H\u0016J&\u00109\u001a\u0004\u0018\u0001052\u0006\u0010:\u001a\u00020;2\b\u0010<\u001a\u0004\u0018\u00010=2\b\u00107\u001a\u0004\u0018\u000108H\u0016J\u0010\u0010>\u001a\u00020\u001d2\u0006\u0010?\u001a\u00020@H\u0016J\u001a\u0010A\u001a\u00020\u001d2\u0006\u00104\u001a\u0002052\b\u00107\u001a\u0004\u0018\u000108H\u0016J\u001a\u0010B\u001a\u0004\u0018\u00010-2\u000e\u0010C\u001a\n\u0012\u0002\b\u0003\u0012\u0002\b\u00030DH\u0002J\b\u0010E\u001a\u00020\u001dH\u0002J\u0018\u0010F\u001a\u00020\u001d2\u0006\u0010G\u001a\u00020H2\u0006\u0010I\u001a\u00020JH\u0002J\u0010\u0010K\u001a\u00020\u001d2\u0006\u0010L\u001a\u00020JH\u0002J\u0010\u0010M\u001a\u00020\u001d2\u0006\u0010N\u001a\u00020HH\u0002J \u0010O\u001a\u00020\u001d2\u0006\u0010N\u001a\u00020H2\u0006\u0010P\u001a\u00020H2\u0006\u0010Q\u001a\u00020JH\u0002J\u0010\u0010R\u001a\u00020\u001d2\u0006\u0010S\u001a\u00020JH\u0002J\u0018\u0010T\u001a\u00020\u001d2\u0006\u0010U\u001a\u00020H2\u0006\u0010V\u001a\u00020JH\u0002J\u0014\u0010W\u001a\u00020\u001d2\f\u0010X\u001a\b\u0012\u0004\u0012\u00020\u001d0\u001cJ\b\u0010Y\u001a\u00020\u001dH\u0002J\b\u0010Z\u001a\u00020\u001dH\u0002J\b\u0010[\u001a\u00020\u001dH\u0002J \u0010\\\u001a\u00020\u001d2\u0006\u0010]\u001a\u00020\n2\u0006\u0010^\u001a\u00020\f2\u0006\u0010U\u001a\u00020HH\u0002J\b\u0010_\u001a\u00020\u001dH\u0002J\u0010\u0010`\u001a\u00020\u001d2\u0006\u0010,\u001a\u00020-H\u0002J\u0010\u0010a\u001a\u00020\u001d2\u0006\u0010,\u001a\u00020-H\u0002J\u0010\u0010b\u001a\u00020\u001d2\u0006\u0010L\u001a\u00020JH\u0002J\b\u0010c\u001a\u00020\u001dH\u0002J\u0010\u0010d\u001a\u00020\u001d2\u0006\u0010S\u001a\u00020JH\u0002R\u000e\u0010\u0007\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0015X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0015X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\fX\u0082.\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u001b\u001a\n\u0012\u0004\u0012\u00020\u001d\u0018\u00010\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001e\u001a\u00020\u0015X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001f\u001a\u00020\fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010 \u001a\u00020!X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\"\u001a\u00020\fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010#\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010$\u001a\u00020\fX\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006e"}, d2 = {"Lcom/example/castapp/ui/dialog/RemoteSenderControlDialog;", "Landroidx/fragment/app/DialogFragment;", "remoteSenderConnection", "Lcom/example/castapp/model/RemoteSenderConnection;", "remoteClient", "Lcom/example/castapp/remote/RemoteSenderWebSocketClient;", "(Lcom/example/castapp/model/RemoteSenderConnection;Lcom/example/castapp/remote/RemoteSenderWebSocketClient;)V", "addConnectionButton", "Landroid/widget/ImageButton;", "bitrateSeekBar", "Landroid/widget/SeekBar;", "bitrateValueText", "Landroid/widget/TextView;", "closeButton", "connectionCountText", "connectionListAdapter", "Lcom/example/castapp/ui/adapter/RemoteSenderAdapter;", "connectionsRecyclerView", "Landroidx/recyclerview/widget/RecyclerView;", "dialogTitle", "mediaAudioVolumeLayout", "Landroid/widget/LinearLayout;", "mediaAudioVolumeSeekBar", "mediaAudioVolumeText", "micAudioVolumeLayout", "micAudioVolumeSeekBar", "micAudioVolumeText", "onDismissListener", "Lkotlin/Function0;", "", "remoteControlLayout", "remoteControlStatusText", "remoteControlSwitch", "Landroidx/appcompat/widget/SwitchCompat;", "resolutionInfoText", "resolutionSeekBar", "resolutionValueText", "handleConnectionAdded", "message", "Lcom/example/castapp/websocket/ControlMessage;", "handleConnectionListSync", "handleConnectionRemoved", "handleConnectionUpdated", "handleRemoteCastToggle", "connection", "Lcom/example/castapp/model/Connection;", "enabled", "", "handleRemoteMediaAudioToggle", "handleRemoteMicAudioToggle", "handleSettingsSync", "initViews", "view", "Landroid/view/View;", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onCreateView", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "onDismiss", "dialog", "Landroid/content/DialogInterface;", "onViewCreated", "parseConnectionData", "connectionData", "", "requestSettingsSync", "sendAddConnectionRequest", "ipAddress", "", "port", "", "sendBitrateChange", "bitrateMbps", "sendDeleteConnectionRequest", "connectionId", "sendEditConnectionRequest", "newIpAddress", "newPort", "sendResolutionChange", "scalePercent", "sendVolumeChange", "volumeType", "volume", "setOnDismissListener", "listener", "setupClickListeners", "setupControls", "setupRecyclerView", "setupVolumeControl", "seekBar", "textView", "showAddConnectionDialog", "showDeleteConnectionConfirmDialog", "showEditConnectionDialog", "updateBitrateDisplay", "updateConnectionCount", "updateResolutionDisplay", "app_debug"})
public final class RemoteSenderControlDialog extends androidx.fragment.app.DialogFragment {
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.model.RemoteSenderConnection remoteSenderConnection = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.remote.RemoteSenderWebSocketClient remoteClient = null;
    private android.widget.TextView dialogTitle;
    private android.widget.ImageButton closeButton;
    private android.widget.SeekBar bitrateSeekBar;
    private android.widget.TextView bitrateValueText;
    private android.widget.SeekBar resolutionSeekBar;
    private android.widget.TextView resolutionValueText;
    private android.widget.TextView resolutionInfoText;
    private android.widget.LinearLayout mediaAudioVolumeLayout;
    private android.widget.SeekBar mediaAudioVolumeSeekBar;
    private android.widget.TextView mediaAudioVolumeText;
    private android.widget.LinearLayout micAudioVolumeLayout;
    private android.widget.SeekBar micAudioVolumeSeekBar;
    private android.widget.TextView micAudioVolumeText;
    private android.widget.LinearLayout remoteControlLayout;
    private androidx.appcompat.widget.SwitchCompat remoteControlSwitch;
    private android.widget.TextView remoteControlStatusText;
    private androidx.recyclerview.widget.RecyclerView connectionsRecyclerView;
    private android.widget.TextView connectionCountText;
    private android.widget.ImageButton addConnectionButton;
    private com.example.castapp.ui.adapter.RemoteSenderAdapter connectionListAdapter;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function0<kotlin.Unit> onDismissListener;
    
    public RemoteSenderControlDialog(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.RemoteSenderConnection remoteSenderConnection, @org.jetbrains.annotations.NotNull()
    com.example.castapp.remote.RemoteSenderWebSocketClient remoteClient) {
        super();
    }
    
    /**
     * 设置对话框关闭监听器
     */
    public final void setOnDismissListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> listener) {
    }
    
    @java.lang.Override()
    public void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override()
    public void onDismiss(@org.jetbrains.annotations.NotNull()
    android.content.DialogInterface dialog) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void initViews(android.view.View view) {
    }
    
    private final void setupRecyclerView() {
    }
    
    private final void setupControls() {
    }
    
    private final void setupVolumeControl(android.widget.SeekBar seekBar, android.widget.TextView textView, java.lang.String volumeType) {
    }
    
    private final void setupClickListeners() {
    }
    
    private final void updateBitrateDisplay(int bitrateMbps) {
    }
    
    private final void updateResolutionDisplay(int scalePercent) {
    }
    
    private final void sendBitrateChange(int bitrateMbps) {
    }
    
    private final void sendResolutionChange(int scalePercent) {
    }
    
    private final void sendVolumeChange(java.lang.String volumeType, int volume) {
    }
    
    private final void requestSettingsSync() {
    }
    
    /**
     * 处理远程设置同步消息
     */
    public final void handleSettingsSync(@org.jetbrains.annotations.NotNull()
    com.example.castapp.websocket.ControlMessage message) {
    }
    
    /**
     * 处理连接列表同步消息
     */
    private final void handleConnectionListSync(com.example.castapp.websocket.ControlMessage message) {
    }
    
    /**
     * 处理远程投屏切换
     */
    private final void handleRemoteCastToggle(com.example.castapp.model.Connection connection, boolean enabled) {
    }
    
    /**
     * 处理远程媒体音频切换
     */
    private final void handleRemoteMediaAudioToggle(com.example.castapp.model.Connection connection, boolean enabled) {
    }
    
    /**
     * 处理远程麦克风音频切换
     */
    private final void handleRemoteMicAudioToggle(com.example.castapp.model.Connection connection, boolean enabled) {
    }
    
    /**
     * 处理连接添加事件
     */
    public final void handleConnectionAdded(@org.jetbrains.annotations.NotNull()
    com.example.castapp.websocket.ControlMessage message) {
    }
    
    /**
     * 处理连接更新事件
     */
    public final void handleConnectionUpdated(@org.jetbrains.annotations.NotNull()
    com.example.castapp.websocket.ControlMessage message) {
    }
    
    /**
     * 处理连接删除事件
     */
    public final void handleConnectionRemoved(@org.jetbrains.annotations.NotNull()
    com.example.castapp.websocket.ControlMessage message) {
    }
    
    /**
     * 解析连接数据
     */
    private final com.example.castapp.model.Connection parseConnectionData(java.util.Map<?, ?> connectionData) {
        return null;
    }
    
    /**
     * 更新连接数量显示
     */
    private final void updateConnectionCount() {
    }
    
    /**
     * 显示添加连接对话框
     */
    private final void showAddConnectionDialog() {
    }
    
    /**
     * 显示编辑连接对话框
     */
    private final void showEditConnectionDialog(com.example.castapp.model.Connection connection) {
    }
    
    /**
     * 显示删除连接确认对话框
     */
    private final void showDeleteConnectionConfirmDialog(com.example.castapp.model.Connection connection) {
    }
    
    /**
     * 发送添加连接请求
     */
    private final void sendAddConnectionRequest(java.lang.String ipAddress, int port) {
    }
    
    /**
     * 发送编辑连接请求
     */
    private final void sendEditConnectionRequest(java.lang.String connectionId, java.lang.String newIpAddress, int newPort) {
    }
    
    /**
     * 发送删除连接请求
     */
    private final void sendDeleteConnectionRequest(java.lang.String connectionId) {
    }
}