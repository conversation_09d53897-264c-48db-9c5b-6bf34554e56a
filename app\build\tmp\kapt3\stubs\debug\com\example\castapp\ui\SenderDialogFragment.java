package com.example.castapp.ui;

/**
 * 发送端设置对话框
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00dc\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\f\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000e\n\u0002\u0010 \n\u0002\b\t\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u001d\u0018\u0000 \u0090\u00012\u00020\u0001:\u0002\u0090\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u00108\u001a\u0002092\u0006\u0010:\u001a\u00020!H\u0002J\u0010\u0010;\u001a\u0002092\u0006\u0010<\u001a\u00020!H\u0002J\u0010\u0010=\u001a\u0002092\u0006\u0010<\u001a\u00020!H\u0002J\u0010\u0010>\u001a\u0002092\u0006\u0010?\u001a\u00020\u0010H\u0002J \u0010@\u001a\u0002092\u0006\u0010?\u001a\u00020\u00102\u0006\u0010A\u001a\u00020\u00142\u0006\u0010B\u001a\u00020\u0018H\u0002J\u0018\u0010C\u001a\u0002092\u0006\u0010?\u001a\u00020\u00102\u0006\u0010A\u001a\u00020\u0014H\u0002J\u0010\u0010D\u001a\u0002092\u0006\u0010E\u001a\u00020FH\u0002J\u0010\u0010G\u001a\u0002092\u0006\u0010H\u001a\u00020IH\u0002J\u0010\u0010J\u001a\u0002092\u0006\u0010K\u001a\u00020LH\u0002J\u0018\u0010M\u001a\u0002092\u0006\u0010N\u001a\u00020\u00182\u0006\u0010O\u001a\u00020PH\u0002J\b\u0010Q\u001a\u000209H\u0002J\u0012\u0010R\u001a\u0002092\b\u0010S\u001a\u0004\u0018\u00010TH\u0016J&\u0010U\u001a\u0004\u0018\u00010L2\u0006\u0010V\u001a\u00020W2\b\u0010X\u001a\u0004\u0018\u00010Y2\b\u0010S\u001a\u0004\u0018\u00010TH\u0016J\b\u0010Z\u001a\u000209H\u0016J\u001a\u0010[\u001a\u0002092\u0006\u0010K\u001a\u00020L2\b\u0010S\u001a\u0004\u0018\u00010TH\u0016J\u0010\u0010\\\u001a\u0002092\u0006\u0010]\u001a\u00020\u0018H\u0002J\u0018\u0010^\u001a\u0002092\u0006\u0010?\u001a\u00020\u00102\u0006\u0010_\u001a\u00020\u0018H\u0002J\b\u0010`\u001a\u000209H\u0002J\b\u0010a\u001a\u000209H\u0002J\b\u0010b\u001a\u000209H\u0002J\b\u0010c\u001a\u000209H\u0002J\b\u0010d\u001a\u000209H\u0002J\b\u0010e\u001a\u000209H\u0002J\u0016\u0010f\u001a\u0002092\f\u0010g\u001a\b\u0012\u0004\u0012\u00020\u00100hH\u0002J\b\u0010i\u001a\u000209H\u0002J\b\u0010j\u001a\u000209H\u0002J\b\u0010k\u001a\u000209H\u0002J\b\u0010l\u001a\u000209H\u0002J<\u0010m\u001a\u0002092\u0006\u0010n\u001a\u00020\u00062\u0006\u0010o\u001a\u00020\b2\u0006\u0010p\u001a\u00020\u00182\u0006\u0010q\u001a\u00020r2\u0012\u0010s\u001a\u000e\u0012\u0004\u0012\u00020!\u0012\u0004\u0012\u0002090tH\u0002J<\u0010u\u001a\u0002092\u0006\u0010n\u001a\u00020\u00062\u0006\u0010o\u001a\u00020\b2\u0006\u0010p\u001a\u00020\u00182\u0006\u0010q\u001a\u00020r2\u0012\u0010s\u001a\u000e\u0012\u0004\u0012\u00020!\u0012\u0004\u0012\u0002090tH\u0002J\b\u0010v\u001a\u000209H\u0002J\u0010\u0010w\u001a\u0002092\u0006\u0010?\u001a\u00020\u0010H\u0002J\u0010\u0010x\u001a\u0002092\u0006\u0010?\u001a\u00020\u0010H\u0002J\u0010\u0010y\u001a\u0002092\u0006\u0010z\u001a\u00020\u0018H\u0002J\b\u0010{\u001a\u000209H\u0002J\b\u0010|\u001a\u000209H\u0002J\u0010\u0010}\u001a\u0002092\u0006\u0010:\u001a\u00020!H\u0002J\u0010\u0010~\u001a\u0002092\u0006\u0010:\u001a\u00020!H\u0002J#\u0010\u007f\u001a\u0002092\u0007\u0010\u0080\u0001\u001a\u00020\u00102\u0007\u0010\u0081\u0001\u001a\u00020\u00182\u0007\u0010\u0082\u0001\u001a\u00020!H\u0002J\u001a\u0010\u0083\u0001\u001a\u0002092\u0006\u0010_\u001a\u00020\u00182\u0007\u0010\u0084\u0001\u001a\u00020\u0014H\u0002J\u0018\u0010\u0085\u0001\u001a\u0002092\r\u0010\u0086\u0001\u001a\b\u0012\u0004\u0012\u00020\u00100hH\u0002J\t\u0010\u0087\u0001\u001a\u000209H\u0002J\u0011\u0010\u0088\u0001\u001a\u0002092\u0006\u0010<\u001a\u00020!H\u0003J\u0011\u0010\u0089\u0001\u001a\u0002092\u0006\u0010<\u001a\u00020!H\u0002J\u001b\u0010\u008a\u0001\u001a\u0002092\u0007\u0010\u008b\u0001\u001a\u00020\u00182\u0007\u0010\u008c\u0001\u001a\u00020!H\u0002J\u001b\u0010\u008d\u0001\u001a\u00020\u00142\u0007\u0010\u008e\u0001\u001a\u00020\u00182\u0007\u0010\u008f\u0001\u001a\u00020\u0018H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00100\u000fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0017\u001a\u0004\u0018\u00010\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u001aX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\u001aX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001e\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001f\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010 \u001a\u00020!X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\"\u001a\u00020#X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010$\u001a\u00020\u001aX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010%\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010&\u001a\u00020\'X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010(\u001a\u00020)X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010*\u001a\u00020\u0016X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010+\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010,\u001a\u00020-X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010.\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010/\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u00100\u001a\u000201X\u0082.\u00a2\u0006\u0002\n\u0000R\u001b\u00102\u001a\u0002038BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b6\u00107\u001a\u0004\b4\u00105\u00a8\u0006\u0091\u0001"}, d2 = {"Lcom/example/castapp/ui/SenderDialogFragment;", "Landroidx/fragment/app/DialogFragment;", "()V", "addReceiverButton", "Landroid/widget/Button;", "bitrateSeekBar", "Landroid/widget/SeekBar;", "bitrateValueText", "Landroid/widget/TextView;", "closeButton", "Landroid/widget/ImageButton;", "connectionAdapter", "Lcom/example/castapp/ui/adapter/ConnectionAdapter;", "connectionCountText", "connections", "Ljava/util/concurrent/CopyOnWriteArrayList;", "Lcom/example/castapp/model/Connection;", "connectionsRecyclerView", "Landroidx/recyclerview/widget/RecyclerView;", "isResolutionAdjusting", "", "lastResolutionAdjustTime", "", "localIpAddress", "", "mediaAudioVolumeLayout", "Landroid/widget/LinearLayout;", "mediaAudioVolumeSeekBar", "mediaAudioVolumeText", "micAudioVolumeLayout", "micAudioVolumeSeekBar", "micAudioVolumeText", "pendingResolutionScale", "", "permissionHelper", "Lcom/example/castapp/manager/PermissionManager$PermissionHelper;", "remoteControlLayout", "remoteControlStatusText", "remoteControlSwitch", "Landroid/widget/Switch;", "remoteSenderServer", "Lcom/example/castapp/remote/RemoteSenderServer;", "resolutionAdjustMinInterval", "resolutionInfoText", "resolutionManager", "Lcom/example/castapp/manager/ResolutionManager;", "resolutionSeekBar", "resolutionValueText", "stateManager", "Lcom/example/castapp/manager/StateManager;", "viewModel", "Lcom/example/castapp/viewmodel/SenderViewModel;", "getViewModel", "()Lcom/example/castapp/viewmodel/SenderViewModel;", "viewModel$delegate", "Lkotlin/Lazy;", "applyBitrateChange", "", "bitrateMbps", "applyResolutionChange", "scalePercent", "applyResolutionChangeInternal", "forceRefreshConnectionState", "connection", "handleAudioToggle", "isEnabled", "audioType", "handleCastToggle", "handleRemoteControlUIUpdate", "update", "Lcom/example/castapp/viewmodel/SenderViewModel$RemoteControlUIUpdate;", "handleResolutionAdjustmentState", "state", "Lcom/example/castapp/viewmodel/SenderViewModel$ResolutionAdjustmentState;", "initViews", "view", "Landroid/view/View;", "notifyRemoteControlClients", "key", "value", "", "observeViewModel", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onCreateView", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "onDestroy", "onViewCreated", "registerPreciseStateUpdateListener", "connectionId", "scheduleConnectionFailureCheck", "functionType", "setupAudioVolumeControlListeners", "setupAudioVolumeControls", "setupBitrateControl", "setupBitrateControlListener", "setupClickListeners", "setupPreciseStateUpdateListeners", "setupPreciseStateUpdateListenersForNewConnections", "newConnections", "", "setupRecyclerView", "setupRemoteControlSettings", "setupResolutionControl", "setupResolutionControlListener", "setupVolumeControl", "seekBar", "textView", "prefKey", "sharedPrefs", "Landroid/content/SharedPreferences;", "applyChange", "Lkotlin/Function1;", "setupVolumeControlListener", "showAddReceiverDialog", "showEditConnectionDialog", "showRemoveConnectionDialog", "showToast", "message", "startRemoteControlServer", "stopRemoteControlServer", "updateBitrateDisplay", "updateBitrateFromRemote", "updateConnection", "originalConnection", "newIpAddress", "newPort", "updateConnectionToggleFromRemote", "enabled", "updateConnectionsList", "newConnectionList", "updateRemoteControlStatusText", "updateResolutionDisplay", "updateResolutionFromRemote", "updateVolumeFromRemote", "volumeType", "volume", "validateInput", "ipAddress", "portText", "Companion", "app_debug"})
public final class SenderDialogFragment extends androidx.fragment.app.DialogFragment {
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy viewModel$delegate = null;
    private androidx.recyclerview.widget.RecyclerView connectionsRecyclerView;
    private android.widget.TextView connectionCountText;
    private android.widget.Button addReceiverButton;
    private android.widget.ImageButton closeButton;
    private android.widget.SeekBar bitrateSeekBar;
    private android.widget.TextView bitrateValueText;
    private android.widget.SeekBar resolutionSeekBar;
    private android.widget.TextView resolutionValueText;
    private android.widget.TextView resolutionInfoText;
    private android.widget.LinearLayout mediaAudioVolumeLayout;
    private android.widget.SeekBar mediaAudioVolumeSeekBar;
    private android.widget.TextView mediaAudioVolumeText;
    private android.widget.LinearLayout micAudioVolumeLayout;
    private android.widget.SeekBar micAudioVolumeSeekBar;
    private android.widget.TextView micAudioVolumeText;
    private android.widget.LinearLayout remoteControlLayout;
    private android.widget.Switch remoteControlSwitch;
    private android.widget.TextView remoteControlStatusText;
    private com.example.castapp.ui.adapter.ConnectionAdapter connectionAdapter;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.CopyOnWriteArrayList<com.example.castapp.model.Connection> connections = null;
    private com.example.castapp.manager.PermissionManager.PermissionHelper permissionHelper;
    private com.example.castapp.manager.ResolutionManager resolutionManager;
    private com.example.castapp.manager.StateManager stateManager;
    private boolean isResolutionAdjusting = false;
    private long lastResolutionAdjustTime = 0L;
    private final long resolutionAdjustMinInterval = 1000L;
    private int pendingResolutionScale = -1;
    private com.example.castapp.remote.RemoteSenderServer remoteSenderServer;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String localIpAddress;
    private static final int DEFAULT_AUDIO_VOLUME = 80;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.ui.SenderDialogFragment.Companion Companion = null;
    
    public SenderDialogFragment() {
        super();
    }
    
    private final com.example.castapp.viewmodel.SenderViewModel getViewModel() {
        return null;
    }
    
    @java.lang.Override()
    public void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void initViews(android.view.View view) {
    }
    
    private final void observeViewModel() {
    }
    
    private final void updateConnectionsList(java.util.List<com.example.castapp.model.Connection> newConnectionList) {
    }
    
    /**
     * 🚀 重构：移除全量刷新方法，改用精准更新机制
     * 原syncCastingStates()方法导致全量刷新，现在通过精准状态监听器实现局部更新
     */
    private final void setupRecyclerView() {
    }
    
    private final void setupBitrateControl() {
    }
    
    private final void updateBitrateDisplay(int bitrateMbps) {
    }
    
    private final void applyBitrateChange(int bitrateMbps) {
    }
    
    /**
     * 只设置码率控制监听器，不重新设置值
     * 用于远程同步后恢复监听器
     */
    private final void setupBitrateControlListener() {
    }
    
    private final void setupResolutionControl() {
    }
    
    @android.annotation.SuppressLint(value = {"SetTextI18n"})
    private final void updateResolutionDisplay(int scalePercent) {
    }
    
    /**
     * 应用分辨率变化（带防抖和重试机制）
     */
    private final void applyResolutionChange(int scalePercent) {
    }
    
    /**
     * 内部分辨率调整实现
     */
    private final void applyResolutionChangeInternal(int scalePercent) {
    }
    
    /**
     * 处理分辨率调整状态变化
     */
    private final void handleResolutionAdjustmentState(com.example.castapp.viewmodel.SenderViewModel.ResolutionAdjustmentState state) {
    }
    
    /**
     * 只设置分辨率控制监听器，不重新设置值
     * 用于远程同步后恢复监听器
     */
    private final void setupResolutionControlListener() {
    }
    
    private final void setupAudioVolumeControls() {
    }
    
    private final void setupVolumeControl(android.widget.SeekBar seekBar, android.widget.TextView textView, java.lang.String prefKey, android.content.SharedPreferences sharedPrefs, kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> applyChange) {
    }
    
    /**
     * 只设置音量控制监听器，不重新设置值
     * 用于远程同步后恢复监听器
     */
    private final void setupAudioVolumeControlListeners() {
    }
    
    private final void setupVolumeControlListener(android.widget.SeekBar seekBar, android.widget.TextView textView, java.lang.String prefKey, android.content.SharedPreferences sharedPrefs, kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> applyChange) {
    }
    
    private final void setupRemoteControlSettings() {
    }
    
    private final void startRemoteControlServer() {
    }
    
    private final void stopRemoteControlServer() {
    }
    
    private final void updateRemoteControlStatusText() {
    }
    
    private final void setupClickListeners() {
    }
    
    /**
     * 显示添加接收端对话框
     */
    private final void showAddReceiverDialog() {
    }
    
    /**
     * 显示编辑连接对话框
     */
    private final void showEditConnectionDialog(com.example.castapp.model.Connection connection) {
    }
    
    /**
     * 显示删除连接确认对话框
     */
    private final void showRemoveConnectionDialog(com.example.castapp.model.Connection connection) {
    }
    
    /**
     * 验证输入
     */
    private final boolean validateInput(java.lang.String ipAddress, java.lang.String portText) {
        return false;
    }
    
    /**
     * 更新连接信息
     */
    private final void updateConnection(com.example.castapp.model.Connection originalConnection, java.lang.String newIpAddress, int newPort) {
    }
    
    private final void showToast(java.lang.String message) {
    }
    
    private final void handleCastToggle(com.example.castapp.model.Connection connection, boolean isEnabled) {
    }
    
    private final void handleAudioToggle(com.example.castapp.model.Connection connection, boolean isEnabled, java.lang.String audioType) {
    }
    
    private final void scheduleConnectionFailureCheck(com.example.castapp.model.Connection connection, java.lang.String functionType) {
    }
    
    private final void forceRefreshConnectionState(com.example.castapp.model.Connection connection) {
    }
    
    /**
     * 从远程控制更新码率（双向同步）
     */
    private final void updateBitrateFromRemote(int bitrateMbps) {
    }
    
    /**
     * 从远程控制更新分辨率（双向同步）
     */
    private final void updateResolutionFromRemote(int scalePercent) {
    }
    
    /**
     * 从远程控制更新音量（双向同步）
     */
    private final void updateVolumeFromRemote(java.lang.String volumeType, int volume) {
    }
    
    /**
     * 从远程控制更新连接切换（双向同步）
     */
    private final void updateConnectionToggleFromRemote(java.lang.String functionType, boolean enabled) {
    }
    
    /**
     * 处理远程控制UI更新
     */
    private final void handleRemoteControlUIUpdate(com.example.castapp.viewmodel.SenderViewModel.RemoteControlUIUpdate update) {
    }
    
    /**
     * 通知远程控制端UI更新
     */
    private final void notifyRemoteControlClients(java.lang.String key, java.lang.Object value) {
    }
    
    /**
     * 🚀 新增：设置精准状态更新监听器
     */
    private final void setupPreciseStateUpdateListeners() {
    }
    
    /**
     * 🚀 新增：为新连接批量注册精准状态监听器
     */
    private final void setupPreciseStateUpdateListenersForNewConnections(java.util.List<com.example.castapp.model.Connection> newConnections) {
    }
    
    /**
     * 🚀 新增：注册单个连接的精准状态更新监听器
     */
    private final void registerPreciseStateUpdateListener(java.lang.String connectionId) {
    }
    
    @java.lang.Override()
    public void onDestroy() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/example/castapp/ui/SenderDialogFragment$Companion;", "", "()V", "DEFAULT_AUDIO_VOLUME", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}