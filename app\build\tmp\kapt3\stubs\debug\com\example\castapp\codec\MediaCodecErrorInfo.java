package com.example.castapp.codec;

/**
 * 🚀 MediaCodec错误信息数据类
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0012\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B-\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\u0006\u0010\t\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\nJ\t\u0010\u0012\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\bH\u00c6\u0003J\t\u0010\u0016\u001a\u00020\u0003H\u00c6\u0003J;\u0010\u0017\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u0018\u001a\u00020\b2\b\u0010\u0019\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001a\u001a\u00020\u001bH\u00d6\u0001J\t\u0010\u001c\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\fR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\u0010R\u0011\u0010\t\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\f\u00a8\u0006\u001d"}, d2 = {"Lcom/example/castapp/codec/MediaCodecErrorInfo;", "", "errorCode", "", "errorType", "Lcom/example/castapp/codec/MediaCodecErrorType;", "description", "isRecoverable", "", "recommendedAction", "(Ljava/lang/String;Lcom/example/castapp/codec/MediaCodecErrorType;Ljava/lang/String;ZLjava/lang/String;)V", "getDescription", "()Ljava/lang/String;", "getErrorCode", "getErrorType", "()Lcom/example/castapp/codec/MediaCodecErrorType;", "()Z", "getRecommendedAction", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class MediaCodecErrorInfo {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String errorCode = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.codec.MediaCodecErrorType errorType = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String description = null;
    private final boolean isRecoverable = false;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String recommendedAction = null;
    
    public MediaCodecErrorInfo(@org.jetbrains.annotations.NotNull()
    java.lang.String errorCode, @org.jetbrains.annotations.NotNull()
    com.example.castapp.codec.MediaCodecErrorType errorType, @org.jetbrains.annotations.NotNull()
    java.lang.String description, boolean isRecoverable, @org.jetbrains.annotations.NotNull()
    java.lang.String recommendedAction) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getErrorCode() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.codec.MediaCodecErrorType getErrorType() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDescription() {
        return null;
    }
    
    public final boolean isRecoverable() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getRecommendedAction() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.codec.MediaCodecErrorType component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    public final boolean component4() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.codec.MediaCodecErrorInfo copy(@org.jetbrains.annotations.NotNull()
    java.lang.String errorCode, @org.jetbrains.annotations.NotNull()
    com.example.castapp.codec.MediaCodecErrorType errorType, @org.jetbrains.annotations.NotNull()
    java.lang.String description, boolean isRecoverable, @org.jetbrains.annotations.NotNull()
    java.lang.String recommendedAction) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}