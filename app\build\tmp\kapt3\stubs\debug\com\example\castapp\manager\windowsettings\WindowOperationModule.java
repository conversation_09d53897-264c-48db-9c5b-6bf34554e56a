package com.example.castapp.manager.windowsettings;

/**
 * 窗口操作模块
 * 负责处理投屏窗口的各种操作和功能切换
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\t\n\u0002\u0010\u000b\n\u0002\b\u000b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\t\u001a\u00020\u00072\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\rJ\u0016\u0010\u000e\u001a\u00020\u00072\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\u000f\u001a\u00020\u0010J\u0014\u0010\u0011\u001a\u00020\u00072\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006J\u0014\u0010\u0013\u001a\u00020\u00072\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006J\u0016\u0010\u0014\u001a\u00020\u00072\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\u0015\u001a\u00020\u0010J\u0016\u0010\u0016\u001a\u00020\u00072\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\u0017\u001a\u00020\u0010J\u0016\u0010\u0018\u001a\u00020\u00072\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\u0019\u001a\u00020\u001aJ\u0016\u0010\u001b\u001a\u00020\u00072\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\u0019\u001a\u00020\u001aJ\u0016\u0010\u001c\u001a\u00020\u00072\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\u001d\u001a\u00020\u001aJ\u0016\u0010\u001e\u001a\u00020\u00072\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\u0019\u001a\u00020\u001aJ\u0016\u0010\u001f\u001a\u00020\u00072\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\u0019\u001a\u00020\u001aJ\u0016\u0010 \u001a\u00020\u00072\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\u0019\u001a\u00020\u001aJ\u0016\u0010!\u001a\u00020\u00072\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\u0019\u001a\u00020\u001aJ\u0016\u0010\"\u001a\u00020\u00072\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\u0019\u001a\u00020\u001aJ\u0016\u0010#\u001a\u00020\u00072\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010$\u001a\u00020\u001aR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010\b\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006%"}, d2 = {"Lcom/example/castapp/manager/windowsettings/WindowOperationModule;", "", "dataModule", "Lcom/example/castapp/manager/windowsettings/WindowDataModule;", "(Lcom/example/castapp/manager/windowsettings/WindowDataModule;)V", "dialogRefreshCallback", "Lkotlin/Function0;", "", "precisionControlUpdateCallback", "setBorderColor", "connectionId", "", "color", "", "setBorderWidth", "width", "", "setDialogRefreshCallback", "callback", "setPrecisionControlUpdateCallback", "setWindowAlpha", "alpha", "setWindowCornerRadius", "cornerRadius", "toggleBorderMode", "isEnabled", "", "toggleControlMode", "toggleCropMode", "enable", "toggleDragMode", "toggleEditMode", "toggleMirrorMode", "toggleRotationMode", "toggleScaleMode", "toggleWindowVisibility", "isVisible", "app_debug"})
public final class WindowOperationModule {
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.windowsettings.WindowDataModule dataModule = null;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function0<kotlin.Unit> precisionControlUpdateCallback;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function0<kotlin.Unit> dialogRefreshCallback;
    
    public WindowOperationModule(@org.jetbrains.annotations.NotNull()
    com.example.castapp.manager.windowsettings.WindowDataModule dataModule) {
        super();
    }
    
    /**
     * 设置精准控制面板更新回调
     */
    public final void setPrecisionControlUpdateCallback(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> callback) {
    }
    
    /**
     * 设置对话框刷新回调
     */
    public final void setDialogRefreshCallback(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> callback) {
    }
    
    /**
     * 切换指定连接的裁剪模式
     */
    public final void toggleCropMode(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean enable) {
    }
    
    /**
     * 切换拖动功能
     */
    public final void toggleDragMode(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean isEnabled) {
    }
    
    /**
     * 切换缩放功能
     */
    public final void toggleScaleMode(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean isEnabled) {
    }
    
    /**
     * 切换旋转功能
     */
    public final void toggleRotationMode(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean isEnabled) {
    }
    
    /**
     * 📝 切换编辑功能（仅文字窗口）
     */
    public final void toggleEditMode(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean isEnabled) {
    }
    
    /**
     * 切换窗口显示/隐藏
     */
    public final void toggleWindowVisibility(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean isVisible) {
    }
    
    /**
     * 切换镜像模式
     */
    public final void toggleMirrorMode(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean isEnabled) {
    }
    
    /**
     * 设置窗口圆角半径
     */
    public final void setWindowCornerRadius(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, float cornerRadius) {
    }
    
    /**
     * 设置窗口透明度
     */
    public final void setWindowAlpha(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, float alpha) {
    }
    
    /**
     * 切换精准调控功能
     */
    public final void toggleControlMode(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean isEnabled) {
    }
    
    /**
     * 切换边框显示模式
     */
    public final void toggleBorderMode(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean isEnabled) {
    }
    
    /**
     * 设置边框颜色
     */
    public final void setBorderColor(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, int color) {
    }
    
    /**
     * 设置边框宽度
     */
    public final void setBorderWidth(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, float width) {
    }
}