package com.example.castapp.manager;

/**
 * 悬浮窗管理器
 * 负责管理悬浮窗权限和悬浮窗服务的启动
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\u0018\u0000 \u000e2\u00020\u0001:\u0001\u000eB\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0006\u001a\u00020\u0007J\u000e\u0010\b\u001a\u00020\u00072\u0006\u0010\t\u001a\u00020\u0005J\u000e\u0010\n\u001a\u00020\u00072\u0006\u0010\u000b\u001a\u00020\fJ\b\u0010\r\u001a\u00020\u0007H\u0002R\u0016\u0010\u0003\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000f"}, d2 = {"Lcom/example/castapp/manager/FloatingWindowManager;", "", "()V", "currentContextRef", "Ljava/lang/ref/WeakReference;", "Landroid/content/Context;", "cleanup", "", "initialize", "context", "requestOverlayPermissionAndStartStopwatch", "permissionHelper", "Lcom/example/castapp/manager/PermissionManager$ActivityPermissionHelper;", "startFloatingStopwatch", "Companion", "app_debug"})
public final class FloatingWindowManager {
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.example.castapp.manager.FloatingWindowManager INSTANCE;
    @org.jetbrains.annotations.Nullable()
    private java.lang.ref.WeakReference<android.content.Context> currentContextRef;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.manager.FloatingWindowManager.Companion Companion = null;
    
    private FloatingWindowManager() {
        super();
    }
    
    /**
     * 初始化管理器
     */
    public final void initialize(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    /**
     * 请求悬浮窗权限并启动秒表
     */
    public final void requestOverlayPermissionAndStartStopwatch(@org.jetbrains.annotations.NotNull()
    com.example.castapp.manager.PermissionManager.ActivityPermissionHelper permissionHelper) {
    }
    
    /**
     * 启动悬浮秒表
     */
    private final void startFloatingStopwatch() {
    }
    
    /**
     * 清理资源
     */
    public final void cleanup() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0005\u001a\u00020\u0004R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0006"}, d2 = {"Lcom/example/castapp/manager/FloatingWindowManager$Companion;", "", "()V", "INSTANCE", "Lcom/example/castapp/manager/FloatingWindowManager;", "getInstance", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.manager.FloatingWindowManager getInstance() {
            return null;
        }
    }
}