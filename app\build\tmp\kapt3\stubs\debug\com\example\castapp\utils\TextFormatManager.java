package com.example.castapp.utils;

/**
 * 文本格式管理器
 * 负责文本内容和格式的持久化存储
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000V\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0010\u0007\n\u0002\b\t\u0018\u0000 %2\u00020\u0001:\u0003%&\'B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\nJ\u0010\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000fH\u0002J\u0010\u0010\u0010\u001a\u0004\u0018\u00010\u00112\u0006\u0010\u0012\u001a\u00020\nJ\u0010\u0010\u0013\u001a\u0004\u0018\u00010\b2\u0006\u0010\u0012\u001a\u00020\nJ\u0010\u0010\u0014\u001a\u0004\u0018\u00010\u00152\u0006\u0010\u0012\u001a\u00020\nJ\\\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0012\u001a\u00020\n2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u00192\b\b\u0002\u0010\u001b\u001a\u00020\r2\n\b\u0002\u0010\u001c\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u001d\u001a\u0004\u0018\u00010\n2\b\b\u0002\u0010\u001e\u001a\u00020\u001f2\b\b\u0002\u0010 \u001a\u00020\rJ\u0016\u0010!\u001a\u00020\u00172\u0006\u0010\u0012\u001a\u00020\n2\u0006\u0010\"\u001a\u00020\bJ0\u0010#\u001a\u00020\u00172\u0006\u0010\u0012\u001a\u00020\n2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u00192\b\b\u0002\u0010\u001b\u001a\u00020\rJ\u000e\u0010$\u001a\u00020\n2\u0006\u0010\"\u001a\u00020\bR\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006("}, d2 = {"Lcom/example/castapp/utils/TextFormatManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "sharedPreferences", "Landroid/content/SharedPreferences;", "deserializeSpannableString", "Landroid/text/SpannableString;", "textContent", "", "richTextData", "extractOriginalSpValue", "", "span", "Landroid/text/style/AbsoluteSizeSpan;", "getExtendedTextFormat", "Lcom/example/castapp/utils/TextFormatManager$ExtendedTextFormatInfo;", "textId", "getRichTextFormat", "getTextFormat", "Lcom/example/castapp/utils/TextFormatManager$TextFormatInfo;", "saveExtendedTextFormat", "", "isBold", "", "isItalic", "fontSize", "fontName", "fontFamily", "lineSpacing", "", "textAlignment", "saveRichTextFormat", "spannableString", "saveTextFormat", "serializeSpannableString", "Companion", "ExtendedTextFormatInfo", "TextFormatInfo", "app_debug"})
public final class TextFormatManager {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String PREF_NAME = "text_format_preferences";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_TEXT_CONTENT = "text_content_";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_IS_BOLD = "is_bold_";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_IS_ITALIC = "is_italic_";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_FONT_SIZE = "font_size_";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_RICH_TEXT_DATA = "rich_text_data_";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_FONT_NAME = "font_name_";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_FONT_FAMILY = "font_family_";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_LINE_SPACING = "line_spacing_";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_TEXT_ALIGNMENT = "text_alignment_";
    @org.jetbrains.annotations.NotNull()
    private final android.content.SharedPreferences sharedPreferences = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.utils.TextFormatManager.Companion Companion = null;
    
    public TextFormatManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    /**
     * 保存文本格式
     */
    public final void saveTextFormat(@org.jetbrains.annotations.NotNull()
    java.lang.String textId, @org.jetbrains.annotations.NotNull()
    java.lang.String textContent, boolean isBold, boolean isItalic, int fontSize) {
    }
    
    /**
     * 获取文本格式
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.utils.TextFormatManager.TextFormatInfo getTextFormat(@org.jetbrains.annotations.NotNull()
    java.lang.String textId) {
        return null;
    }
    
    /**
     * 📝 保存富文本格式（包含完整的SpannableString信息）
     */
    public final void saveRichTextFormat(@org.jetbrains.annotations.NotNull()
    java.lang.String textId, @org.jetbrains.annotations.NotNull()
    android.text.SpannableString spannableString) {
    }
    
    /**
     * 📝 获取富文本格式（返回完整的SpannableString）
     */
    @org.jetbrains.annotations.Nullable()
    public final android.text.SpannableString getRichTextFormat(@org.jetbrains.annotations.NotNull()
    java.lang.String textId) {
        return null;
    }
    
    /**
     * 📝 将SpannableString序列化为JSON字符串
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String serializeSpannableString(@org.jetbrains.annotations.NotNull()
    android.text.SpannableString spannableString) {
        return null;
    }
    
    /**
     * 🎯 提取AbsoluteSizeSpan的原始sp值
     */
    private final int extractOriginalSpValue(android.text.style.AbsoluteSizeSpan span) {
        return 0;
    }
    
    /**
     * 📝 从JSON字符串反序列化为SpannableString
     */
    @org.jetbrains.annotations.NotNull()
    public final android.text.SpannableString deserializeSpannableString(@org.jetbrains.annotations.NotNull()
    java.lang.String textContent, @org.jetbrains.annotations.NotNull()
    java.lang.String richTextData) {
        return null;
    }
    
    /**
     * 📝 保存扩展文本格式（包含完整的字体格式信息）
     */
    public final void saveExtendedTextFormat(@org.jetbrains.annotations.NotNull()
    java.lang.String textId, @org.jetbrains.annotations.NotNull()
    java.lang.String textContent, boolean isBold, boolean isItalic, int fontSize, @org.jetbrains.annotations.Nullable()
    java.lang.String fontName, @org.jetbrains.annotations.Nullable()
    java.lang.String fontFamily, float lineSpacing, int textAlignment) {
    }
    
    /**
     * 📝 获取扩展文本格式（包含完整的字体格式信息）
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.utils.TextFormatManager.ExtendedTextFormatInfo getExtendedTextFormat(@org.jetbrains.annotations.NotNull()
    java.lang.String textId) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\n\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000e"}, d2 = {"Lcom/example/castapp/utils/TextFormatManager$Companion;", "", "()V", "KEY_FONT_FAMILY", "", "KEY_FONT_NAME", "KEY_FONT_SIZE", "KEY_IS_BOLD", "KEY_IS_ITALIC", "KEY_LINE_SPACING", "KEY_RICH_TEXT_DATA", "KEY_TEXT_ALIGNMENT", "KEY_TEXT_CONTENT", "PREF_NAME", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
    
    /**
     * 📝 扩展文本格式信息数据类
     * 包含完整的字体格式信息
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u0007\n\u0002\b\u001d\b\u0086\b\u0018\u00002\u00020\u0001B_\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0007\u001a\u00020\b\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0002\u0010\u000b\u001a\u00020\f\u0012\b\b\u0002\u0010\r\u001a\u00020\b\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0002\u0010\u000fJ\t\u0010\u001b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\bH\u00c6\u0003J\u000b\u0010\u001f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010 \u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010!\u001a\u00020\fH\u00c6\u0003J\t\u0010\"\u001a\u00020\bH\u00c6\u0003J\u000b\u0010#\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003Ji\u0010$\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\b2\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010%\u001a\u00020\u00052\b\u0010&\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\'\u001a\u00020\bH\u00d6\u0001J\t\u0010(\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\n\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0013\u0010\t\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0011R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0004\u0010\u0015R\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0015R\u0011\u0010\u000b\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0013\u0010\u000e\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0011R\u0011\u0010\r\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0014R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0011\u00a8\u0006)"}, d2 = {"Lcom/example/castapp/utils/TextFormatManager$ExtendedTextFormatInfo;", "", "textContent", "", "isBold", "", "isItalic", "fontSize", "", "fontName", "fontFamily", "lineSpacing", "", "textAlignment", "richTextData", "(Ljava/lang/String;ZZILjava/lang/String;Ljava/lang/String;FILjava/lang/String;)V", "getFontFamily", "()Ljava/lang/String;", "getFontName", "getFontSize", "()I", "()Z", "getLineSpacing", "()F", "getRichTextData", "getTextAlignment", "getTextContent", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "toString", "app_debug"})
    public static final class ExtendedTextFormatInfo {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String textContent = null;
        private final boolean isBold = false;
        private final boolean isItalic = false;
        private final int fontSize = 0;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String fontName = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String fontFamily = null;
        private final float lineSpacing = 0.0F;
        private final int textAlignment = 0;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String richTextData = null;
        
        public ExtendedTextFormatInfo(@org.jetbrains.annotations.NotNull()
        java.lang.String textContent, boolean isBold, boolean isItalic, int fontSize, @org.jetbrains.annotations.Nullable()
        java.lang.String fontName, @org.jetbrains.annotations.Nullable()
        java.lang.String fontFamily, float lineSpacing, int textAlignment, @org.jetbrains.annotations.Nullable()
        java.lang.String richTextData) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getTextContent() {
            return null;
        }
        
        public final boolean isBold() {
            return false;
        }
        
        public final boolean isItalic() {
            return false;
        }
        
        public final int getFontSize() {
            return 0;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getFontName() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getFontFamily() {
            return null;
        }
        
        public final float getLineSpacing() {
            return 0.0F;
        }
        
        public final int getTextAlignment() {
            return 0;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getRichTextData() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        public final boolean component2() {
            return false;
        }
        
        public final boolean component3() {
            return false;
        }
        
        public final int component4() {
            return 0;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component5() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component6() {
            return null;
        }
        
        public final float component7() {
            return 0.0F;
        }
        
        public final int component8() {
            return 0;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component9() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.utils.TextFormatManager.ExtendedTextFormatInfo copy(@org.jetbrains.annotations.NotNull()
        java.lang.String textContent, boolean isBold, boolean isItalic, int fontSize, @org.jetbrains.annotations.Nullable()
        java.lang.String fontName, @org.jetbrains.annotations.Nullable()
        java.lang.String fontFamily, float lineSpacing, int textAlignment, @org.jetbrains.annotations.Nullable()
        java.lang.String richTextData) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    /**
     * 文本格式信息数据类
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0013\b\u0086\b\u0018\u00002\u00020\u0001B3\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0007\u001a\u00020\b\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0002\u0010\nJ\t\u0010\u0011\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\bH\u00c6\u0003J\u000b\u0010\u0015\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J=\u0010\u0016\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\u0017\u001a\u00020\u00052\b\u0010\u0018\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0019\u001a\u00020\bH\u00d6\u0001J\t\u0010\u001a\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0004\u0010\rR\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\rR\u0013\u0010\t\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000f\u00a8\u0006\u001b"}, d2 = {"Lcom/example/castapp/utils/TextFormatManager$TextFormatInfo;", "", "textContent", "", "isBold", "", "isItalic", "fontSize", "", "richTextData", "(Ljava/lang/String;ZZILjava/lang/String;)V", "getFontSize", "()I", "()Z", "getRichTextData", "()Ljava/lang/String;", "getTextContent", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "other", "hashCode", "toString", "app_debug"})
    public static final class TextFormatInfo {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String textContent = null;
        private final boolean isBold = false;
        private final boolean isItalic = false;
        private final int fontSize = 0;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String richTextData = null;
        
        public TextFormatInfo(@org.jetbrains.annotations.NotNull()
        java.lang.String textContent, boolean isBold, boolean isItalic, int fontSize, @org.jetbrains.annotations.Nullable()
        java.lang.String richTextData) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getTextContent() {
            return null;
        }
        
        public final boolean isBold() {
            return false;
        }
        
        public final boolean isItalic() {
            return false;
        }
        
        public final int getFontSize() {
            return 0;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getRichTextData() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        public final boolean component2() {
            return false;
        }
        
        public final boolean component3() {
            return false;
        }
        
        public final int component4() {
            return 0;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component5() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.utils.TextFormatManager.TextFormatInfo copy(@org.jetbrains.annotations.NotNull()
        java.lang.String textContent, boolean isBold, boolean isItalic, int fontSize, @org.jetbrains.annotations.Nullable()
        java.lang.String richTextData) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}