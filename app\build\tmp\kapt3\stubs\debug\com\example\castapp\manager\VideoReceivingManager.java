package com.example.castapp.manager;

/**
 * 视频接收管理器
 * 负责管理完整的视频流接收和显示业务
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000V\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\u0012\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0010\n\u0002\u0018\u0002\n\u0002\b\u0006\u0018\u00002\u00020\u0001:\u00010B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u000e\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u0012J\u000e\u0010\u0013\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u0012J\u000e\u0010\u0014\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u0012JE\u0010\u0015\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00122\b\u0010\u0016\u001a\u0004\u0018\u00010\u00172\b\u0010\u0018\u001a\u0004\u0018\u00010\u00172\b\u0010\u0019\u001a\u0004\u0018\u00010\u001a2\b\u0010\u001b\u001a\u0004\u0018\u00010\u001a2\b\u0010\u001c\u001a\u0004\u0018\u00010\u001a\u00a2\u0006\u0002\u0010\u001dJ\u001e\u0010\u001e\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u001f\u001a\u00020\u001a2\u0006\u0010 \u001a\u00020\u001aJ\u001e\u0010!\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0019\u001a\u00020\u001a2\u0006\u0010\u001b\u001a\u00020\u001aJ\u0016\u0010\"\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010#\u001a\u00020\bJ\u000e\u0010$\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u0012J\u0006\u0010%\u001a\u00020\u0010J\u0006\u0010\u0007\u001a\u00020\bJ2\u0010&\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0019\u001a\u00020\u001a2\u0006\u0010\u001b\u001a\u00020\u001a2\u0006\u0010\'\u001a\u00020\b2\b\u0010(\u001a\u0004\u0018\u00010\u0012H\u0002J\u0018\u0010)\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00122\b\u0010*\u001a\u0004\u0018\u00010+J\u0018\u0010,\u001a\u00020\b2\u0006\u0010-\u001a\u00020\u001a2\b\u0010.\u001a\u0004\u0018\u00010\u000eJ\u0006\u0010/\u001a\u00020\u0010R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\t\u001a\u0004\u0018\u00010\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000b\u001a\u0004\u0018\u00010\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\r\u001a\u0004\u0018\u00010\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u00061"}, d2 = {"Lcom/example/castapp/manager/VideoReceivingManager;", "", "stateManager", "Lcom/example/castapp/manager/StateManager;", "callback", "Lcom/example/castapp/manager/VideoReceivingManager$VideoReceivingCallback;", "(Lcom/example/castapp/manager/StateManager;Lcom/example/castapp/manager/VideoReceivingManager$VideoReceivingCallback;)V", "isRunning", "", "multiConnectionManager", "Lcom/example/castapp/rtp/MultiConnectionManager;", "rtpReceiver", "Lcom/example/castapp/rtp/RtpReceiver;", "webSocketServer", "Lcom/example/castapp/websocket/WebSocketServer;", "clearDisconnectionFlag", "", "connectionId", "", "gracefulStopDecoder", "handleConnectionDisconnect", "handleH264ConfigWithOrientation", "spsData", "", "ppsData", "width", "", "height", "orientation", "(Ljava/lang/String;[B[BLjava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;)V", "handleResolutionChange", "newWidth", "newHeight", "handleScreenResolution", "handleVideoFunctionControl", "isEnabled", "handleVideoStreamStop", "immediateStopAllDecoders", "sendResolutionAdjustmentComplete", "success", "error", "setSurfaceForConnection", "surface", "Landroid/view/Surface;", "startVideoReceiving", "port", "webSocketServerInstance", "stopVideoReceiving", "VideoReceivingCallback", "app_debug"})
public final class VideoReceivingManager {
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.StateManager stateManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.VideoReceivingManager.VideoReceivingCallback callback = null;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.rtp.RtpReceiver rtpReceiver;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.rtp.MultiConnectionManager multiConnectionManager;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.websocket.WebSocketServer webSocketServer;
    private boolean isRunning = false;
    
    public VideoReceivingManager(@org.jetbrains.annotations.NotNull()
    com.example.castapp.manager.StateManager stateManager, @org.jetbrains.annotations.NotNull()
    com.example.castapp.manager.VideoReceivingManager.VideoReceivingCallback callback) {
        super();
    }
    
    /**
     * 启动视频接收服务
     */
    public final boolean startVideoReceiving(int port, @org.jetbrains.annotations.Nullable()
    com.example.castapp.websocket.WebSocketServer webSocketServerInstance) {
        return false;
    }
    
    /**
     * 停止视频接收服务
     */
    public final void stopVideoReceiving() {
    }
    
    /**
     * 为指定连接设置Surface
     */
    public final void setSurfaceForConnection(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.Nullable()
    android.view.Surface surface) {
    }
    
    /**
     * 清理指定连接的断开标记
     */
    public final void clearDisconnectionFlag(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 立即停止所有MediaCodec解码器
     */
    public final void immediateStopAllDecoders() {
    }
    
    /**
     * 🚀 优雅停止特定连接的MediaCodec解码器（用于窗口删除）
     */
    public final void gracefulStopDecoder(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 🎯 横竖屏适配：处理包含方向信息的H.264配置数据
     */
    public final void handleH264ConfigWithOrientation(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.Nullable()
    byte[] spsData, @org.jetbrains.annotations.Nullable()
    byte[] ppsData, @org.jetbrains.annotations.Nullable()
    java.lang.Integer width, @org.jetbrains.annotations.Nullable()
    java.lang.Integer height, @org.jetbrains.annotations.Nullable()
    java.lang.Integer orientation) {
    }
    
    /**
     * 处理视频流停止
     */
    public final void handleVideoStreamStop(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 处理屏幕分辨率信息
     */
    public final void handleScreenResolution(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, int width, int height) {
    }
    
    /**
     * 处理分辨率变化（实时调整）
     */
    public final void handleResolutionChange(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, int newWidth, int newHeight) {
    }
    
    /**
     * 发送分辨率调整完成通知给发送端
     */
    private final void sendResolutionAdjustmentComplete(java.lang.String connectionId, int width, int height, boolean success, java.lang.String error) {
    }
    
    /**
     * 处理视频功能控制
     */
    public final void handleVideoFunctionControl(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean isEnabled) {
    }
    
    /**
     * 处理连接断开
     */
    public final void handleConnectionDisconnect(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 获取运行状态
     */
    public final boolean isRunning() {
        return false;
    }
    
    /**
     * 视频接收回调接口
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\n\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J2\u0010\u0006\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\b2\u0006\u0010\n\u001a\u00020\u000b2\b\u0010\f\u001a\u0004\u0018\u00010\u0005H&J \u0010\r\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\bH&J\u0010\u0010\u000e\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\u0018\u0010\u000f\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\f\u001a\u00020\u0005H&J(\u0010\u0010\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0011\u001a\u00020\b2\u0006\u0010\u0012\u001a\u00020\b2\u0006\u0010\u0013\u001a\u00020\bH&J\u0010\u0010\u0014\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&\u00a8\u0006\u0015"}, d2 = {"Lcom/example/castapp/manager/VideoReceivingManager$VideoReceivingCallback;", "", "onNewVideoConnection", "", "connectionId", "", "onResolutionAdjustmentComplete", "width", "", "height", "success", "", "error", "onScreenResolution", "onVideoConnectionDisconnected", "onVideoError", "onVideoOrientationChanged", "orientation", "videoWidth", "videoHeight", "onVideoWindowRemoved", "app_debug"})
    public static abstract interface VideoReceivingCallback {
        
        public abstract void onNewVideoConnection(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId);
        
        public abstract void onVideoConnectionDisconnected(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId);
        
        public abstract void onVideoWindowRemoved(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId);
        
        public abstract void onScreenResolution(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, int width, int height);
        
        public abstract void onVideoError(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
        java.lang.String error);
        
        public abstract void onResolutionAdjustmentComplete(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, int width, int height, boolean success, @org.jetbrains.annotations.Nullable()
        java.lang.String error);
        
        public abstract void onVideoOrientationChanged(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, int orientation, int videoWidth, int videoHeight);
    }
}