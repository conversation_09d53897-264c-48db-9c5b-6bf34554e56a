package com.example.castapp.codec;

/**
 * H.264视频解码器
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00ac\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010!\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0012\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010\u000b\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b!\u0018\u0000 \u0083\u00012\u00020\u0001:\b\u0082\u0001\u0083\u0001\u0084\u0001\u0085\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\b\u0010C\u001a\u00020\u0019H\u0002J\u0006\u0010D\u001a\u00020\u0019J\b\u0010E\u001a\u00020\u0019H\u0002J\u000e\u0010F\u001a\u00020\u00192\u0006\u0010G\u001a\u00020%J\b\u0010H\u001a\u00020\u0019H\u0002J\u000e\u0010I\u001a\b\u0012\u0004\u0012\u00020K0JH\u0002J\b\u0010L\u001a\u00020\tH\u0002J\u0016\u0010M\u001a\u0012\u0012\u0006\u0012\u0004\u0018\u000109\u0012\u0006\u0012\u0004\u0018\u0001090NJ\u0010\u0010O\u001a\u00020P2\u0006\u0010Q\u001a\u000206H\u0002J\u0010\u0010R\u001a\u00020\u00072\u0006\u0010S\u001a\u000206H\u0002J\b\u0010T\u001a\u00020\u0019H\u0002J\u0010\u0010U\u001a\u00020\u00192\u0006\u0010S\u001a\u000206H\u0002J\b\u0010V\u001a\u00020\u0019H\u0002J\b\u0010W\u001a\u00020\u0019H\u0002J\b\u0010X\u001a\u00020\u0019H\u0002J\u0010\u0010Y\u001a\u00020Z2\u0006\u0010S\u001a\u000206H\u0002J\u0010\u0010[\u001a\u00020Z2\u0006\u0010\\\u001a\u00020KH\u0002J\u0012\u0010]\u001a\u00020Z2\b\u0010^\u001a\u0004\u0018\u000102H\u0002J\b\u0010_\u001a\u00020ZH\u0002J\u0006\u0010`\u001a\u00020ZJ\u0010\u0010a\u001a\u00020Z2\u0006\u0010^\u001a\u000202H\u0002J\b\u0010b\u001a\u00020\u0019H\u0002J\u0018\u0010c\u001a\u00020\u00192\u0006\u0010d\u001a\u00020e2\u0006\u0010G\u001a\u00020%H\u0002J\u001c\u0010f\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00070N2\u0006\u0010g\u001a\u000206H\u0002J$\u0010h\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00070N2\u0006\u0010g\u001a\u0002062\u0006\u0010i\u001a\u00020\u0007H\u0002J\u001c\u0010j\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00070N2\u0006\u0010g\u001a\u000206H\u0002J\u0018\u0010k\u001a\u00020\u00192\u0006\u0010G\u001a\u00020%2\u0006\u0010Q\u001a\u000206H\u0002J\u0018\u0010l\u001a\u00020\u00192\u0006\u0010^\u001a\u0002022\u0006\u0010m\u001a\u00020\u0007H\u0002J\u0012\u0010n\u001a\u00020\u00192\b\u0010o\u001a\u0004\u0018\u000102H\u0002J\u0010\u0010p\u001a\u00020\u00192\u0006\u0010^\u001a\u000202H\u0002J\b\u0010q\u001a\u00020\u0019H\u0002J\u0018\u0010r\u001a\u0004\u0018\u00010K2\f\u0010s\u001a\b\u0012\u0004\u0012\u00020K0JH\u0002J\u0016\u0010t\u001a\u00020\u00192\u0006\u0010u\u001a\u00020\u00072\u0006\u0010v\u001a\u00020\u0007J\n\u0010w\u001a\u0004\u0018\u000106H\u0002J\u0006\u0010x\u001a\u00020ZJ\u0006\u0010y\u001a\u00020\u0019J\b\u0010z\u001a\u00020\u0019H\u0002J\b\u0010{\u001a\u00020\u0019H\u0002J\u0010\u0010|\u001a\u00020\u00192\u0006\u0010}\u001a\u00020PH\u0002J\u0017\u0010~\u001a\u00020\u00192\u0006\u0010\u007f\u001a\u00020\u00072\u0007\u0010\u0080\u0001\u001a\u00020\u0007J\u0019\u0010\u0081\u0001\u001a\u00020\u00192\u0006\u0010u\u001a\u00020\u00072\u0006\u0010v\u001a\u00020\u0007H\u0002R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010\n\u001a\n \f*\u0004\u0018\u00010\u000b0\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0013X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0001X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0017\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00190\u00180\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u0001X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001e\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001f\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010 \u001a\u00020\u0001X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010!\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\"\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010#\u001a\b\u0012\u0004\u0012\u00020%0$X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010&\u001a\u00020\'X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010(\u001a\u00020\'X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010)\u001a\u00020\'X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010*\u001a\u00020\'X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010+\u001a\u00020\'X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010,\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010-\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010.\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010/\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00100\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u00101\u001a\u0004\u0018\u000102X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00103\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u00104\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u00105\u001a\u0004\u0018\u000106X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00107\u001a\u00020\u0001X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u00108\u001a\u0004\u0018\u000109X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010:\u001a\u0004\u0018\u000109X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010;\u001a\u00020<X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010=\u001a\u0004\u0018\u000106X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010>\u001a\u00020\tX\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010?\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0012\u0010@\u001a\u0004\u0018\u00010\u0007X\u0082\u000e\u00a2\u0006\u0004\n\u0002\u0010AR\u0012\u0010B\u001a\u0004\u0018\u00010\u0007X\u0082\u000e\u00a2\u0006\u0004\n\u0002\u0010A\u00a8\u0006\u0086\u0001"}, d2 = {"Lcom/example/castapp/codec/VideoDecoder;", "", "outputSurface", "Landroid/view/Surface;", "(Landroid/view/Surface;)V", "availableInputBuffers", "", "", "averageDecodeTime", "", "backgroundReleaseExecutor", "Ljava/util/concurrent/ExecutorService;", "kotlin.jvm.PlatformType", "cachedCurrentTime", "callbackHandler", "Landroid/os/Handler;", "callbackThread", "Landroid/os/HandlerThread;", "codecInstanceId", "Ljava/util/concurrent/atomic/AtomicLong;", "codecStateLock", "configurationLatch", "Ljava/util/concurrent/CountDownLatch;", "configurationListeners", "Lkotlin/Function0;", "", "configurationLock", "currentQueueSize", "decodeCount", "droppedFrames", "droppedIFrames", "iFrameCount", "inputBufferLock", "inputBufferReuseHits", "inputBufferReuseMisses", "inputQueue", "Ljava/util/concurrent/ConcurrentLinkedQueue;", "Lcom/example/castapp/rtp/PayloadView;", "isConfigured", "Ljava/util/concurrent/atomic/AtomicBoolean;", "isConfiguring", "isReleasingOldResources", "isRunning", "isUpdatingResolution", "lastDecodeTime", "lastPerformanceLogTime", "lastQueueAdjustment", "lastTimestampUpdate", "maxQueueSize", "mediaCodec", "Landroid/media/MediaCodec;", "minQueueSize", "pFrameCount", "ppsData", "", "resolutionUpdateLock", "selectedDecoderName", "", "selectedDecoderType", "shutdownHook", "Ljava/lang/Thread;", "spsData", "timestampCacheIntervalNs", "totalDecodeDelay", "webSocketHeight", "Ljava/lang/Integer;", "webSocketWidth", "adjustQueueSizeBasedOnPerformance", "cleanup", "configureDecoder", "decode", "payloadView", "emergencyCleanup", "getAvailableHardwareDecoders", "", "Landroid/media/MediaCodecInfo;", "getCachedCurrentTime", "getDecoderInfo", "Lkotlin/Pair;", "getFrameType", "Lcom/example/castapp/codec/VideoDecoder$FrameType;", "h264Data", "getNalType", "data", "handleCodecInternalError", "handleConfigurationData", "handleProcessKilledError", "handleResourceExhaustedError", "handleUnknownError", "isConfigurationData", "", "isHardwareDecoder", "codecInfo", "isMediaCodecValid", "codec", "isProcessBeingKilled", "isUsingHardwareAcceleration", "isValidCodecInstance", "notifyConfigurationComplete", "optimizedVideoInputBufferWriteFromPayloadView", "inputBuffer", "Ljava/nio/ByteBuffer;", "parseH264SPS", "sps", "parseSpsDimensionsHeuristic", "startIndex", "parseSpsForDimensions", "processFrameDataZeroCopy", "processInputDataDirect", "index", "releaseOldResourcesInBackground", "oldCodec", "safeReleaseDecoder", "scheduleDelayedConfiguration", "selectBestHardwareDecoder", "hardwareDecoders", "setWebSocketResolution", "width", "height", "smartFrameDrop", "start", "stop", "stopAllAsyncOperations", "tryProcessPendingData", "updateFrameStatistics", "frameType", "updateResolution", "newWidth", "newHeight", "validateResolutionForDecoding", "ByteArrayDataView", "Companion", "FrameType", "MediaCodecCallback", "app_debug"})
public final class VideoDecoder {
    @org.jetbrains.annotations.NotNull()
    private final android.view.Surface outputSurface = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String MIME_TYPE = "video/avc";
    @org.jetbrains.annotations.Nullable()
    private android.media.MediaCodec mediaCodec;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.atomic.AtomicBoolean isRunning = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentLinkedQueue<com.example.castapp.rtp.PayloadView> inputQueue = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.atomic.AtomicBoolean isConfigured = null;
    @org.jetbrains.annotations.Nullable()
    private byte[] spsData;
    @org.jetbrains.annotations.Nullable()
    private byte[] ppsData;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.Object resolutionUpdateLock = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.Object codecStateLock = null;
    @org.jetbrains.annotations.NotNull()
    private java.util.concurrent.atomic.AtomicLong codecInstanceId;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.atomic.AtomicBoolean isUpdatingResolution = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.atomic.AtomicBoolean isReleasingOldResources = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.atomic.AtomicBoolean isConfiguring = null;
    @org.jetbrains.annotations.NotNull()
    private final android.os.HandlerThread callbackThread = null;
    @org.jetbrains.annotations.NotNull()
    private final android.os.Handler callbackHandler = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.Thread shutdownHook = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.Integer> availableInputBuffers = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.Object inputBufferLock = null;
    private final java.util.concurrent.ExecutorService backgroundReleaseExecutor = null;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String selectedDecoderName;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String selectedDecoderType;
    private long lastDecodeTime = 0L;
    private long decodeCount = 0L;
    private long totalDecodeDelay = 0L;
    private long lastTimestampUpdate = 0L;
    private long cachedCurrentTime = 0L;
    private final long timestampCacheIntervalNs = 16000000L;
    private long droppedFrames = 0L;
    private long lastPerformanceLogTime = 0L;
    private int currentQueueSize = 12;
    private int maxQueueSize = 30;
    private int minQueueSize = 6;
    private long lastQueueAdjustment = 0L;
    private long averageDecodeTime = 0L;
    private long iFrameCount = 0L;
    private long pFrameCount = 0L;
    private long droppedIFrames = 0L;
    private long inputBufferReuseHits = 0L;
    private long inputBufferReuseMisses = 0L;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.CountDownLatch configurationLatch = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<kotlin.jvm.functions.Function0<kotlin.Unit>> configurationListeners = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.Object configurationLock = null;
    @org.jetbrains.annotations.Nullable()
    private java.lang.Integer webSocketWidth;
    @org.jetbrains.annotations.Nullable()
    private java.lang.Integer webSocketHeight;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.codec.VideoDecoder.Companion Companion = null;
    
    public VideoDecoder(@org.jetbrains.annotations.NotNull()
    android.view.Surface outputSurface) {
        super();
    }
    
    /**
     * 🚀 关键方法：尝试处理待处理的数据
     * 当新数据加入队列时，检查是否有可用的输入缓冲区并立即处理
     */
    private final void tryProcessPendingData() {
    }
    
    /**
     * 直接处理输入数据（用于缓存的缓冲区）
     */
    private final void processInputDataDirect(android.media.MediaCodec codec, int index) {
    }
    
    /**
     * 🚀 验证MediaCodec实例是否仍然有效
     * 防止在分辨率更新过程中操作已失效的实例
     */
    private final boolean isValidCodecInstance(android.media.MediaCodec codec) {
        return false;
    }
    
    /**
     * 🚀 检查MediaCodec是否处于有效状态
     * 用于强制结束进程时的额外保护
     */
    private final boolean isMediaCodecValid(android.media.MediaCodec codec) {
        return false;
    }
    
    /**
     * 🚀 检测进程是否正在被强制结束
     * 通过多种指标判断进程状态
     */
    private final boolean isProcessBeingKilled() {
        return false;
    }
    
    /**
     * 🚀 安全停止所有异步操作
     * 确保在分辨率更新前所有回调都已停止
     */
    private final void stopAllAsyncOperations() {
    }
    
    /**
     * 通知配置完成（事件驱动）
     */
    private final void notifyConfigurationComplete() {
    }
    
    /**
     * 检测并获取可用的硬件H.264解码器列表
     */
    private final java.util.List<android.media.MediaCodecInfo> getAvailableHardwareDecoders() {
        return null;
    }
    
    /**
     * 判断解码器是否为硬件解码器
     */
    private final boolean isHardwareDecoder(android.media.MediaCodecInfo codecInfo) {
        return false;
    }
    
    /**
     * 选择最佳的硬件解码器
     */
    private final android.media.MediaCodecInfo selectBestHardwareDecoder(java.util.List<android.media.MediaCodecInfo> hardwareDecoders) {
        return null;
    }
    
    /**
     * 🚀 CPU优化：获取缓存的当前时间，减少系统调用
     */
    private final long getCachedCurrentTime() {
        return 0L;
    }
    
    /**
     * 启动解码器
     */
    public final boolean start() {
        return false;
    }
    
    /**
     * 🚀 停止解码器 - 增强状态同步和Native层保护
     */
    public final void stop() {
    }
    
    /**
     * 完全清理解码器资源，包括线程池 - 增强Native层保护
     * 在解码器不再使用时调用，确保没有内存泄露和SIGSEGV
     */
    public final void cleanup() {
    }
    
    /**
     * 🚀 紧急清理方法 - 用于进程强制结束时的最后保护
     */
    private final void emergencyCleanup() {
    }
    
    /**
     * 安全释放MediaCodec解码器资源
     */
    private final void safeReleaseDecoder(android.media.MediaCodec codec) {
    }
    
    /**
     * 🚀 零拷贝解码：统一入口，移除向后兼容
     * 只支持PayloadView输入，性能最优
     */
    public final void decode(@org.jetbrains.annotations.NotNull()
    com.example.castapp.rtp.PayloadView payloadView) {
    }
    
    /**
     * 🚀 零拷贝帧数据处理：统一处理逻辑，避免代码重复
     */
    private final void processFrameDataZeroCopy(com.example.castapp.rtp.PayloadView payloadView, byte[] h264Data) {
    }
    
    /**
     * 🚀 动态更新视频分辨率并重新配置解码器
     * 采用"安全停止 + 创建新资源 + 后台释放旧资源"策略，确保状态同步
     */
    public final void updateResolution(int newWidth, int newHeight) {
    }
    
    /**
     * 在后台释放旧资源
     */
    private final void releaseOldResourcesInBackground(android.media.MediaCodec oldCodec) {
    }
    
    /**
     * 验证分辨率是否适合解码
     */
    private final void validateResolutionForDecoding(int width, int height) {
    }
    
    /**
     * 检查是否是配置数据（SPS/PPS）
     */
    private final boolean isConfigurationData(byte[] data) {
        return false;
    }
    
    /**
     * 处理配置数据
     */
    private final void handleConfigurationData(byte[] data) {
    }
    
    /**
     * 🚀 延迟配置解码器
     * 在旧资源释放完成后再配置
     */
    private final void scheduleDelayedConfiguration() {
    }
    
    /**
     * 获取NAL单元类型
     */
    private final int getNalType(byte[] data) {
        return 0;
    }
    
    /**
     * 配置解码器
     */
    private final void configureDecoder() {
    }
    
    /**
     * 从SPS中解析视频尺寸
     * 🚀 简化版：优先使用WebSocket分辨率，无需SPS解析验证
     */
    private final kotlin.Pair<java.lang.Integer, java.lang.Integer> parseSpsForDimensions(byte[] sps) {
        return null;
    }
    
    /**
     * 解析H.264 SPS (Sequence Parameter Set) 获取视频分辨率
     */
    private final kotlin.Pair<java.lang.Integer, java.lang.Integer> parseH264SPS(byte[] sps) {
        return null;
    }
    
    /**
     * 启发式SPS分辨率解析
     * 基于常见的H.264编码器输出模式
     */
    private final kotlin.Pair<java.lang.Integer, java.lang.Integer> parseSpsDimensionsHeuristic(byte[] sps, int startIndex) {
        return null;
    }
    
    /**
     * 🚀 设置WebSocket传递的分辨率信息
     * 作为SPS解析的补充验证机制
     */
    public final void setWebSocketResolution(int width, int height) {
    }
    
    /**
     * 获取当前使用的解码器信息
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlin.Pair<java.lang.String, java.lang.String> getDecoderInfo() {
        return null;
    }
    
    /**
     * 检查解码器是否正在使用硬件加速
     */
    public final boolean isUsingHardwareAcceleration() {
        return false;
    }
    
    /**
     * 获取帧类型
     */
    private final com.example.castapp.codec.VideoDecoder.FrameType getFrameType(byte[] h264Data) {
        return null;
    }
    
    /**
     * 更新帧类型统计
     */
    private final void updateFrameStatistics(com.example.castapp.codec.VideoDecoder.FrameType frameType) {
    }
    
    /**
     * 智能帧丢弃策略
     */
    private final byte[] smartFrameDrop() {
        return null;
    }
    
    /**
     * 根据性能动态调整队列大小
     */
    private final void adjustQueueSizeBasedOnPerformance() {
    }
    
    /**
     * 🚀 终极零拷贝优化：从PayloadView直接写入输入缓冲区
     */
    private final void optimizedVideoInputBufferWriteFromPayloadView(java.nio.ByteBuffer inputBuffer, com.example.castapp.rtp.PayloadView payloadView) {
    }
    
    /**
     * 🚀 处理进程被强制结束的错误
     */
    private final void handleProcessKilledError() {
    }
    
    /**
     * 🚀 处理资源耗尽错误
     */
    private final void handleResourceExhaustedError() {
    }
    
    /**
     * 🚀 处理编解码器内部错误
     */
    private final void handleCodecInternalError() {
    }
    
    /**
     * 🚀 处理未知错误
     */
    private final void handleUnknownError() {
    }
    
    /**
     * 🚀 零拷贝ByteArray包装器 - 避免数据拷贝的DataView实现
     * 直接引用ByteArray，无需额外的缓冲区管理
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0012\n\u0000\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0005\n\u0002\b\u0004\u0018\u00002\u00020\u0001B!\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0007J \u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u00052\u0006\u0010\u0010\u001a\u00020\u0005H\u0016J \u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u00032\u0006\u0010\u0011\u001a\u00020\u00052\u0006\u0010\u0010\u001a\u00020\u0005H\u0016J(\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u00032\u0006\u0010\u0011\u001a\u00020\u00052\u0006\u0010\u000f\u001a\u00020\u00052\u0006\u0010\u0010\u001a\u00020\u0005H\u0016J\u0010\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u0005H\u0016J\u0006\u0010\u0015\u001a\u00020\u000eJ\b\u0010\u0016\u001a\u00020\u0003H\u0016R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0004\u001a\u00020\u0005X\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0014\u0010\u0006\u001a\u00020\u0005X\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\t\u00a8\u0006\u0017"}, d2 = {"Lcom/example/castapp/codec/VideoDecoder$ByteArrayDataView;", "Lcom/example/castapp/network/DataView;", "data", "", "offset", "", "size", "([BII)V", "getOffset", "()I", "getSize", "copyTo", "", "dest", "Ljava/nio/ByteBuffer;", "srcOffset", "length", "destOffset", "getByte", "", "index", "getDirectByteBuffer", "toByteArray", "app_debug"})
    public static final class ByteArrayDataView implements com.example.castapp.network.DataView {
        @org.jetbrains.annotations.NotNull()
        private final byte[] data = null;
        private final int offset = 0;
        private final int size = 0;
        
        public ByteArrayDataView(@org.jetbrains.annotations.NotNull()
        byte[] data, int offset, int size) {
            super();
        }
        
        @java.lang.Override()
        public int getOffset() {
            return 0;
        }
        
        @java.lang.Override()
        public int getSize() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public byte[] toByteArray() {
            return null;
        }
        
        @java.lang.Override()
        public void copyTo(@org.jetbrains.annotations.NotNull()
        byte[] dest, int destOffset, int length) {
        }
        
        @java.lang.Override()
        public byte getByte(int index) {
            return 0;
        }
        
        @java.lang.Override()
        public void copyTo(@org.jetbrains.annotations.NotNull()
        byte[] dest, int destOffset, int srcOffset, int length) {
        }
        
        @java.lang.Override()
        public void copyTo(@org.jetbrains.annotations.NotNull()
        java.nio.ByteBuffer dest, int srcOffset, int length) {
        }
        
        /**
         * 🚀 零拷贝优化：获取直接ByteBuffer视图
         */
        @org.jetbrains.annotations.NotNull()
        public final java.nio.ByteBuffer getDirectByteBuffer() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/example/castapp/codec/VideoDecoder$Companion;", "", "()V", "MIME_TYPE", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0005\b\u0082\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005\u00a8\u0006\u0006"}, d2 = {"Lcom/example/castapp/codec/VideoDecoder$FrameType;", "", "(Ljava/lang/String;I)V", "I_FRAME", "P_FRAME", "UNKNOWN", "app_debug"})
    static enum FrameType {
        /*public static final*/ I_FRAME /* = new I_FRAME() */,
        /*public static final*/ P_FRAME /* = new P_FRAME() */,
        /*public static final*/ UNKNOWN /* = new UNKNOWN() */;
        
        FrameType() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.example.castapp.codec.VideoDecoder.FrameType> getEntries() {
            return null;
        }
    }
    
    /**
     * MediaCodec异步回调处理器
     * 🚀 异步模式：系统驱动的高效解码处理
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\b\u0082\u0004\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0002J\u0018\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u0005\u001a\u00020\u0006H\u0016J\u0018\u0010\u000b\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\f\u001a\u00020\rH\u0016J \u0010\u000e\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\u000f\u001a\u00020\u0010H\u0016J\u0018\u0010\u0011\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u0012\u001a\u00020\u0013H\u0016J\u0018\u0010\u0014\u001a\u00020\u00152\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\f\u001a\u00020\rH\u0002\u00a8\u0006\u0016"}, d2 = {"Lcom/example/castapp/codec/VideoDecoder$MediaCodecCallback;", "Landroid/media/MediaCodec$Callback;", "(Lcom/example/castapp/codec/VideoDecoder;)V", "analyzeMediaCodecError", "Lcom/example/castapp/codec/MediaCodecErrorInfo;", "e", "Landroid/media/MediaCodec$CodecException;", "onError", "", "codec", "Landroid/media/MediaCodec;", "onInputBufferAvailable", "index", "", "onOutputBufferAvailable", "info", "Landroid/media/MediaCodec$BufferInfo;", "onOutputFormatChanged", "format", "Landroid/media/MediaFormat;", "processInputData", "", "app_debug"})
    final class MediaCodecCallback extends android.media.MediaCodec.Callback {
        
        public MediaCodecCallback() {
            super();
        }
        
        @java.lang.Override()
        public void onInputBufferAvailable(@org.jetbrains.annotations.NotNull()
        android.media.MediaCodec codec, int index) {
        }
        
        /**
         * 处理输入数据
         */
        private final boolean processInputData(android.media.MediaCodec codec, int index) {
            return false;
        }
        
        @java.lang.Override()
        public void onOutputBufferAvailable(@org.jetbrains.annotations.NotNull()
        android.media.MediaCodec codec, int index, @org.jetbrains.annotations.NotNull()
        android.media.MediaCodec.BufferInfo info) {
        }
        
        @java.lang.Override()
        public void onError(@org.jetbrains.annotations.NotNull()
        android.media.MediaCodec codec, @org.jetbrains.annotations.NotNull()
        android.media.MediaCodec.CodecException e) {
        }
        
        /**
         * 🚀 分析MediaCodec错误类型和严重程度
         */
        private final com.example.castapp.codec.MediaCodecErrorInfo analyzeMediaCodecError(android.media.MediaCodec.CodecException e) {
            return null;
        }
        
        @java.lang.Override()
        public void onOutputFormatChanged(@org.jetbrains.annotations.NotNull()
        android.media.MediaCodec codec, @org.jetbrains.annotations.NotNull()
        android.media.MediaFormat format) {
        }
    }
}