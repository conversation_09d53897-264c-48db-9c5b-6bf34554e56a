package com.example.castapp.audio;

/**
 * 音频同步管理器
 * 负责管理音频流的时间同步和延迟补偿
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u0002\n\u0002\b\b\n\u0002\u0010\u000b\n\u0002\b\u0003\u0018\u0000 \u00142\u00020\u0001:\u0002\u0014\u0015B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\u0006H\u0002J\u0006\u0010\f\u001a\u00020\nJ\b\u0010\r\u001a\u00020\nH\u0002J\u0018\u0010\u000e\u001a\u00020\n2\u0006\u0010\u000f\u001a\u00020\u00052\b\b\u0002\u0010\u0010\u001a\u00020\bJ\u000e\u0010\u0011\u001a\u00020\n2\u0006\u0010\u000f\u001a\u00020\u0005J\u000e\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u000f\u001a\u00020\u0005R\u001a\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0016"}, d2 = {"Lcom/example/castapp/audio/AudioSyncManager;", "", "()V", "connectionSyncInfo", "Ljava/util/concurrent/ConcurrentHashMap;", "", "Lcom/example/castapp/audio/AudioSyncManager$SyncInfo;", "lastStatsTime", "", "checkAndAdjustLatency", "", "syncInfo", "clearAll", "logStatsIfNeeded", "recordPacketReceived", "connectionId", "receiveTime", "resetConnectionSync", "shouldDropPacket", "", "Companion", "SyncInfo", "app_debug"})
public final class AudioSyncManager {
    private static final long TARGET_LATENCY_MS = 50L;
    private static final long LATENCY_ADJUSTMENT_THRESHOLD_MS = 20L;
    private static final long MAX_ALLOWED_LATENCY_MS = 200L;
    private static final long STATS_INTERVAL_MS = 30000L;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, com.example.castapp.audio.AudioSyncManager.SyncInfo> connectionSyncInfo = null;
    private long lastStatsTime = 0L;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.audio.AudioSyncManager.Companion Companion = null;
    
    public AudioSyncManager() {
        super();
    }
    
    /**
     * 记录音频包接收时间
     */
    public final void recordPacketReceived(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, long receiveTime) {
    }
    
    /**
     * 检查并调整延迟
     */
    private final void checkAndAdjustLatency(com.example.castapp.audio.AudioSyncManager.SyncInfo syncInfo) {
    }
    
    /**
     * 判断是否应该丢弃音频包以减少延迟
     */
    public final boolean shouldDropPacket(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
        return false;
    }
    
    /**
     * 重置连接的同步信息
     */
    public final void resetConnectionSync(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 清理所有同步信息
     */
    public final void clearAll() {
    }
    
    /**
     * 定期输出统计信息
     */
    private final void logStatsIfNeeded() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0004\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\b"}, d2 = {"Lcom/example/castapp/audio/AudioSyncManager$Companion;", "", "()V", "LATENCY_ADJUSTMENT_THRESHOLD_MS", "", "MAX_ALLOWED_LATENCY_MS", "STATS_INTERVAL_MS", "TARGET_LATENCY_MS", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u001e\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001BI\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0007\u001a\u00020\u0005\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u0012\b\b\u0002\u0010\n\u001a\u00020\t\u0012\b\b\u0002\u0010\u000b\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\fJ\t\u0010\u001f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010 \u001a\u00020\u0005H\u00c6\u0003J\t\u0010!\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\"\u001a\u00020\u0005H\u00c6\u0003J\t\u0010#\u001a\u00020\tH\u00c6\u0003J\t\u0010$\u001a\u00020\tH\u00c6\u0003J\t\u0010%\u001a\u00020\u0005H\u00c6\u0003JO\u0010&\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\u00052\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\t2\b\b\u0002\u0010\u000b\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\'\u001a\u00020(2\b\u0010)\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010*\u001a\u00020\tH\u00d6\u0001J\t\u0010+\u001a\u00020\u0003H\u00d6\u0001R\u001a\u0010\u0006\u001a\u00020\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\r\u0010\u000e\"\u0004\b\u000f\u0010\u0010R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u001a\u0010\u000b\u001a\u00020\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0013\u0010\u000e\"\u0004\b\u0014\u0010\u0010R\u001a\u0010\u0004\u001a\u00020\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0015\u0010\u000e\"\u0004\b\u0016\u0010\u0010R\u001a\u0010\u0007\u001a\u00020\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0017\u0010\u000e\"\u0004\b\u0018\u0010\u0010R\u001a\u0010\n\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0019\u0010\u001a\"\u0004\b\u001b\u0010\u001cR\u001a\u0010\b\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001d\u0010\u001a\"\u0004\b\u001e\u0010\u001c\u00a8\u0006,"}, d2 = {"Lcom/example/castapp/audio/AudioSyncManager$SyncInfo;", "", "connectionId", "", "lastPacketTime", "", "averageLatency", "latencyVariance", "packetsReceived", "", "packetsDropped", "lastAdjustmentTime", "(Ljava/lang/String;JJJIIJ)V", "getAverageLatency", "()J", "setAverageLatency", "(J)V", "getConnectionId", "()Ljava/lang/String;", "getLastAdjustmentTime", "setLastAdjustmentTime", "getLastPacketTime", "setLastPacketTime", "getLatencyVariance", "setLatencyVariance", "getPacketsDropped", "()I", "setPacketsDropped", "(I)V", "getPacketsReceived", "setPacketsReceived", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "equals", "", "other", "hashCode", "toString", "app_debug"})
    public static final class SyncInfo {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String connectionId = null;
        private long lastPacketTime;
        private long averageLatency;
        private long latencyVariance;
        private int packetsReceived;
        private int packetsDropped;
        private long lastAdjustmentTime;
        
        public SyncInfo(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, long lastPacketTime, long averageLatency, long latencyVariance, int packetsReceived, int packetsDropped, long lastAdjustmentTime) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getConnectionId() {
            return null;
        }
        
        public final long getLastPacketTime() {
            return 0L;
        }
        
        public final void setLastPacketTime(long p0) {
        }
        
        public final long getAverageLatency() {
            return 0L;
        }
        
        public final void setAverageLatency(long p0) {
        }
        
        public final long getLatencyVariance() {
            return 0L;
        }
        
        public final void setLatencyVariance(long p0) {
        }
        
        public final int getPacketsReceived() {
            return 0;
        }
        
        public final void setPacketsReceived(int p0) {
        }
        
        public final int getPacketsDropped() {
            return 0;
        }
        
        public final void setPacketsDropped(int p0) {
        }
        
        public final long getLastAdjustmentTime() {
            return 0L;
        }
        
        public final void setLastAdjustmentTime(long p0) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        public final long component2() {
            return 0L;
        }
        
        public final long component3() {
            return 0L;
        }
        
        public final long component4() {
            return 0L;
        }
        
        public final int component5() {
            return 0;
        }
        
        public final int component6() {
            return 0;
        }
        
        public final long component7() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.audio.AudioSyncManager.SyncInfo copy(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, long lastPacketTime, long averageLatency, long latencyVariance, int packetsReceived, int packetsDropped, long lastAdjustmentTime) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}