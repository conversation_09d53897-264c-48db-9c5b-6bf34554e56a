<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="3dp"
    android:layout_marginBottom="4dp"
    android:background="@drawable/list_item_background"
    android:elevation="1dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/order_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="1. "
            android:textStyle="bold"
            android:textSize="11sp"
            android:textColor="#FF9800"/>

        <TextView
            android:id="@+id/device_info_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Xiaomi 2109522C"
            android:textStyle="bold"
            android:textSize="10sp"
            android:textColor="#2196F3"
            android:maxLines="1"
            android:ellipsize="end"
            android:maxWidth="120dp" />

        <TextView
            android:id="@+id/connection_id_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="(12345678)"
            android:textSize="10sp"
            android:textColor="#666666"/>

    </LinearLayout>

    <!-- 变换信息 - 位置、缩放、旋转、层级 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">



        <TextView
            android:id="@+id/tv_scale"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="缩放：1.0"
            android:textSize="9sp"
            android:textColor="#666666"
            android:layout_marginStart="2dp"/>

        <TextView
            android:id="@+id/tv_layer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="层级：1"
            android:textSize="9sp"
            android:textColor="#666666"
            android:layout_marginEnd="2dp"/>

        <TextView
            android:id="@+id/tv_position"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1.6"
            android:text="位置：（50,100）"
            android:textSize="9sp"
            android:padding="0dp"
            android:textColor="#666666"/>
    </LinearLayout>

        <!-- 第二行：旋转和层级 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="2dp">

        <TextView
            android:id="@+id/tv_rotation"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="旋转：0°"
            android:textSize="9sp"
            android:textColor="#666666"
            android:layout_marginStart="2dp"/>

        <TextView
            android:id="@+id/corner_radius_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="圆角: 16dp"
            android:textSize="9sp"
            android:textColor="#666666"
            android:layout_marginEnd="2dp"/>

        <TextView
            android:id="@+id/opacity_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1.6"
            android:text="透明度: 100%"
            android:textSize="9sp"
            android:textColor="#666666"/>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="2dp">

        <TextView
            android:id="@+id/visibility_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="显示: 开启"
            android:textSize="9sp"
            android:textColor="#666666"
            android:layout_marginStart="2dp"/>

        <TextView
            android:id="@+id/landscape_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="横屏: 关闭"
            android:textSize="9sp"
            android:textColor="#666666"/>

        <TextView
            android:id="@+id/mirror_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1.6"
            android:text="镜像: 关闭"
            android:textSize="9sp"
            android:textColor="#666666"/>



    </LinearLayout>

    <!-- 边框信息行 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="2dp">

        <TextView
            android:id="@+id/border_status_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="边框: 开启"
            android:textSize="9sp"
            android:textColor="#666666"
            android:layout_marginStart="2dp"/>

        <TextView
            android:id="@+id/border_width_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="宽度: 2dp"
            android:textSize="9sp"
            android:textColor="#666666"
            android:layout_marginEnd="2dp"/>

        <TextView
            android:id="@+id/border_color_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1.6"
            android:text="颜色: FFFF3145"
            android:textSize="9sp"
            android:textColor="#666666"/>

    </LinearLayout>

    <TextView
        android:id="@+id/crop_rect_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="裁剪区域: 无"
        android:textSize="9sp"
        android:textColor="#666666"
        android:layout_marginTop="2dp"
        android:layout_marginStart="2dp"/>

    <!-- 🏷️ 备注信息 -->
    <TextView
        android:id="@+id/note_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="备注: 无"
        android:textSize="9sp"
        android:textColor="#FF9800"
        android:layout_marginTop="2dp"
        android:layout_marginStart="2dp"
        android:maxLines="2"
        android:ellipsize="end"
        android:maxWidth="200dp"/>

    <!-- 🎬 视频播放状态信息 -->
    <TextView
        android:id="@+id/video_play_status_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text=""
        android:textSize="9sp"
        android:textColor="#4CAF50"
        android:layout_marginTop="2dp"
        android:layout_marginStart="2dp"
        android:visibility="gone"/>

</LinearLayout>