<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp"
        android:background="@android:color/white">

        <!-- 标题区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="16dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_cast"
                android:tint="#2196F3"
                android:layout_marginEnd="8dp" />

            <TextView
                android:id="@+id/dialog_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="添加接收端"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="#333333" />

        </LinearLayout>

        <!-- IP地址输入区域 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            app:boxBackgroundMode="outline"
            app:boxCornerRadiusTopStart="8dp"
            app:boxCornerRadiusTopEnd="8dp"
            app:boxCornerRadiusBottomStart="8dp"
            app:boxCornerRadiusBottomEnd="8dp"
            app:boxStrokeColor="#2196F3"
            app:hintTextColor="#666666"
            app:startIconDrawable="@drawable/ic_cast"
            app:startIconTint="#666666">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/ip_address_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="例如: *************"
                android:inputType="text"
                android:textSize="16sp"
                android:textColor="#333333"
                android:maxLines="1"
                android:imeOptions="actionNext" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 端口号输入区域 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="端口号"
            app:boxBackgroundMode="outline"
            app:boxCornerRadiusTopStart="8dp"
            app:boxCornerRadiusTopEnd="8dp"
            app:boxCornerRadiusBottomStart="8dp"
            app:boxCornerRadiusBottomEnd="8dp"
            app:boxStrokeColor="#2196F3"
            app:hintTextColor="#666666"
            app:startIconDrawable="@drawable/ic_send"
            app:startIconTint="#666666"
            app:helperText="范围: 1024-65535"
            app:helperTextTextColor="#888888">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/port_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="8888"
                android:inputType="number"
                android:text="8888"
                android:textSize="16sp"
                android:textColor="#333333"
                android:maxLines="1"
                android:imeOptions="actionDone" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 提示信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginTop="12dp"
            android:padding="12dp"
            android:background="@drawable/rounded_background"
            android:backgroundTint="#E3F2FD">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_notification"
                android:tint="#1976D2"
                android:layout_marginEnd="8dp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="请确保接收端设备已启动并在同一网络中"
                android:textSize="12sp"
                android:textColor="#1976D2"
                android:lineSpacingExtra="2dp" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>