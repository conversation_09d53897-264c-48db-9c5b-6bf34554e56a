package com.example.castapp.network;

/**
 * UDP接收器 - 零拷贝优化版本
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0004\u0018\u0000 \u00192\u00020\u0001:\u0001\u0019B!\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u0005\u00a2\u0006\u0002\u0010\bJ\u0006\u0010\u0015\u001a\u00020\u0016J\b\u0010\u0017\u001a\u00020\u0007H\u0002J\u0006\u0010\u0018\u001a\u00020\u0007R\u000e\u0010\t\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000f\u001a\u0004\u0018\u00010\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0013\u001a\u0004\u0018\u00010\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001a"}, d2 = {"Lcom/example/castapp/network/UdpReceiver;", "", "port", "", "onDataReceived", "Lkotlin/Function1;", "Lcom/example/castapp/network/DataView;", "", "(ILkotlin/jvm/functions/Function1;)V", "isRunning", "Ljava/util/concurrent/atomic/AtomicBoolean;", "lastStatsTime", "", "packetsReceived", "receiveErrors", "receiveThread", "Ljava/lang/Thread;", "smartBufferManager", "Lcom/example/castapp/network/SmartBufferManager;", "socket", "Ljava/net/DatagramSocket;", "start", "", "startReceiveLoop", "stop", "Companion", "app_debug"})
public final class UdpReceiver {
    private final int port = 0;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<com.example.castapp.network.DataView, kotlin.Unit> onDataReceived = null;
    private static final int SOCKET_TIMEOUT = 1;
    private static final int RECEIVE_BUFFER_SIZE = 524288;
    private static final int HIGH_LATENCY_THRESHOLD = 50;
    private static final int STATS_REPORT_INTERVAL = 120000;
    @org.jetbrains.annotations.Nullable()
    private java.net.DatagramSocket socket;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.atomic.AtomicBoolean isRunning = null;
    @org.jetbrains.annotations.Nullable()
    private java.lang.Thread receiveThread;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.network.SmartBufferManager smartBufferManager = null;
    private long packetsReceived = 0L;
    private long receiveErrors = 0L;
    private long lastStatsTime;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.network.UdpReceiver.Companion Companion = null;
    
    public UdpReceiver(int port, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.castapp.network.DataView, kotlin.Unit> onDataReceived) {
        super();
    }
    
    /**
     * 启动接收器
     */
    public final boolean start() {
        return false;
    }
    
    /**
     * 停止接收器
     */
    public final void stop() {
    }
    
    /**
     * 启动接收循环
     */
    private final void startReceiveLoop() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0004\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\b"}, d2 = {"Lcom/example/castapp/network/UdpReceiver$Companion;", "", "()V", "HIGH_LATENCY_THRESHOLD", "", "RECEIVE_BUFFER_SIZE", "SOCKET_TIMEOUT", "STATS_REPORT_INTERVAL", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}