package com.example.castapp.manager;

/**
 * 统一WebSocket连接管理器 - 彻底重构版本
 * 核心原则：每个Connection只维护一个WebSocket连接，所有功能共享
 * 通过消息类型区分不同功能，而不是创建多个连接
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000R\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010#\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0016\u0018\u0000 12\u00020\u0001:\u00011B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0013\u001a\u00020\n2\u0006\u0010\u0014\u001a\u00020\u0005H\u0002J\u0018\u0010\u0015\u001a\u00020\t2\u0006\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0014\u001a\u00020\u0005H\u0002J\u0010\u0010\u0018\u001a\u00020\n2\u0006\u0010\u0014\u001a\u00020\u0005H\u0002J\u0010\u0010\u0019\u001a\u00020\n2\u0006\u0010\u0014\u001a\u00020\u0005H\u0002J\u000e\u0010\u001a\u001a\u00020\n2\u0006\u0010\u0014\u001a\u00020\u0005J\n\u0010\u001b\u001a\u0004\u0018\u00010\u001cH\u0003J\u0010\u0010\u001d\u001a\u0004\u0018\u00010\u00122\u0006\u0010\u0014\u001a\u00020\u0005J\u0010\u0010\u001e\u001a\u00020\n2\u0006\u0010\u0014\u001a\u00020\u0005H\u0002J\u000e\u0010\u001f\u001a\u00020\t2\u0006\u0010\u0014\u001a\u00020\u0005J\u000e\u0010 \u001a\u00020\n2\u0006\u0010\u0014\u001a\u00020\u0005J \u0010!\u001a\u00020\t2\u0006\u0010\u0014\u001a\u00020\u00052\u0006\u0010\"\u001a\u00020\t2\b\b\u0002\u0010#\u001a\u00020\u0005J(\u0010$\u001a\u00020\n2\u0006\u0010%\u001a\u00020\u00122\u0006\u0010\u0014\u001a\u00020\u00052\u0006\u0010&\u001a\u00020\u00052\u0006\u0010\'\u001a\u00020\tH\u0002J\"\u0010(\u001a\u00020\n2\u0006\u0010\u0014\u001a\u00020\u00052\u0012\u0010)\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n0\bJ\"\u0010*\u001a\u00020\n2\u0006\u0010\u0014\u001a\u00020\u00052\u0012\u0010)\u001a\u000e\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\n0\bJ\u0016\u0010+\u001a\u00020\t2\u0006\u0010\u0016\u001a\u00020\u00172\u0006\u0010&\u001a\u00020\u0005J\u0018\u0010,\u001a\u00020\n2\u0006\u0010\u0014\u001a\u00020\u00052\u0006\u0010-\u001a\u00020\u0005H\u0002J\u0010\u0010.\u001a\u00020\n2\u0006\u0010\u0014\u001a\u00020\u0005H\u0002J\u0016\u0010/\u001a\u00020\n2\u0006\u0010\u0016\u001a\u00020\u00172\u0006\u0010&\u001a\u00020\u0005J\u0010\u00100\u001a\u00020\n2\u0006\u0010\u0014\u001a\u00020\u0005H\u0002R \u0010\u0003\u001a\u0014\u0012\u0004\u0012\u00020\u0005\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00060\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R&\u0010\u0007\u001a\u001a\u0012\u0004\u0012\u00020\u0005\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n0\b0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R \u0010\u000b\u001a\u0014\u0012\u0004\u0012\u00020\u0005\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00060\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R,\u0010\f\u001a \u0012\u0004\u0012\u00020\u0005\u0012\u0016\u0012\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\n0\b0\r0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00120\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00062"}, d2 = {"Lcom/example/castapp/manager/WebSocketManager;", "", "()V", "activeFunctions", "Ljava/util/concurrent/ConcurrentHashMap;", "", "", "connectionStateListeners", "Lkotlin/Function1;", "", "", "functionEnabledStates", "messageListeners", "", "Lcom/example/castapp/websocket/ControlMessage;", "scope", "Lkotlinx/coroutines/CoroutineScope;", "webSocketClients", "Lcom/example/castapp/websocket/WebSocketClient;", "cleanupConnection", "connectionId", "createWebSocketConnection", "connection", "Lcom/example/castapp/model/Connection;", "destroyWebSocketConnectionForDeletion", "destroyWebSocketConnectionSync", "forceDisconnectConnection", "getApplicationContext", "Landroid/content/Context;", "getWebSocketClient", "handleWebSocketAbnormalDisconnection", "isConnectionActive", "removeListeners", "sendCastingState", "isCasting", "message", "sendFunctionControlMessage", "webSocketClient", "functionType", "enabled", "setConnectionStateListener", "listener", "setMessageListener", "startFunction", "stopAudioServiceForConnection", "audioType", "stopCastingServiceForConnection", "stopFunction", "updateConnectionStateOnDisconnect", "Companion", "app_debug"})
public final class WebSocketManager {
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.example.castapp.manager.WebSocketManager INSTANCE;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String FUNCTION_VIDEO = "video";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String FUNCTION_MEDIA_AUDIO = "media_audio";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String FUNCTION_MIC_AUDIO = "mic_audio";
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, com.example.castapp.websocket.WebSocketClient> webSocketClients = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, java.util.Set<java.lang.String>> activeFunctions = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, java.util.Set<java.lang.String>> functionEnabledStates = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, kotlin.jvm.functions.Function1<java.lang.Boolean, kotlin.Unit>> connectionStateListeners = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, java.util.List<kotlin.jvm.functions.Function1<com.example.castapp.websocket.ControlMessage, kotlin.Unit>>> messageListeners = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.CoroutineScope scope = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.manager.WebSocketManager.Companion Companion = null;
    
    private WebSocketManager() {
        super();
    }
    
    /**
     * 启动连接功能
     * @param connection 连接对象
     * @param functionType 功能类型
     * @return 是否成功启动
     */
    public final boolean startFunction(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.Connection connection, @org.jetbrains.annotations.NotNull()
    java.lang.String functionType) {
        return false;
    }
    
    /**
     * 停止连接功能
     * @param connection 连接对象
     * @param functionType 功能类型
     */
    public final void stopFunction(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.Connection connection, @org.jetbrains.annotations.NotNull()
    java.lang.String functionType) {
    }
    
    /**
     * 创建纯净WebSocket连接（不携带功能信息）- 简化版
     * 使用Connection模型的统一端口管理
     */
    private final boolean createWebSocketConnection(com.example.castapp.model.Connection connection, java.lang.String connectionId) {
        return false;
    }
    
    /**
     * 销毁WebSocket连接 - 同步版本，立即断开连接
     */
    private final void destroyWebSocketConnectionSync(java.lang.String connectionId) {
    }
    
    /**
     * 🔥 移除消息去重：允许重复发送功能控制消息
     */
    private final void sendFunctionControlMessage(com.example.castapp.websocket.WebSocketClient webSocketClient, java.lang.String connectionId, java.lang.String functionType, boolean enabled) {
    }
    
    /**
     * 清理连接相关数据 - 🚀 优化：支持异常断开处理
     */
    private final void cleanupConnection(java.lang.String connectionId) {
    }
    
    /**
     * 停止指定连接的投屏服务
     */
    private final void stopCastingServiceForConnection(java.lang.String connectionId) {
    }
    
    /**
     * 停止指定连接的音频服务
     */
    private final void stopAudioServiceForConnection(java.lang.String connectionId, java.lang.String audioType) {
    }
    
    /**
     * 🚀 新增：处理WebSocket异常断开（如接收端APP被强制结束）
     */
    private final void handleWebSocketAbnormalDisconnection(java.lang.String connectionId) {
    }
    
    /**
     * 更新连接状态为断开状态，自动恢复所有开关
     */
    private final void updateConnectionStateOnDisconnect(java.lang.String connectionId) {
    }
    
    /**
     * 获取应用上下文
     */
    @android.annotation.SuppressLint(value = {"PrivateApi"})
    private final android.content.Context getApplicationContext() {
        return null;
    }
    
    /**
     * 获取WebSocket客户端（统一连接管理）
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.websocket.WebSocketClient getWebSocketClient(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
        return null;
    }
    
    /**
     * 设置连接状态监听器
     */
    public final void setConnectionStateListener(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> listener) {
    }
    
    /**
     * 🚀 修复：设置消息监听器（支持多个监听器）
     */
    public final void setMessageListener(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.castapp.websocket.ControlMessage, kotlin.Unit> listener) {
    }
    
    /**
     * 🚀 修复：移除监听器（清空所有消息监听器）
     */
    public final void removeListeners(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 发送投屏状态
     */
    public final boolean sendCastingState(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean isCasting, @org.jetbrains.annotations.NotNull()
    java.lang.String message) {
        return false;
    }
    
    /**
     * 检查指定连接的WebSocket是否处于活跃状态
     * @param connectionId 连接ID
     * @return true表示连接存在且活跃，false表示连接不存在或已断开
     */
    public final boolean isConnectionActive(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
        return false;
    }
    
    /**
     * 🗑️ 强制断开WebSocket连接（用于窗口删除等场景）- 优化版本
     * 不依赖于功能状态判断，直接断开连接，避免重复处理
     */
    public final void forceDisconnectConnection(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 🚀 专门用于窗口删除的WebSocket连接销毁方法
     * 避免与正常断开流程产生冲突
     */
    private final void destroyWebSocketConnectionForDeletion(java.lang.String connectionId) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\t\u001a\u00020\bR\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0007\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/example/castapp/manager/WebSocketManager$Companion;", "", "()V", "FUNCTION_MEDIA_AUDIO", "", "FUNCTION_MIC_AUDIO", "FUNCTION_VIDEO", "INSTANCE", "Lcom/example/castapp/manager/WebSocketManager;", "getInstance", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.manager.WebSocketManager getInstance() {
            return null;
        }
    }
}