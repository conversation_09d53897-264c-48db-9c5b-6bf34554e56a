package com.example.castapp.utils;

/**
 * 遥控端富文本格式解析器
 * 用于解析从接收端传输过来的富文本格式数据
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\b\u0002\u0018\u00002\u00020\u0001:\u0001\nB\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u001a\u0010\u0005\u001a\u00020\u00062\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\bR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/example/castapp/utils/RemoteTextFormatParser;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "parseTextFormat", "Lcom/example/castapp/utils/RemoteTextFormatParser$ParsedTextFormat;", "formatData", "", "", "ParsedTextFormat", "app_debug"})
public final class RemoteTextFormatParser {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    
    public RemoteTextFormatParser(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    /**
     * 解析文字格式数据
     * @param formatData 从接收端传输过来的格式数据
     * @return 解析后的格式信息
     */
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.utils.RemoteTextFormatParser.ParsedTextFormat parseTextFormat(@org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.String, ? extends java.lang.Object> formatData) {
        return null;
    }
    
    /**
     * 富文本格式信息数据类
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u0007\n\u0002\b%\b\u0086\b\u0018\u00002\u00020\u0001B\u0081\u0001\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\u0007\u0012\b\b\u0002\u0010\t\u001a\u00020\n\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0002\u0010\r\u001a\u00020\u000e\u0012\b\b\u0002\u0010\u000f\u001a\u00020\n\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0007\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u0007\u0012\b\b\u0002\u0010\u0012\u001a\u00020\n\u00a2\u0006\u0002\u0010\u0013J\t\u0010\"\u001a\u00020\u0003H\u00c6\u0003J\t\u0010#\u001a\u00020\u0007H\u00c6\u0003J\t\u0010$\u001a\u00020\u0007H\u00c6\u0003J\t\u0010%\u001a\u00020\nH\u00c6\u0003J\u000b\u0010&\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010\'\u001a\u00020\u0007H\u00c6\u0003J\t\u0010(\u001a\u00020\u0007H\u00c6\u0003J\t\u0010)\u001a\u00020\nH\u00c6\u0003J\u000b\u0010*\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010+\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010,\u001a\u00020\u000eH\u00c6\u0003J\t\u0010-\u001a\u00020\nH\u00c6\u0003J\u0087\u0001\u0010.\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00072\b\b\u0002\u0010\t\u001a\u00020\n2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\r\u001a\u00020\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\n2\b\b\u0002\u0010\u0010\u001a\u00020\u00072\b\b\u0002\u0010\u0011\u001a\u00020\u00072\b\b\u0002\u0010\u0012\u001a\u00020\nH\u00c6\u0001J\u0013\u0010/\u001a\u00020\u00072\b\u00100\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u00101\u001a\u00020\nH\u00d6\u0001J\t\u00102\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\f\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0013\u0010\u000b\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0015R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0011\u0010\u0010\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u001aR\u0011\u0010\b\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u001aR\u0011\u0010\u0011\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u001aR\u0011\u0010\r\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001cR\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001eR\u0011\u0010\u000f\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0018R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u0015R\u0011\u0010\u0012\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u0018\u00a8\u00063"}, d2 = {"Lcom/example/castapp/utils/RemoteTextFormatParser$ParsedTextFormat;", "", "textContent", "", "spannableString", "Landroid/text/SpannableString;", "isBold", "", "isItalic", "fontSize", "", "fontName", "fontFamily", "lineSpacing", "", "textAlignment", "hasRichTextFormat", "isWindowColorEnabled", "windowBackgroundColor", "(Ljava/lang/String;Landroid/text/SpannableString;ZZILjava/lang/String;Ljava/lang/String;FIZZI)V", "getFontFamily", "()Ljava/lang/String;", "getFontName", "getFontSize", "()I", "getHasRichTextFormat", "()Z", "getLineSpacing", "()F", "getSpannableString", "()Landroid/text/SpannableString;", "getTextAlignment", "getTextContent", "getWindowBackgroundColor", "component1", "component10", "component11", "component12", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "toString", "app_debug"})
    public static final class ParsedTextFormat {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String textContent = null;
        @org.jetbrains.annotations.Nullable()
        private final android.text.SpannableString spannableString = null;
        private final boolean isBold = false;
        private final boolean isItalic = false;
        private final int fontSize = 0;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String fontName = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String fontFamily = null;
        private final float lineSpacing = 0.0F;
        private final int textAlignment = 0;
        private final boolean hasRichTextFormat = false;
        private final boolean isWindowColorEnabled = false;
        private final int windowBackgroundColor = 0;
        
        public ParsedTextFormat(@org.jetbrains.annotations.NotNull()
        java.lang.String textContent, @org.jetbrains.annotations.Nullable()
        android.text.SpannableString spannableString, boolean isBold, boolean isItalic, int fontSize, @org.jetbrains.annotations.Nullable()
        java.lang.String fontName, @org.jetbrains.annotations.Nullable()
        java.lang.String fontFamily, float lineSpacing, int textAlignment, boolean hasRichTextFormat, boolean isWindowColorEnabled, int windowBackgroundColor) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getTextContent() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final android.text.SpannableString getSpannableString() {
            return null;
        }
        
        public final boolean isBold() {
            return false;
        }
        
        public final boolean isItalic() {
            return false;
        }
        
        public final int getFontSize() {
            return 0;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getFontName() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getFontFamily() {
            return null;
        }
        
        public final float getLineSpacing() {
            return 0.0F;
        }
        
        public final int getTextAlignment() {
            return 0;
        }
        
        public final boolean getHasRichTextFormat() {
            return false;
        }
        
        public final boolean isWindowColorEnabled() {
            return false;
        }
        
        public final int getWindowBackgroundColor() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        public final boolean component10() {
            return false;
        }
        
        public final boolean component11() {
            return false;
        }
        
        public final int component12() {
            return 0;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final android.text.SpannableString component2() {
            return null;
        }
        
        public final boolean component3() {
            return false;
        }
        
        public final boolean component4() {
            return false;
        }
        
        public final int component5() {
            return 0;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component6() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component7() {
            return null;
        }
        
        public final float component8() {
            return 0.0F;
        }
        
        public final int component9() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.utils.RemoteTextFormatParser.ParsedTextFormat copy(@org.jetbrains.annotations.NotNull()
        java.lang.String textContent, @org.jetbrains.annotations.Nullable()
        android.text.SpannableString spannableString, boolean isBold, boolean isItalic, int fontSize, @org.jetbrains.annotations.Nullable()
        java.lang.String fontName, @org.jetbrains.annotations.Nullable()
        java.lang.String fontFamily, float lineSpacing, int textAlignment, boolean hasRichTextFormat, boolean isWindowColorEnabled, int windowBackgroundColor) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}