package com.example.castapp.manager;

/**
 * 遥控端窗口信息缓存管理器
 * 负责在遥控端本地保存和维护窗口设置信息
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0004\u0018\u0000 \u00142\u00020\u0001:\u0001\u0014B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0016\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nJ\u0016\u0010\u000b\u001a\u00020\f2\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nJ\u0016\u0010\r\u001a\u00020\u000e2\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nJ\u001c\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00110\u00102\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nJ$\u0010\u0012\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00110\u0010R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0015"}, d2 = {"Lcom/example/castapp/manager/RemoteWindowInfoCache;", "", "()V", "gson", "Lcom/google/gson/Gson;", "clearWindowInfo", "", "context", "Landroid/content/Context;", "receiverId", "", "getCacheTimestamp", "", "hasCachedWindowInfo", "", "loadWindowInfo", "", "Lcom/example/castapp/model/CastWindowInfo;", "saveWindowInfo", "windowInfoList", "Companion", "app_debug"})
public final class RemoteWindowInfoCache {
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.example.castapp.manager.RemoteWindowInfoCache INSTANCE;
    @org.jetbrains.annotations.NotNull()
    private final com.google.gson.Gson gson = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.manager.RemoteWindowInfoCache.Companion Companion = null;
    
    private RemoteWindowInfoCache() {
        super();
    }
    
    /**
     * 保存窗口信息到本地缓存
     * @param context 上下文
     * @param receiverId 接收端设备ID
     * @param windowInfoList 窗口信息列表
     */
    public final void saveWindowInfo(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String receiverId, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.castapp.model.CastWindowInfo> windowInfoList) {
    }
    
    /**
     * 从本地缓存加载窗口信息
     * @param context 上下文
     * @param receiverId 接收端设备ID
     * @return 窗口信息列表，如果没有缓存则返回空列表
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.castapp.model.CastWindowInfo> loadWindowInfo(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String receiverId) {
        return null;
    }
    
    /**
     * 检查缓存是否存在
     * @param context 上下文
     * @param receiverId 接收端设备ID
     * @return 是否存在缓存
     */
    public final boolean hasCachedWindowInfo(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String receiverId) {
        return false;
    }
    
    /**
     * 清除指定设备的缓存
     * @param context 上下文
     * @param receiverId 接收端设备ID
     */
    public final void clearWindowInfo(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String receiverId) {
    }
    
    /**
     * 获取缓存时间戳
     * @param context 上下文
     * @param receiverId 接收端设备ID
     * @return 缓存时间戳，如果没有缓存则返回0
     */
    public final long getCacheTimestamp(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String receiverId) {
        return 0L;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0005\u001a\u00020\u0004R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0006"}, d2 = {"Lcom/example/castapp/manager/RemoteWindowInfoCache$Companion;", "", "()V", "INSTANCE", "Lcom/example/castapp/manager/RemoteWindowInfoCache;", "getInstance", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.manager.RemoteWindowInfoCache getInstance() {
            return null;
        }
    }
}