package com.example.castapp.rtp;

/**
 * 视频解码器管理器 - 简化版
 * 专注于解码器生命周期管理，移除连接管理职责
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000h\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u0012\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000e\n\u0002\u0010\"\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0014\n\u0002\u0018\u0002\n\u0002\b\u0005\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0018\u0010\u001a\u001a\u00020\r2\u0006\u0010\u001b\u001a\u00020\u00052\u0006\u0010\u001c\u001a\u00020\u0012H\u0002J\u0006\u0010\u001d\u001a\u00020\rJ\u0010\u0010\u001e\u001a\u00020\r2\u0006\u0010\u001b\u001a\u00020\u0005H\u0002J\u000e\u0010\u001f\u001a\u00020\r2\u0006\u0010\u001b\u001a\u00020\u0005J\u000e\u0010 \u001a\u00020\r2\u0006\u0010\u001b\u001a\u00020\u0005J8\u0010!\u001a\u00020\r2\u0006\u0010\u001b\u001a\u00020\u00052\u001e\u0010\"\u001a\u001a\u0012\u0004\u0012\u00020\u0005\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\r0\f0\t2\u0006\u0010#\u001a\u00020\u0005H\u0002J\u000e\u0010$\u001a\u00020\r2\u0006\u0010\u001b\u001a\u00020\u0005J\u000e\u0010%\u001a\u00020\r2\u0006\u0010\u001b\u001a\u00020\u0005J\f\u0010&\u001a\b\u0012\u0004\u0012\u00020\u00050\'J\u0012\u0010(\u001a\u0004\u0018\u00010\n2\u0006\u0010\u001b\u001a\u00020\u0005H\u0002J\u000e\u0010)\u001a\u00020\r2\u0006\u0010\u001b\u001a\u00020\u0005J\u0016\u0010*\u001a\u00020\r2\u0006\u0010\u001b\u001a\u00020\u00052\u0006\u0010+\u001a\u00020,J\u000e\u0010-\u001a\u00020\r2\u0006\u0010\u001b\u001a\u00020\u0005J\"\u0010.\u001a\u00020\r2\u0006\u0010\u001b\u001a\u00020\u00052\b\u0010/\u001a\u0004\u0018\u00010\u00122\b\u00100\u001a\u0004\u0018\u00010\u0012J;\u00101\u001a\u00020\r2\u0006\u0010\u001b\u001a\u00020\u00052\b\u0010/\u001a\u0004\u0018\u00010\u00122\b\u00100\u001a\u0004\u0018\u00010\u00122\b\u00102\u001a\u0004\u0018\u00010\u00162\b\u00103\u001a\u0004\u0018\u00010\u0016\u00a2\u0006\u0002\u00104J\u000e\u00105\u001a\u00020\u00072\u0006\u0010\u001b\u001a\u00020\u0005J\u0018\u00106\u001a\u00020\u00072\u0006\u0010\u001b\u001a\u00020\u00052\u0006\u00107\u001a\u00020\nH\u0002J\"\u00108\u001a\u00020\r2\u0006\u0010\u001b\u001a\u00020\u00052\u0012\u00109\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\r0\fJ\"\u0010:\u001a\u00020\r2\u0006\u0010\u001b\u001a\u00020\u00052\u0012\u00109\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\r0\fJ.\u0010;\u001a\u00020\r2\u0006\u0010\u001b\u001a\u00020\u00052\u001e\u00109\u001a\u001a\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\r0\u0018J&\u0010<\u001a\u00020\r2\u0006\u0010\u001b\u001a\u00020\u00052\u0014\u00109\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\r\u0018\u00010\fH\u0002J\u001a\u0010=\u001a\u00020\r2\u0006\u0010\u001b\u001a\u00020\u00052\b\b\u0002\u0010>\u001a\u00020\u0007H\u0002J\u0018\u0010?\u001a\u00020\r2\u0006\u0010\u001b\u001a\u00020\u00052\b\u0010@\u001a\u0004\u0018\u00010AJ\u000e\u0010B\u001a\u00020\r2\u0006\u0010\u001b\u001a\u00020\u0005J\u001e\u0010C\u001a\u00020\u00072\u0006\u0010\u001b\u001a\u00020\u00052\u0006\u0010D\u001a\u00020\u00162\u0006\u0010E\u001a\u00020\u0016RN\u0010\u0003\u001aB\u0012\f\u0012\n \u0006*\u0004\u0018\u00010\u00050\u0005\u0012\f\u0012\n \u0006*\u0004\u0018\u00010\u00070\u0007 \u0006* \u0012\f\u0012\n \u0006*\u0004\u0018\u00010\u00050\u0005\u0012\f\u0012\n \u0006*\u0004\u0018\u00010\u00070\u0007\u0018\u00010\u00040\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\n0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R&\u0010\u000b\u001a\u001a\u0012\u0004\u0012\u00020\u0005\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\r0\f0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000RN\u0010\u000e\u001aB\u0012\f\u0012\n \u0006*\u0004\u0018\u00010\u00050\u0005\u0012\f\u0012\n \u0006*\u0004\u0018\u00010\u00070\u0007 \u0006* \u0012\f\u0012\n \u0006*\u0004\u0018\u00010\u00050\u0005\u0012\f\u0012\n \u0006*\u0004\u0018\u00010\u00070\u0007\u0018\u00010\u00040\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R&\u0010\u000f\u001a\u001a\u0012\u0004\u0012\u00020\u0005\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\r0\f0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R \u0010\u0010\u001a\u0014\u0012\u0004\u0012\u00020\u0005\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00120\u00110\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R \u0010\u0013\u001a\u0014\u0012\u0004\u0012\u00020\u0005\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00120\u00110\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R&\u0010\u0014\u001a\u001a\u0012\u0004\u0012\u00020\u0005\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00160\u00150\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R2\u0010\u0017\u001a&\u0012\u0004\u0012\u00020\u0005\u0012\u001c\u0012\u001a\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\r0\u00180\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000RN\u0010\u0019\u001aB\u0012\f\u0012\n \u0006*\u0004\u0018\u00010\u00050\u0005\u0012\f\u0012\n \u0006*\u0004\u0018\u00010\u00070\u0007 \u0006* \u0012\f\u0012\n \u0006*\u0004\u0018\u00010\u00050\u0005\u0012\f\u0012\n \u0006*\u0004\u0018\u00010\u00070\u0007\u0018\u00010\u00040\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006F"}, d2 = {"Lcom/example/castapp/rtp/MultiConnectionManager;", "", "()V", "creatingDecoders", "Ljava/util/concurrent/ConcurrentHashMap$KeySetView;", "", "kotlin.jvm.PlatformType", "", "decoders", "Ljava/util/concurrent/ConcurrentHashMap;", "Lcom/example/castapp/codec/VideoDecoder;", "disconnectCallbacks", "Lkotlin/Function1;", "", "disconnectedConnections", "displayCallbacks", "pendingConfigurationData", "Ljava/util/concurrent/CopyOnWriteArrayList;", "", "pendingData", "pendingResolutionData", "Lkotlin/Pair;", "", "screenResolutionCallbacks", "Lkotlin/Function3;", "videoStreamStoppedConnections", "cacheFrameData", "connectionId", "data", "cleanup", "cleanupConnectionData", "clearDisconnectionFlag", "clearVideoStreamStoppedState", "ensureCallbackRegistered", "callbacks", "callbackType", "ensureDisconnectCallbackRegistered", "ensureDisplayCallbackRegistered", "getAllConnectionIds", "", "getOrCreateDecoder", "handleConnectionDisconnect", "handleH264Data", "payloadView", "Lcom/example/castapp/rtp/PayloadView;", "handleVideoStreamStop", "handleWebSocketH264Config", "spsData", "ppsData", "handleWebSocketH264ConfigWithResolution", "width", "height", "(Ljava/lang/String;[B[BLjava/lang/Integer;Ljava/lang/Integer;)V", "isConnectionValid", "processCachedData", "decoder", "registerDisconnectCallback", "callback", "registerDisplayCallback", "registerScreenResolutionCallback", "safeInvokeDisconnectCallback", "safeStopDecoder", "useCleanup", "setSurface", "surface", "Landroid/view/Surface;", "triggerDisplayCallback", "updateDecoderResolution", "newWidth", "newHeight", "app_debug"})
public final class MultiConnectionManager {
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, com.example.castapp.codec.VideoDecoder> decoders = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, kotlin.jvm.functions.Function1<java.lang.String, kotlin.Unit>> displayCallbacks = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, kotlin.jvm.functions.Function1<java.lang.String, kotlin.Unit>> disconnectCallbacks = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, kotlin.jvm.functions.Function3<java.lang.String, java.lang.Integer, java.lang.Integer, kotlin.Unit>> screenResolutionCallbacks = null;
    private final java.util.concurrent.ConcurrentHashMap.KeySetView<java.lang.String, java.lang.Boolean> creatingDecoders = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, java.util.concurrent.CopyOnWriteArrayList<byte[]>> pendingData = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, java.util.concurrent.CopyOnWriteArrayList<byte[]>> pendingConfigurationData = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, kotlin.Pair<java.lang.Integer, java.lang.Integer>> pendingResolutionData = null;
    private final java.util.concurrent.ConcurrentHashMap.KeySetView<java.lang.String, java.lang.Boolean> disconnectedConnections = null;
    private final java.util.concurrent.ConcurrentHashMap.KeySetView<java.lang.String, java.lang.Boolean> videoStreamStoppedConnections = null;
    
    public MultiConnectionManager() {
        super();
    }
    
    /**
     * 注册连接的显示回调
     */
    public final void registerDisplayCallback(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> callback) {
    }
    
    /**
     * 注册连接断开回调
     */
    public final void registerDisconnectCallback(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> callback) {
    }
    
    /**
     * 为特定连接动态注册回调（如果尚未注册）
     */
    private final void ensureCallbackRegistered(java.lang.String connectionId, java.util.concurrent.ConcurrentHashMap<java.lang.String, kotlin.jvm.functions.Function1<java.lang.String, kotlin.Unit>> callbacks, java.lang.String callbackType) {
    }
    
    public final void ensureDisplayCallbackRegistered(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    public final void ensureDisconnectCallbackRegistered(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 注册屏幕分辨率回调
     */
    public final void registerScreenResolutionCallback(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super java.lang.String, ? super java.lang.Integer, ? super java.lang.Integer, kotlin.Unit> callback) {
    }
    
    /**
     * 检查连接状态是否有效
     */
    public final boolean isConnectionValid(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
        return false;
    }
    
    /**
     * 触发显示回调（用于WebSocket连接请求）
     */
    public final void triggerDisplayCallback(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 处理通过WebSocket接收到的H.264配置数据（兼容性方法）
     */
    public final void handleWebSocketH264Config(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.Nullable()
    byte[] spsData, @org.jetbrains.annotations.Nullable()
    byte[] ppsData) {
    }
    
    /**
     * 🚀 新增：处理通过WebSocket接收到的H.264配置数据（包含分辨率信息）
     */
    public final void handleWebSocketH264ConfigWithResolution(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.Nullable()
    byte[] spsData, @org.jetbrains.annotations.Nullable()
    byte[] ppsData, @org.jetbrains.annotations.Nullable()
    java.lang.Integer width, @org.jetbrains.annotations.Nullable()
    java.lang.Integer height) {
    }
    
    /**
     * 缓存帧数据，支持大小限制
     */
    private final void cacheFrameData(java.lang.String connectionId, byte[] data) {
    }
    
    /**
     * 处理接收到的H.264数据（零拷贝优化）- 增强连接状态检查
     */
    public final void handleH264Data(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    com.example.castapp.rtp.PayloadView payloadView) {
    }
    
    /**
     * 获取或创建解码器
     */
    private final com.example.castapp.codec.VideoDecoder getOrCreateDecoder(java.lang.String connectionId) {
        return null;
    }
    
    /**
     * 清理连接相关数据
     * 🚀 增强版：清理分辨率缓存
     */
    private final void cleanupConnectionData(java.lang.String connectionId) {
    }
    
    /**
     * 处理缓存数据 - 架构优化：只处理WebSocket配置数据和RTP帧数据
     * 🚀 增强版：支持分辨率信息处理
     */
    private final boolean processCachedData(java.lang.String connectionId, com.example.castapp.codec.VideoDecoder decoder) {
        return false;
    }
    
    /**
     * 为连接设置Surface
     */
    public final void setSurface(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.Nullable()
    android.view.Surface surface) {
    }
    
    /**
     * 更新解码器分辨率
     * 用于处理发送端分辨率实时调整
     */
    public final boolean updateDecoderResolution(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, int newWidth, int newHeight) {
        return false;
    }
    
    /**
     * 安全停止解码器
     */
    private final void safeStopDecoder(java.lang.String connectionId, boolean useCleanup) {
    }
    
    /**
     * 处理视频流停止（但连接可能仍然存在，用于音频流）
     */
    public final void handleVideoStreamStop(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 安全调用断开回调
     */
    private final void safeInvokeDisconnectCallback(java.lang.String connectionId, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> callback) {
    }
    
    /**
     * 处理连接断开
     */
    public final void handleConnectionDisconnect(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 清理指定连接的断开标记，允许重连（统一ID架构）- 根源级清理
     */
    public final void clearDisconnectionFlag(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 获取所有活跃连接ID
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Set<java.lang.String> getAllConnectionIds() {
        return null;
    }
    
    /**
     * 清理视频流停止状态，允许视频流重新启动（统一ID架构）- 根源级修复
     */
    public final void clearVideoStreamStoppedState(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 清理所有连接
     */
    public final void cleanup() {
    }
}