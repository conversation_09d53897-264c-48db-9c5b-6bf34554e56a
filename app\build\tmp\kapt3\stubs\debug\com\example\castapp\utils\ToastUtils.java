package com.example.castapp.utils;

/**
 * Toast工具类
 * 提供简洁的Toast消息显示功能，替代ToastManager
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0000\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0016\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bJ\u001e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n\u00a8\u0006\u000b"}, d2 = {"Lcom/example/castapp/utils/ToastUtils;", "", "()V", "showToast", "", "context", "Landroid/content/Context;", "message", "", "duration", "", "app_debug"})
public final class ToastUtils {
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.utils.ToastUtils INSTANCE = null;
    
    private ToastUtils() {
        super();
    }
    
    /**
     * 显示短时间Toast消息
     * @param context 上下文
     * @param message 消息内容
     */
    public final void showToast(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String message) {
    }
    
    /**
     * 显示Toast消息
     * @param context 上下文
     * @param message 消息内容
     * @param duration 显示时长
     */
    public final void showToast(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String message, int duration) {
    }
}