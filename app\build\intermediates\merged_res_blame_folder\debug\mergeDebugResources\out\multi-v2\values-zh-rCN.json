{"logs": [{"outputFile": "com.example.castapp-mergeDebugResources-42:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\dbc1af71e9c7a6d81d74e5415e27cca6\\transformed\\material-1.12.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,443,513,590,681,787,860,911,973,1050,1109,1168,1246,1307,1364,1420,1479,1537,1591,1676,1732,1790,1844,1909,2001,2075,2147,2226,2300,2376,2498,2560,2622,2721,2800,2874,2924,2975,3041,3105,3174,3245,3316,3377,3448,3515,3575,3661,3740,3807,3890,3975,4049,4114,4190,4238,4311,4375,4451,4529,4591,4655,4718,4783,4863,4939,5017,5093,5147,5202,5271,5346,5419", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,63,61,69,69,76,90,105,72,50,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,71,78,73,75,121,61,61,98,78,73,49,50,65,63,68,70,70,60,70,66,59,85,78,66,82,84,73,64,75,47,72,63,75,77,61,63,62,64,79,75,77,75,53,54,68,74,72,69", "endOffsets": "242,306,368,438,508,585,676,782,855,906,968,1045,1104,1163,1241,1302,1359,1415,1474,1532,1586,1671,1727,1785,1839,1904,1996,2070,2142,2221,2295,2371,2493,2555,2617,2716,2795,2869,2919,2970,3036,3100,3169,3240,3311,3372,3443,3510,3570,3656,3735,3802,3885,3970,4044,4109,4185,4233,4306,4370,4446,4524,4586,4650,4713,4778,4858,4934,5012,5088,5142,5197,5266,5341,5414,5484"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2854,2918,2980,3050,3120,3861,3952,4058,4131,4182,4244,4321,4380,4439,4517,4578,4635,4691,4750,4808,4862,4947,5003,5061,5115,5180,5272,5346,5418,5497,5571,5647,5769,5831,5893,5992,6071,6145,6195,6246,6312,6376,6445,6516,6587,6648,6719,6786,6846,6932,7011,7078,7161,7246,7320,7385,7461,7509,7582,7646,7722,7800,7862,7926,7989,8054,8134,8210,8288,8364,8418,8473,8621,8696,8769", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "endColumns": "12,63,61,69,69,76,90,105,72,50,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,71,78,73,75,121,61,61,98,78,73,49,50,65,63,68,70,70,60,70,66,59,85,78,66,82,84,73,64,75,47,72,63,75,77,61,63,62,64,79,75,77,75,53,54,68,74,72,69", "endOffsets": "292,2913,2975,3045,3115,3192,3947,4053,4126,4177,4239,4316,4375,4434,4512,4573,4630,4686,4745,4803,4857,4942,4998,5056,5110,5175,5267,5341,5413,5492,5566,5642,5764,5826,5888,5987,6066,6140,6190,6241,6307,6371,6440,6511,6582,6643,6714,6781,6841,6927,7006,7073,7156,7241,7315,7380,7456,7504,7577,7641,7717,7795,7857,7921,7984,8049,8129,8205,8283,8359,8413,8468,8537,8691,8764,8834"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f3713a0c8f8ac4a98c195ead755fc68d\\transformed\\appcompat-1.7.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,2662", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,2736"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,487,587,669,766,872,949,1024,1115,1208,1305,1401,1495,1588,1683,1775,1866,1957,2035,2131,2226,2321,2418,2514,2612,2760,8542", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "387,482,582,664,761,867,944,1019,1110,1203,1300,1396,1490,1583,1678,1770,1861,1952,2030,2126,2221,2316,2413,2509,2607,2755,2849,8616"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b74c097ccea204c2309e4f5f5bb2f587\\transformed\\core-1.13.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,719", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,815"}, "to": {"startLines": "38,39,40,41,42,43,44,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3197,3289,3390,3484,3578,3671,3765,8839", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "3284,3385,3479,3573,3666,3760,3856,8935"}}]}]}