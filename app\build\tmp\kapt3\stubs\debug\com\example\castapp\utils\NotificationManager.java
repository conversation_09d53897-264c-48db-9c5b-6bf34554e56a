package com.example.castapp.utils;

/**
 * 通知管理器
 * 消除各服务中重复的通知创建代码
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u00c6\u0002\u0018\u00002\u00020\u0001:\u0003\u001d\u001e\u001fB\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0016\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bJ\u0016\u0010\t\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\bJ\u000e\u0010\u000b\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006J\u000e\u0010\f\u001a\u00020\r2\u0006\u0010\u0005\u001a\u00020\u0006J\u001e\u0010\u000e\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u0012J\u0016\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u000f\u001a\u00020\u0010J\u001e\u0010\u0015\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\b2\u0006\u0010\u0016\u001a\u00020\rJ\u001e\u0010\u0017\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\b2\u0006\u0010\u0016\u001a\u00020\rJ&\u0010\u0018\u001a\u00020\r2\u0006\u0010\u0005\u001a\u00020\u00062\u000e\u0010\u0019\u001a\n\u0012\u0006\b\u0001\u0012\u00020\u001b0\u001a2\u0006\u0010\u001c\u001a\u00020\b\u00a8\u0006 "}, d2 = {"Lcom/example/castapp/utils/NotificationManager;", "", "()V", "createAudioStreamingNotification", "Landroid/app/Notification;", "context", "Landroid/content/Context;", "title", "", "createCastingNotification", "content", "createFloatingStopwatchNotification", "createMainActivityPendingIntent", "Landroid/app/PendingIntent;", "createNotification", "type", "Lcom/example/castapp/utils/NotificationManager$NotificationType;", "config", "Lcom/example/castapp/utils/NotificationManager$NotificationConfig;", "createNotificationChannel", "", "createReceivingNotification", "stopPendingIntent", "createRemoteControlNotification", "createServiceStopPendingIntent", "serviceClass", "Ljava/lang/Class;", "Landroid/app/Service;", "stopAction", "NotificationAction", "NotificationConfig", "NotificationType", "app_debug"})
public final class NotificationManager {
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.utils.NotificationManager INSTANCE = null;
    
    private NotificationManager() {
        super();
    }
    
    /**
     * 创建通知渠道
     */
    public final void createNotificationChannel(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.example.castapp.utils.NotificationManager.NotificationType type) {
    }
    
    /**
     * 创建通知
     */
    @org.jetbrains.annotations.NotNull()
    public final android.app.Notification createNotification(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.example.castapp.utils.NotificationManager.NotificationType type, @org.jetbrains.annotations.NotNull()
    com.example.castapp.utils.NotificationManager.NotificationConfig config) {
        return null;
    }
    
    /**
     * 创建默认的主界面PendingIntent
     */
    @org.jetbrains.annotations.NotNull()
    public final android.app.PendingIntent createMainActivityPendingIntent(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    /**
     * 创建服务停止PendingIntent
     */
    @org.jetbrains.annotations.NotNull()
    public final android.app.PendingIntent createServiceStopPendingIntent(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.Class<? extends android.app.Service> serviceClass, @org.jetbrains.annotations.NotNull()
    java.lang.String stopAction) {
        return null;
    }
    
    /**
     * 便捷方法：创建投屏服务通知
     */
    @org.jetbrains.annotations.NotNull()
    public final android.app.Notification createCastingNotification(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String content) {
        return null;
    }
    
    /**
     * 便捷方法：创建接收服务通知（带停止按钮）
     */
    @org.jetbrains.annotations.NotNull()
    public final android.app.Notification createReceivingNotification(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String content, @org.jetbrains.annotations.NotNull()
    android.app.PendingIntent stopPendingIntent) {
        return null;
    }
    
    /**
     * 便捷方法：创建音频流服务通知
     */
    @org.jetbrains.annotations.NotNull()
    public final android.app.Notification createAudioStreamingNotification(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String title) {
        return null;
    }
    
    /**
     * 便捷方法：创建悬浮秒表通知
     */
    @org.jetbrains.annotations.NotNull()
    public final android.app.Notification createFloatingStopwatchNotification(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    /**
     * 便捷方法：创建远程控制服务通知（带停止按钮）
     */
    @org.jetbrains.annotations.NotNull()
    public final android.app.Notification createRemoteControlNotification(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String content, @org.jetbrains.annotations.NotNull()
    android.app.PendingIntent stopPendingIntent) {
        return null;
    }
    
    /**
     * 通知动作配置类
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001B\u001d\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\t\u0010\u000f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0011\u001a\u00020\u0007H\u00c6\u0003J\'\u0010\u0012\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u00c6\u0001J\u0013\u0010\u0013\u001a\u00020\u00142\b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\u0017\u001a\u00020\u0005H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000e\u00a8\u0006\u0018"}, d2 = {"Lcom/example/castapp/utils/NotificationManager$NotificationAction;", "", "iconRes", "", "title", "", "pendingIntent", "Landroid/app/PendingIntent;", "(ILjava/lang/String;Landroid/app/PendingIntent;)V", "getIconRes", "()I", "getPendingIntent", "()Landroid/app/PendingIntent;", "getTitle", "()Ljava/lang/String;", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "toString", "app_debug"})
    public static final class NotificationAction {
        private final int iconRes = 0;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String title = null;
        @org.jetbrains.annotations.NotNull()
        private final android.app.PendingIntent pendingIntent = null;
        
        public NotificationAction(int iconRes, @org.jetbrains.annotations.NotNull()
        java.lang.String title, @org.jetbrains.annotations.NotNull()
        android.app.PendingIntent pendingIntent) {
            super();
        }
        
        public final int getIconRes() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getTitle() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.app.PendingIntent getPendingIntent() {
            return null;
        }
        
        public final int component1() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.app.PendingIntent component3() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.utils.NotificationManager.NotificationAction copy(int iconRes, @org.jetbrains.annotations.NotNull()
        java.lang.String title, @org.jetbrains.annotations.NotNull()
        android.app.PendingIntent pendingIntent) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    /**
     * 通知配置类
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u001b\b\u0086\b\u0018\u00002\u00020\u0001BY\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u0007\u001a\u00020\b\u0012\b\b\u0002\u0010\t\u001a\u00020\u0006\u0012\b\b\u0002\u0010\n\u001a\u00020\b\u0012\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u000f\u00a2\u0006\u0002\u0010\u0010J\t\u0010\u001d\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0006H\u00c6\u0003J\t\u0010 \u001a\u00020\bH\u00c6\u0003J\t\u0010!\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\"\u001a\u00020\bH\u00c6\u0003J\u000f\u0010#\u001a\b\u0012\u0004\u0012\u00020\r0\fH\u00c6\u0003J\u000b\u0010$\u001a\u0004\u0018\u00010\u000fH\u00c6\u0003Ja\u0010%\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\u00062\b\b\u0002\u0010\n\u001a\u00020\b2\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f2\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u000fH\u00c6\u0001J\u0013\u0010&\u001a\u00020\b2\b\u0010\'\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010(\u001a\u00020\u0006H\u00d6\u0001J\t\u0010)\u001a\u00020\u0003H\u00d6\u0001R\u0017\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0013\u0010\u000e\u001a\u0004\u0018\u00010\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\u0019R\u0011\u0010\t\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0018R\u0011\u0010\n\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0019R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0014\u00a8\u0006*"}, d2 = {"Lcom/example/castapp/utils/NotificationManager$NotificationConfig;", "", "title", "", "content", "iconRes", "", "isOngoing", "", "priority", "showBadge", "actions", "", "Lcom/example/castapp/utils/NotificationManager$NotificationAction;", "contentIntent", "Landroid/app/PendingIntent;", "(Ljava/lang/String;Ljava/lang/String;IZIZLjava/util/List;Landroid/app/PendingIntent;)V", "getActions", "()Ljava/util/List;", "getContent", "()Ljava/lang/String;", "getContentIntent", "()Landroid/app/PendingIntent;", "getIconRes", "()I", "()Z", "getPriority", "getShowBadge", "getTitle", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "equals", "other", "hashCode", "toString", "app_debug"})
    public static final class NotificationConfig {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String title = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String content = null;
        private final int iconRes = 0;
        private final boolean isOngoing = false;
        private final int priority = 0;
        private final boolean showBadge = false;
        @org.jetbrains.annotations.NotNull()
        private final java.util.List<com.example.castapp.utils.NotificationManager.NotificationAction> actions = null;
        @org.jetbrains.annotations.Nullable()
        private final android.app.PendingIntent contentIntent = null;
        
        public NotificationConfig(@org.jetbrains.annotations.NotNull()
        java.lang.String title, @org.jetbrains.annotations.NotNull()
        java.lang.String content, int iconRes, boolean isOngoing, int priority, boolean showBadge, @org.jetbrains.annotations.NotNull()
        java.util.List<com.example.castapp.utils.NotificationManager.NotificationAction> actions, @org.jetbrains.annotations.Nullable()
        android.app.PendingIntent contentIntent) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getTitle() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getContent() {
            return null;
        }
        
        public final int getIconRes() {
            return 0;
        }
        
        public final boolean isOngoing() {
            return false;
        }
        
        public final int getPriority() {
            return 0;
        }
        
        public final boolean getShowBadge() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<com.example.castapp.utils.NotificationManager.NotificationAction> getActions() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final android.app.PendingIntent getContentIntent() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component2() {
            return null;
        }
        
        public final int component3() {
            return 0;
        }
        
        public final boolean component4() {
            return false;
        }
        
        public final int component5() {
            return 0;
        }
        
        public final boolean component6() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<com.example.castapp.utils.NotificationManager.NotificationAction> component7() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final android.app.PendingIntent component8() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.utils.NotificationManager.NotificationConfig copy(@org.jetbrains.annotations.NotNull()
        java.lang.String title, @org.jetbrains.annotations.NotNull()
        java.lang.String content, int iconRes, boolean isOngoing, int priority, boolean showBadge, @org.jetbrains.annotations.NotNull()
        java.util.List<com.example.castapp.utils.NotificationManager.NotificationAction> actions, @org.jetbrains.annotations.Nullable()
        android.app.PendingIntent contentIntent) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    /**
     * 通知类型枚举
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0011\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B1\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0007\u001a\u00020\u0005\u0012\b\b\u0002\u0010\b\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\tR\u0011\u0010\u0007\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u000bR\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000bR\u0011\u0010\b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000fj\u0002\b\u0011j\u0002\b\u0012j\u0002\b\u0013j\u0002\b\u0014j\u0002\b\u0015\u00a8\u0006\u0016"}, d2 = {"Lcom/example/castapp/utils/NotificationManager$NotificationType;", "", "notificationId", "", "channelId", "", "channelName", "channelDescription", "importance", "(Ljava/lang/String;IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;I)V", "getChannelDescription", "()Ljava/lang/String;", "getChannelId", "getChannelName", "getImportance", "()I", "getNotificationId", "CASTING", "RECEIVING", "AUDIO_STREAMING", "FLOATING_STOPWATCH", "REMOTE_CONTROL", "app_debug"})
    public static enum NotificationType {
        /*public static final*/ CASTING /* = new CASTING(0, null, null, null, 0) */,
        /*public static final*/ RECEIVING /* = new RECEIVING(0, null, null, null, 0) */,
        /*public static final*/ AUDIO_STREAMING /* = new AUDIO_STREAMING(0, null, null, null, 0) */,
        /*public static final*/ FLOATING_STOPWATCH /* = new FLOATING_STOPWATCH(0, null, null, null, 0) */,
        /*public static final*/ REMOTE_CONTROL /* = new REMOTE_CONTROL(0, null, null, null, 0) */;
        private final int notificationId = 0;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String channelId = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String channelName = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String channelDescription = null;
        private final int importance = 0;
        
        NotificationType(int notificationId, java.lang.String channelId, java.lang.String channelName, java.lang.String channelDescription, int importance) {
        }
        
        public final int getNotificationId() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getChannelId() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getChannelName() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getChannelDescription() {
            return null;
        }
        
        public final int getImportance() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.example.castapp.utils.NotificationManager.NotificationType> getEntries() {
            return null;
        }
    }
}