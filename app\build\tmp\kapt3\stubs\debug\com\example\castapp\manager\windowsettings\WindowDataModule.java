package com.example.castapp.manager.windowsettings;

/**
 * 窗口数据模块
 * 负责管理所有投屏窗口相关的数据存储和映射关系
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000`\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\r\n\u0002\u0010\"\n\u0000\n\u0002\u0010$\n\u0002\b\u001f\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u0005J\u0016\u0010\u001b\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u00052\u0006\u0010\u001c\u001a\u00020\u0006J\u0006\u0010\u001d\u001a\u00020\u0019J\u0006\u0010\u001e\u001a\u00020\u0019J\u0006\u0010\u001f\u001a\u00020\u0019J\u0006\u0010 \u001a\u00020\u0019J\u0006\u0010!\u001a\u00020\u0019J\u0006\u0010\"\u001a\u00020\u0019J\u0006\u0010#\u001a\u00020\u0019J\u0010\u0010$\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u0005H\u0002J\u000e\u0010%\u001a\u00020\b2\u0006\u0010\u001a\u001a\u00020\u0005J\f\u0010&\u001a\b\u0012\u0004\u0012\u00020\u00050\'J\u0012\u0010(\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060)J\u000e\u0010*\u001a\u00020\b2\u0006\u0010\u001a\u001a\u00020\u0005J\b\u0010+\u001a\u0004\u0018\u00010\u0010J\b\u0010,\u001a\u0004\u0018\u00010\u0012J\u000e\u0010-\u001a\u00020\b2\u0006\u0010\u001a\u001a\u00020\u0005J\u000e\u0010.\u001a\u00020\b2\u0006\u0010\u001a\u001a\u00020\u0005J\u001c\u0010/\u001a\u0010\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\r\u0018\u00010\f2\u0006\u0010\u001a\u001a\u00020\u0005J\b\u00100\u001a\u0004\u0018\u00010\u0017J\u0006\u00101\u001a\u00020\rJ\u0010\u00102\u001a\u0004\u0018\u00010\u00062\u0006\u0010\u001a\u001a\u00020\u0005J\u0016\u00103\u001a\u00020\u00192\u0006\u00104\u001a\u00020\u00102\u0006\u00105\u001a\u00020\u0017J\u000e\u00106\u001a\u00020\b2\u0006\u0010\u001a\u001a\u00020\u0005J\u0010\u00107\u001a\u00020\b2\u0006\u0010\u001a\u001a\u00020\u0005H\u0002J\u000e\u00108\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u0005J\u000e\u00109\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u0005J\u000e\u0010:\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u0005J\u000e\u0010;\u001a\u00020\b2\u0006\u0010\u001a\u001a\u00020\u0005J\u000e\u0010<\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u0005J\u0010\u0010=\u001a\u0004\u0018\u00010\u00062\u0006\u0010\u001a\u001a\u00020\u0005J\u0018\u0010>\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u00052\u0006\u0010?\u001a\u00020\bH\u0002J\u0016\u0010@\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u00052\u0006\u0010?\u001a\u00020\bJ\u0010\u0010A\u001a\u00020\u00192\b\u0010B\u001a\u0004\u0018\u00010\u0012J\u0016\u0010C\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u00052\u0006\u0010?\u001a\u00020\bJ\u0016\u0010D\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u00052\u0006\u0010?\u001a\u00020\bJ\u001e\u0010E\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u00052\u0006\u0010F\u001a\u00020\r2\u0006\u0010G\u001a\u00020\rR\u001a\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\b0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\b0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\b0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R&\u0010\u000b\u001a\u001a\u0012\u0004\u0012\u00020\u0005\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\r0\f0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u000e\u001a\n\u0012\u0004\u0012\u00020\u0010\u0018\u00010\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0011\u001a\u0004\u0018\u00010\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000RN\u0010\u0013\u001aB\u0012\f\u0012\n \u0015*\u0004\u0018\u00010\u00050\u0005\u0012\f\u0012\n \u0015*\u0004\u0018\u00010\b0\b \u0015* \u0012\f\u0012\n \u0015*\u0004\u0018\u00010\u00050\u0005\u0012\f\u0012\n \u0015*\u0004\u0018\u00010\b0\b\u0018\u00010\u00140\u0014X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0016\u001a\n\u0012\u0004\u0012\u00020\u0017\u0018\u00010\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006H"}, d2 = {"Lcom/example/castapp/manager/windowsettings/WindowDataModule;", "", "()V", "connectionCastWindows", "Ljava/util/concurrent/ConcurrentHashMap;", "", "Lcom/example/castapp/ui/windowsettings/TransformHandler;", "connectionControlStates", "", "connectionDragSwitchStates", "connectionEditStates", "connectionScreenResolutions", "Lkotlin/Pair;", "", "currentActivityRef", "Ljava/lang/ref/WeakReference;", "Landroid/app/Activity;", "currentWindowManagerDialog", "Lcom/example/castapp/ui/dialog/WindowManagerDialog;", "pendingWindowCreations", "Ljava/util/concurrent/ConcurrentHashMap$KeySetView;", "kotlin.jvm.PlatformType", "surfaceContainerRef", "Landroid/widget/FrameLayout;", "addPendingCreation", "", "connectionId", "addWindowMapping", "transformHandler", "cleanup", "clearAllControlStates", "clearAllDragSwitchStates", "clearAllEditStates", "clearAllPendingCreations", "clearAllScreenResolutions", "clearAllWindowMappings", "clearEditStateFromPreferences", "containsWindow", "getAllConnectionIds", "", "getAllWindowMappings", "", "getControlState", "getCurrentActivity", "getCurrentDialog", "getDragSwitchState", "getEditState", "getScreenResolution", "getSurfaceContainer", "getWindowCount", "getWindowMapping", "initialize", "activity", "container", "isPendingCreation", "loadEditStateFromPreferences", "removeControlState", "removeDragSwitchState", "removeEditState", "removePendingCreation", "removeScreenResolution", "removeWindowMapping", "saveEditStateToPreferences", "isEnabled", "setControlState", "setCurrentDialog", "dialog", "setDragSwitchState", "setEditState", "setScreenResolution", "width", "height", "app_debug"})
public final class WindowDataModule {
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, com.example.castapp.ui.windowsettings.TransformHandler> connectionCastWindows = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, kotlin.Pair<java.lang.Integer, java.lang.Integer>> connectionScreenResolutions = null;
    private final java.util.concurrent.ConcurrentHashMap.KeySetView<java.lang.String, java.lang.Boolean> pendingWindowCreations = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, java.lang.Boolean> connectionControlStates = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, java.lang.Boolean> connectionEditStates = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, java.lang.Boolean> connectionDragSwitchStates = null;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.dialog.WindowManagerDialog currentWindowManagerDialog;
    @org.jetbrains.annotations.Nullable()
    private java.lang.ref.WeakReference<android.app.Activity> currentActivityRef;
    @org.jetbrains.annotations.Nullable()
    private java.lang.ref.WeakReference<android.widget.FrameLayout> surfaceContainerRef;
    
    public WindowDataModule() {
        super();
    }
    
    /**
     * 初始化数据模块
     */
    public final void initialize(@org.jetbrains.annotations.NotNull()
    android.app.Activity activity, @org.jetbrains.annotations.NotNull()
    android.widget.FrameLayout container) {
    }
    
    /**
     * 添加窗口映射
     */
    public final void addWindowMapping(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    com.example.castapp.ui.windowsettings.TransformHandler transformHandler) {
    }
    
    /**
     * 移除窗口映射
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.ui.windowsettings.TransformHandler removeWindowMapping(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
        return null;
    }
    
    /**
     * 获取窗口映射
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.ui.windowsettings.TransformHandler getWindowMapping(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
        return null;
    }
    
    /**
     * 检查窗口是否存在
     */
    public final boolean containsWindow(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
        return false;
    }
    
    /**
     * 获取所有窗口映射
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, com.example.castapp.ui.windowsettings.TransformHandler> getAllWindowMappings() {
        return null;
    }
    
    /**
     * 获取所有连接ID
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Set<java.lang.String> getAllConnectionIds() {
        return null;
    }
    
    /**
     * 获取窗口数量
     */
    public final int getWindowCount() {
        return 0;
    }
    
    /**
     * 清空所有窗口映射
     */
    public final void clearAllWindowMappings() {
    }
    
    /**
     * 设置屏幕分辨率
     */
    public final void setScreenResolution(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, int width, int height) {
    }
    
    /**
     * 获取屏幕分辨率
     */
    @org.jetbrains.annotations.Nullable()
    public final kotlin.Pair<java.lang.Integer, java.lang.Integer> getScreenResolution(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
        return null;
    }
    
    /**
     * 移除屏幕分辨率
     */
    public final void removeScreenResolution(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 清空所有分辨率映射
     */
    public final void clearAllScreenResolutions() {
    }
    
    /**
     * 添加等待创建的连接
     */
    public final void addPendingCreation(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 移除等待创建的连接
     */
    public final boolean removePendingCreation(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
        return false;
    }
    
    /**
     * 检查是否在等待创建
     */
    public final boolean isPendingCreation(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
        return false;
    }
    
    /**
     * 清空所有等待创建
     */
    public final void clearAllPendingCreations() {
    }
    
    /**
     * 设置调控状态
     */
    public final void setControlState(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean isEnabled) {
    }
    
    /**
     * 获取调控状态
     */
    public final boolean getControlState(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
        return false;
    }
    
    /**
     * 移除调控状态
     */
    public final void removeControlState(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 清空所有调控状态
     */
    public final void clearAllControlStates() {
    }
    
    /**
     * 设置编辑状态
     */
    public final void setEditState(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean isEnabled) {
    }
    
    /**
     * 获取编辑状态
     */
    public final boolean getEditState(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
        return false;
    }
    
    /**
     * 移除编辑状态
     */
    public final void removeEditState(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 清空所有编辑状态
     */
    public final void clearAllEditStates() {
    }
    
    /**
     * 设置拖动开关状态（用户在UI中设置的状态）
     */
    public final void setDragSwitchState(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean isEnabled) {
    }
    
    /**
     * 获取拖动开关状态（用户在UI中设置的状态）
     */
    public final boolean getDragSwitchState(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
        return false;
    }
    
    /**
     * 移除拖动开关状态
     */
    public final void removeDragSwitchState(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 清空所有拖动开关状态
     */
    public final void clearAllDragSwitchStates() {
    }
    
    /**
     * 设置当前窗口管理对话框
     */
    public final void setCurrentDialog(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.dialog.WindowManagerDialog dialog) {
    }
    
    /**
     * 获取当前窗口管理对话框
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.ui.dialog.WindowManagerDialog getCurrentDialog() {
        return null;
    }
    
    /**
     * 获取当前Activity
     */
    @org.jetbrains.annotations.Nullable()
    public final android.app.Activity getCurrentActivity() {
        return null;
    }
    
    /**
     * 获取Surface容器
     */
    @org.jetbrains.annotations.Nullable()
    public final android.widget.FrameLayout getSurfaceContainer() {
        return null;
    }
    
    /**
     * 清理所有数据
     */
    public final void cleanup() {
    }
    
    /**
     * 保存编辑状态到SharedPreferences
     */
    private final void saveEditStateToPreferences(java.lang.String connectionId, boolean isEnabled) {
    }
    
    /**
     * 从SharedPreferences加载编辑状态
     */
    private final boolean loadEditStateFromPreferences(java.lang.String connectionId) {
        return false;
    }
    
    /**
     * 从SharedPreferences清除编辑状态
     */
    private final void clearEditStateFromPreferences(java.lang.String connectionId) {
    }
}