package com.example.castapp.ui;

/**
 * 接收端设置对话框
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u009a\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\u000e\n\u0002\b\u0004\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010!\u001a\u00020\"2\u0006\u0010#\u001a\u00020$H\u0002J\b\u0010%\u001a\u00020\u000eH\u0002J\b\u0010&\u001a\u00020$H\u0002J\b\u0010\'\u001a\u00020$H\u0002J\u0010\u0010(\u001a\u00020$2\u0006\u0010)\u001a\u00020\u000eH\u0002J\u0010\u0010*\u001a\u00020$2\u0006\u0010)\u001a\u00020\u000eH\u0002J\u0010\u0010+\u001a\u00020\"2\u0006\u0010,\u001a\u00020-H\u0002J\u0010\u0010.\u001a\u00020\"2\u0006\u0010/\u001a\u000200H\u0002J\b\u00101\u001a\u00020\"H\u0002J\b\u00102\u001a\u00020\"H\u0002J\b\u00103\u001a\u00020\"H\u0002J\u0012\u00104\u001a\u00020\"2\b\u00105\u001a\u0004\u0018\u000106H\u0016J&\u00107\u001a\u0004\u0018\u0001002\u0006\u00108\u001a\u0002092\b\u0010:\u001a\u0004\u0018\u00010;2\b\u00105\u001a\u0004\u0018\u000106H\u0016J\b\u0010<\u001a\u00020\"H\u0016J\u001a\u0010=\u001a\u00020\"2\u0006\u0010/\u001a\u0002002\b\u00105\u001a\u0004\u0018\u000106H\u0016J\b\u0010>\u001a\u00020\"H\u0002J\b\u0010?\u001a\u00020\"H\u0002J\u0010\u0010@\u001a\u00020\"2\u0006\u0010)\u001a\u00020\u000eH\u0002J\u0010\u0010A\u001a\u00020\"2\u0006\u0010B\u001a\u00020$H\u0002J\u0010\u0010C\u001a\u00020\"2\u0006\u0010D\u001a\u00020\u000eH\u0002J\u0010\u0010E\u001a\u00020\"2\u0006\u0010)\u001a\u00020\u000eH\u0002J\u0010\u0010F\u001a\u00020\"2\u0006\u0010B\u001a\u00020$H\u0002J\u0010\u0010G\u001a\u00020\"2\u0006\u0010H\u001a\u00020IH\u0002J\u0010\u0010J\u001a\u00020\"2\u0006\u0010K\u001a\u00020$H\u0002J\b\u0010L\u001a\u00020\"H\u0002J\b\u0010M\u001a\u00020\"H\u0002J\b\u0010N\u001a\u00020\"H\u0002J\b\u0010O\u001a\u00020\"H\u0002J\b\u0010P\u001a\u00020\"H\u0002J\b\u0010Q\u001a\u00020\"H\u0002J\u0010\u0010R\u001a\u00020\"2\u0006\u0010)\u001a\u00020\u000eH\u0002J\u0010\u0010S\u001a\u00020\"2\u0006\u0010H\u001a\u00020TH\u0002J\b\u0010U\u001a\u00020\"H\u0002J\b\u0010V\u001a\u00020\"H\u0002J\u0010\u0010W\u001a\u00020\"2\u0006\u0010K\u001a\u00020$H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0013X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0016\u001a\u0004\u0018\u00010\u0017X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\fX\u0082.\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u0019\u001a\u00020\u001a8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u001d\u0010\u001e\u001a\u0004\b\u001b\u0010\u001cR\u0010\u0010\u001f\u001a\u0004\u0018\u00010 X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006X"}, d2 = {"Lcom/example/castapp/ui/ReceiverDialogFragment;", "Landroidx/fragment/app/DialogFragment;", "()V", "audioManager", "Landroid/media/AudioManager;", "audioOutputModeGroup", "Landroid/widget/RadioGroup;", "audioVideoServerSwitch", "Landroidx/appcompat/widget/SwitchCompat;", "closeButton", "Landroid/widget/ImageButton;", "ipAddressText", "Landroid/widget/TextView;", "isLocalOperation", "", "isUserDragging", "portInput", "Landroid/widget/EditText;", "receiverVolumeSeekBar", "Landroid/widget/SeekBar;", "receiverVolumeText", "remoteControlSwitch", "remoteSettingsReceiver", "Landroid/content/BroadcastReceiver;", "serverStatus", "viewModel", "Lcom/example/castapp/viewmodel/ReceiverViewModel;", "getViewModel", "()Lcom/example/castapp/viewmodel/ReceiverViewModel;", "viewModel$delegate", "Lkotlin/Lazy;", "volumeContentObserver", "Landroid/database/ContentObserver;", "ensureVolumeConsistency", "", "targetVolume", "", "getCurrentAudioOutputMode", "getCurrentSystemVolumePercent", "getCurrentVolumeStream", "getSystemVolumePercentForMode", "isSpeakerMode", "getVolumeStreamForMode", "handleRemoteSettingsChanged", "intent", "Landroid/content/Intent;", "initViews", "view", "Landroid/view/View;", "loadAudioOutputMode", "loadReceiverVolumeAndSync", "observeViewModel", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onCreateView", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "onDestroy", "onViewCreated", "registerRemoteSettingsReceiver", "resetSystemVolumeListener", "saveAudioOutputMode", "saveReceiverVolume", "volume", "sendLocalAudioVideoChangedToRemote", "enabled", "sendLocalPlaybackModeChangedToRemote", "sendLocalVolumeChangedToRemote", "sendMessageToAllRemoteControllers", "message", "Lcom/example/castapp/websocket/ControlMessage;", "setSystemVolume", "volumePercent", "setupAudioVideoServerSwitchListener", "setupClickListeners", "setupOtherClickListeners", "setupRemoteControlSwitchListener", "setupSystemVolumeListener", "setupVolumeControlStream", "setupVolumeControlStreamForMode", "showToast", "", "unregisterRemoteSettingsReceiver", "updatePortInputState", "updateVolumeUI", "app_debug"})
public final class ReceiverDialogFragment extends androidx.fragment.app.DialogFragment {
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy viewModel$delegate = null;
    private android.widget.TextView ipAddressText;
    private android.widget.EditText portInput;
    private androidx.appcompat.widget.SwitchCompat audioVideoServerSwitch;
    private android.widget.TextView serverStatus;
    private android.widget.ImageButton closeButton;
    private android.widget.RadioGroup audioOutputModeGroup;
    private android.widget.SeekBar receiverVolumeSeekBar;
    private android.widget.TextView receiverVolumeText;
    private androidx.appcompat.widget.SwitchCompat remoteControlSwitch;
    private android.media.AudioManager audioManager;
    @org.jetbrains.annotations.Nullable()
    private android.database.ContentObserver volumeContentObserver;
    private boolean isUserDragging = false;
    @org.jetbrains.annotations.Nullable()
    private android.content.BroadcastReceiver remoteSettingsReceiver;
    private boolean isLocalOperation = true;
    
    public ReceiverDialogFragment() {
        super();
    }
    
    private final com.example.castapp.viewmodel.ReceiverViewModel getViewModel() {
        return null;
    }
    
    @java.lang.Override()
    public void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    /**
     * 初始化视图
     */
    private final void initViews(android.view.View view) {
    }
    
    /**
     * 观察ViewModel状态变化
     */
    private final void observeViewModel() {
    }
    
    /**
     * 统一更新端口输入框状态
     * 根据加载状态和服务运行状态决定端口输入框的启用/禁用
     */
    private final void updatePortInputState() {
    }
    
    /**
     * 设置点击监听器
     */
    private final void setupClickListeners() {
    }
    
    /**
     * 设置音视频服务器开关监听器
     */
    private final void setupAudioVideoServerSwitchListener() {
    }
    
    private final void setupOtherClickListeners() {
    }
    
    private final void showToast(java.lang.String message) {
    }
    
    /**
     * 加载音频输出模式设置 - 🔧 修复：确保获取最新的实际状态
     */
    private final void loadAudioOutputMode() {
    }
    
    /**
     * 保存音频输出模式设置
     */
    private final void saveAudioOutputMode(boolean isSpeakerMode) {
    }
    
    /**
     * 加载接收端音量设置并同步系统音量 - 🔥 根本性修复：总是基于当前系统音量
     */
    private final void loadReceiverVolumeAndSync() {
    }
    
    /**
     * 保存接收端音量设置
     */
    private final void saveReceiverVolume(int volume) {
    }
    
    /**
     * 获取当前播放模式对应的音量流 - 🔥 根本性修复：动态音量流控制
     */
    private final int getCurrentVolumeStream() {
        return 0;
    }
    
    /**
     * 根据指定播放模式获取对应的音量流 - 🔥 时序修复：避免SharedPreferences读取时序问题
     */
    private final int getVolumeStreamForMode(boolean isSpeakerMode) {
        return 0;
    }
    
    /**
     * 获取当前音频输出模式
     */
    private final boolean getCurrentAudioOutputMode() {
        return false;
    }
    
    /**
     * 获取当前系统音量百分比 - 🔥 根本性修复：根据播放模式读取对应音量流
     */
    private final int getCurrentSystemVolumePercent() {
        return 0;
    }
    
    /**
     * 根据指定播放模式获取系统音量百分比 - 🔥 时序修复：避免SharedPreferences读取时序问题
     */
    private final int getSystemVolumePercentForMode(boolean isSpeakerMode) {
        return 0;
    }
    
    /**
     * 设置系统音量 - 🔥 根本性修复：根据播放模式设置对应音量流
     */
    private final void setSystemVolume(int volumePercent) {
    }
    
    /**
     * 更新音量UI - 重构版：单一职责，只负责UI更新
     */
    private final void updateVolumeUI(int volumePercent) {
    }
    
    /**
     * 设置系统音量监听 - 重构版：单向数据流的核心，系统音量是唯一数据源
     */
    private final void setupSystemVolumeListener() {
    }
    
    /**
     * 设置音量控制流 - 🔥 根本性修复：根据播放模式设置对应的音量键控制流
     */
    private final void setupVolumeControlStream() {
    }
    
    /**
     * 根据指定播放模式设置音量控制流 - 🔥 时序修复：避免SharedPreferences读取时序问题
     */
    private final void setupVolumeControlStreamForMode(boolean isSpeakerMode) {
    }
    
    /**
     * 确保音量一致性 - 切换播放模式时保持音量不变
     */
    private final void ensureVolumeConsistency(int targetVolume) {
    }
    
    /**
     * 重新设置系统音量监听器 - 🔥 根本性修复：播放模式切换时重新监听对应音量流
     */
    private final void resetSystemVolumeListener() {
    }
    
    @java.lang.Override()
    public void onDestroy() {
    }
    
    /**
     * 设置远程被控开关监听器
     */
    private final void setupRemoteControlSwitchListener() {
    }
    
    /**
     * 注册远程设置变更广播接收器
     */
    private final void registerRemoteSettingsReceiver() {
    }
    
    /**
     * 注销远程设置变更广播接收器
     */
    private final void unregisterRemoteSettingsReceiver() {
    }
    
    /**
     * 处理远程设置变更
     */
    private final void handleRemoteSettingsChanged(android.content.Intent intent) {
    }
    
    /**
     * 发送本地音视频服务状态变更到遥控端
     */
    private final void sendLocalAudioVideoChangedToRemote(boolean enabled) {
    }
    
    /**
     * 发送本地播放模式变更到遥控端
     */
    private final void sendLocalPlaybackModeChangedToRemote(boolean isSpeakerMode) {
    }
    
    /**
     * 发送本地音量变更到遥控端
     */
    private final void sendLocalVolumeChangedToRemote(int volume) {
    }
    
    /**
     * 发送消息到所有连接的遥控端
     */
    private final void sendMessageToAllRemoteControllers(com.example.castapp.websocket.ControlMessage message) {
    }
}