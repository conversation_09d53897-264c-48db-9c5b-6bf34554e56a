package com.example.castapp.ui.windowsettings;

/**
 * 媒体Surface管理器
 * 负责管理视频和图片的显示
 * 视频使用TextureView + MediaPlayer渲染，与投屏窗口保持一致的变换机制
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000d\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\f\u0018\u00002\u00020\u0001B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0006\u0010\u0017\u001a\u00020\u0018J\b\u0010\u0019\u001a\u00020\u0018H\u0002J\b\u0010\u001a\u001a\u00020\u0018H\u0002J\u0018\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u001d\u001a\u00020\b2\u0006\u0010\u001e\u001a\u00020\u001fH\u0002J\b\u0010 \u001a\u0004\u0018\u00010!J\b\u0010\"\u001a\u0004\u0018\u00010\u0014J\b\u0010#\u001a\u00020\u0018H\u0002J\u000e\u0010$\u001a\u00020\u00182\u0006\u0010%\u001a\u00020\u000bJ\u000e\u0010&\u001a\u00020\u00182\u0006\u0010\'\u001a\u00020\u000fJ\u000e\u0010(\u001a\u00020\u00182\u0006\u0010)\u001a\u00020\u000bJ\u001e\u0010*\u001a\u00020\u00182\u0006\u0010+\u001a\u00020\b2\u0006\u0010\u001d\u001a\u00020\b2\u0006\u0010\u001e\u001a\u00020\u001fJ\u001e\u0010,\u001a\u00020\u00182\u0006\u0010+\u001a\u00020\b2\u0006\u0010\u001d\u001a\u00020\b2\u0006\u0010\u001e\u001a\u00020\u001fR\u000e\u0010\u0007\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\f\u001a\u0004\u0018\u00010\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0011\u001a\u0004\u0018\u00010\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0013\u001a\u0004\u0018\u00010\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006-"}, d2 = {"Lcom/example/castapp/ui/windowsettings/MediaSurfaceManager;", "", "context", "Landroid/content/Context;", "container", "Landroid/widget/FrameLayout;", "(Landroid/content/Context;Landroid/widget/FrameLayout;)V", "connectionId", "", "contentType", "currentPlayCount", "", "imageView", "Landroid/widget/ImageView;", "isPlayEnabled", "", "loopCount", "mediaPlayer", "Landroid/media/MediaPlayer;", "textureView", "Landroid/view/TextureView;", "volume", "", "cleanup", "", "clearVideoSettingsFromPreferences", "clearViews", "createVideoSurfaceTextureListener", "Landroid/view/TextureView$SurfaceTextureListener;", "fileName", "uri", "Landroid/net/Uri;", "getCurrentView", "Landroid/view/View;", "getTextureView", "handleVideoCompletion", "setLoopCount", "count", "setPlayEnabled", "enabled", "setVolume", "volumePercent", "setupImageView", "mediaId", "setupVideoView", "app_debug"})
public final class MediaSurfaceManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final android.widget.FrameLayout container = null;
    @org.jetbrains.annotations.Nullable()
    private android.view.TextureView textureView;
    @org.jetbrains.annotations.Nullable()
    private android.widget.ImageView imageView;
    @org.jetbrains.annotations.Nullable()
    private android.media.MediaPlayer mediaPlayer;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String connectionId = "";
    @org.jetbrains.annotations.NotNull()
    private java.lang.String contentType = "";
    private boolean isPlayEnabled = true;
    private int loopCount = -1;
    private int currentPlayCount = 0;
    private float volume = 0.8F;
    
    public MediaSurfaceManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    android.widget.FrameLayout container) {
        super();
    }
    
    /**
     * 为视频设置TextureView（使用MediaPlayer + Surface渲染）
     */
    public final void setupVideoView(@org.jetbrains.annotations.NotNull()
    java.lang.String mediaId, @org.jetbrains.annotations.NotNull()
    java.lang.String fileName, @org.jetbrains.annotations.NotNull()
    android.net.Uri uri) {
    }
    
    /**
     * 创建视频SurfaceTextureListener
     */
    private final android.view.TextureView.SurfaceTextureListener createVideoSurfaceTextureListener(java.lang.String fileName, android.net.Uri uri) {
        return null;
    }
    
    /**
     * 为图片设置ImageView
     */
    public final void setupImageView(@org.jetbrains.annotations.NotNull()
    java.lang.String mediaId, @org.jetbrains.annotations.NotNull()
    java.lang.String fileName, @org.jetbrains.annotations.NotNull()
    android.net.Uri uri) {
    }
    
    /**
     * 获取当前的视图（用于变换操作）
     */
    @org.jetbrains.annotations.Nullable()
    public final android.view.View getCurrentView() {
        return null;
    }
    
    /**
     * 获取TextureView（视频媒体窗口现在使用TextureView）
     */
    @org.jetbrains.annotations.Nullable()
    public final android.view.TextureView getTextureView() {
        return null;
    }
    
    /**
     * 清理资源
     */
    public final void cleanup() {
    }
    
    /**
     * 清除所有视图
     */
    private final void clearViews() {
    }
    
    /**
     * 设置播放开关
     */
    public final void setPlayEnabled(boolean enabled) {
    }
    
    /**
     * 设置循环次数
     * @param count -1表示无限循环，0表示不循环，>0表示循环指定次数
     */
    public final void setLoopCount(int count) {
    }
    
    /**
     * 设置音量
     * @param volumePercent 音量百分比 (0-100)
     */
    public final void setVolume(int volumePercent) {
    }
    
    /**
     * 处理视频播放完成
     */
    private final void handleVideoCompletion() {
    }
    
    /**
     * 🎬 清理SharedPreferences中的视频播放设置
     */
    private final void clearVideoSettingsFromPreferences() {
    }
}