package com.example.castapp.utils;

/**
 * 设备信息工具类
 * 简单获取设备品牌和型号
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0003\u001a\u00020\u0004\u00a8\u0006\u0005"}, d2 = {"Lcom/example/castapp/utils/DeviceUtils;", "", "()V", "getDeviceDisplayName", "", "app_debug"})
public final class DeviceUtils {
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.utils.DeviceUtils INSTANCE = null;
    
    private DeviceUtils() {
        super();
    }
    
    /**
     * 获取设备显示名称（品牌 + 型号）
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDeviceDisplayName() {
        return null;
    }
}