package com.example.castapp.utils;

/**
 * 🎨 自定义字间距样式
 * 用于为文字添加字间距效果，通过调整字符间的间距实现
 * 🎯 修复：改为继承MetricAffectingSpan，避免与其他格式冲突
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0013\u0010\u0005\u001a\u00020\u00062\b\u0010\u0007\u001a\u0004\u0018\u00010\bH\u0096\u0002J\u0006\u0010\t\u001a\u00020\u0003J\b\u0010\n\u001a\u00020\u000bH\u0016J\b\u0010\f\u001a\u00020\rH\u0016J\u0010\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0011H\u0016J\u0010\u0010\u0012\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0011H\u0016R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0013"}, d2 = {"Lcom/example/castapp/utils/LetterSpacingSpan;", "Landroid/text/style/MetricAffectingSpan;", "letterSpacing", "", "(F)V", "equals", "", "other", "", "getLetterSpacing", "hashCode", "", "toString", "", "updateDrawState", "", "paint", "Landroid/text/TextPaint;", "updateMeasureState", "app_debug"})
public final class LetterSpacingSpan extends android.text.style.MetricAffectingSpan {
    private final float letterSpacing = 0.0F;
    
    public LetterSpacingSpan(float letterSpacing) {
        super();
    }
    
    /**
     * 🎯 修复：使用Paint的letterSpacing属性实现字间距
     * 这样可以与其他格式Span协同工作，不会覆盖其他样式
     */
    @java.lang.Override()
    public void updateMeasureState(@org.jetbrains.annotations.NotNull()
    android.text.TextPaint paint) {
    }
    
    @java.lang.Override()
    public void updateDrawState(@org.jetbrains.annotations.NotNull()
    android.text.TextPaint paint) {
    }
    
    /**
     * 获取字间距值
     */
    public final float getLetterSpacing() {
        return 0.0F;
    }
    
    /**
     * 重写equals方法，用于Span比较
     */
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    /**
     * 重写hashCode方法
     */
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    /**
     * 重写toString方法，便于调试
     */
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}