<?xml version="1.0" encoding="utf-8"?>
<!-- 🐾 选中+应用状态：浅灰色背景 + 橙色渐变覆盖层，避免颜色冲突 -->
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 底层：选中状态的浅灰色背景 🐾 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/selected_gray" />
            <corners android:radius="4dp" />
            <stroke android:width="1dp" android:color="@color/selected_gray_dark" />
        </shape>
    </item>

    <!-- 半透明橙色渐变覆盖层（覆盖整个按钮） -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#80FF9800"
                android:endColor="#80FF5722"
                android:angle="0" />
            <corners android:radius="4dp" />
        </shape>
    </item>
</layer-list>
