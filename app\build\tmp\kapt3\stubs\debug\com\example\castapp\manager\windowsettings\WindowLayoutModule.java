package com.example.castapp.manager.windowsettings;

/**
 * 窗口布局模块
 * 负责处理投屏窗口的布局和层级管理
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\b\u001a\u00020\u00072\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nH\u0002J\u0014\u0010\f\u001a\u00020\u00072\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\nJ\u0018\u0010\u000f\u001a\u00020\u00072\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u000bH\u0002J\u0014\u0010\u0013\u001a\u00020\u00072\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nJ\u0018\u0010\u0014\u001a\u00020\u00072\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u000bH\u0002J\u0010\u0010\u0015\u001a\u00020\u00072\u0006\u0010\u0012\u001a\u00020\u000bH\u0002J\u0010\u0010\u0016\u001a\u00020\u00072\u0006\u0010\u0012\u001a\u00020\u000bH\u0002J\u0010\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u001aH\u0002J\u0018\u0010\u001b\u001a\u00020\u00072\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u000bH\u0002J\u0018\u0010\u001c\u001a\u00020\u00072\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u000bH\u0002J\u0018\u0010\u001d\u001a\u00020\u00072\u0006\u0010\u0012\u001a\u00020\u000b2\u0006\u0010\u001e\u001a\u00020\u001fH\u0002J\u0018\u0010 \u001a\u00020\u00072\u0006\u0010\u0012\u001a\u00020\u000b2\u0006\u0010\u001e\u001a\u00020\u001fH\u0002J\u0018\u0010!\u001a\u00020\u00072\u0006\u0010\u0019\u001a\u00020\u001a2\u0006\u0010\"\u001a\u00020\u0018H\u0002J\u0018\u0010#\u001a\u00020\u00072\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u000bH\u0002J\u0014\u0010$\u001a\u00020\u00072\f\u0010%\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006J\u001e\u0010&\u001a\u00020\u00072\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\u0006\u0010\'\u001a\u00020(H\u0002J\u001e\u0010)\u001a\u00020\u00072\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\u0006\u0010\'\u001a\u00020(H\u0002R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006*"}, d2 = {"Lcom/example/castapp/manager/windowsettings/WindowLayoutModule;", "", "dataModule", "Lcom/example/castapp/manager/windowsettings/WindowDataModule;", "(Lcom/example/castapp/manager/windowsettings/WindowDataModule;)V", "dialogRefreshCallback", "Lkotlin/Function0;", "", "adjustLayoutWindowLayers", "layoutItems", "", "Lcom/example/castapp/database/entity/WindowLayoutItemEntity;", "adjustWindowLayers", "orderedWindowList", "Lcom/example/castapp/model/CastWindowInfo;", "applyCropParametersToExistingWindow", "transformHandler", "Lcom/example/castapp/ui/windowsettings/TransformHandler;", "layoutItem", "applyLayoutItemsToExistingWindows", "applyLayoutParametersToExistingWindow", "createMissingMediaWindow", "createMissingTextWindow", "isRealCastWindow", "", "connectionId", "", "refreshTextWindowColor", "refreshTextWindowFormat", "restoreBasicTextFormat", "textFormatManager", "Lcom/example/castapp/utils/TextFormatManager;", "restoreExtendedTextFormat", "restoreLandscapeModeState", "isEnabled", "restoreVideoPlaybackState", "setDialogRefreshCallback", "callback", "syncNotesToSharedPreferences", "activity", "Landroid/app/Activity;", "syncTextFormatsToSharedPreferences", "app_debug"})
public final class WindowLayoutModule {
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.windowsettings.WindowDataModule dataModule = null;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function0<kotlin.Unit> dialogRefreshCallback;
    
    public WindowLayoutModule(@org.jetbrains.annotations.NotNull()
    com.example.castapp.manager.windowsettings.WindowDataModule dataModule) {
        super();
    }
    
    /**
     * 设置对话框刷新回调
     */
    public final void setDialogRefreshCallback(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> callback) {
    }
    
    /**
     * 🏷️ 同步备注信息到SharedPreferences
     * 在应用布局时，将数据库中的备注信息同步到SharedPreferences中
     */
    private final void syncNotesToSharedPreferences(java.util.List<com.example.castapp.database.entity.WindowLayoutItemEntity> layoutItems, android.app.Activity activity) {
    }
    
    /**
     * 📝 同步文本格式信息到TextFormatManager
     * 在应用布局时，将数据库中的文本格式信息同步到TextFormatManager中
     */
    private final void syncTextFormatsToSharedPreferences(java.util.List<com.example.castapp.database.entity.WindowLayoutItemEntity> layoutItems, android.app.Activity activity) {
    }
    
    /**
     * 调整窗口层级
     * 使用 bringToFront() 方法安全地调整显示层级，不破坏 TextureView 的 Surface
     * 层级规则：列表上方的设备显示在最上方，列表下方的设备显示在最下方
     * 注意：由于列表已经按照实际层级排序（顶部=最上层），所以需要反向调整
     */
    public final void adjustWindowLayers(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.castapp.model.CastWindowInfo> orderedWindowList) {
    }
    
    /**
     * 调整布局窗口层级
     * 根据布局项的 orderIndex 调整窗口层级顺序
     */
    private final void adjustLayoutWindowLayers(java.util.List<com.example.castapp.database.entity.WindowLayoutItemEntity> layoutItems) {
    }
    
    /**
     * 应用布局项到现有窗口（直接使用布局项列表）
     */
    public final void applyLayoutItemsToExistingWindows(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.castapp.database.entity.WindowLayoutItemEntity> layoutItems) {
    }
    
    /**
     * 应用布局参数到现有窗口
     */
    private final void applyLayoutParametersToExistingWindow(com.example.castapp.ui.windowsettings.TransformHandler transformHandler, com.example.castapp.database.entity.WindowLayoutItemEntity layoutItem) {
    }
    
    /**
     * 应用裁剪参数到现有窗口
     */
    private final void applyCropParametersToExistingWindow(com.example.castapp.ui.windowsettings.TransformHandler transformHandler, com.example.castapp.database.entity.WindowLayoutItemEntity layoutItem) {
    }
    
    /**
     * 🎬 恢复视频播放状态
     */
    private final void restoreVideoPlaybackState(com.example.castapp.ui.windowsettings.TransformHandler transformHandler, com.example.castapp.database.entity.WindowLayoutItemEntity layoutItem) {
    }
    
    /**
     * 📝 刷新文本窗口格式显示
     * 在布局恢复后，刷新文本窗口的格式显示以应用新的格式信息
     */
    private final void refreshTextWindowFormat(com.example.castapp.ui.windowsettings.TransformHandler transformHandler, com.example.castapp.database.entity.WindowLayoutItemEntity layoutItem) {
    }
    
    /**
     * 📝 恢复扩展文本格式（包含完整的字体格式信息）
     */
    private final void restoreExtendedTextFormat(com.example.castapp.database.entity.WindowLayoutItemEntity layoutItem, com.example.castapp.utils.TextFormatManager textFormatManager) {
    }
    
    /**
     * 📝 恢复基本文本格式（后备方案）
     */
    private final void restoreBasicTextFormat(com.example.castapp.database.entity.WindowLayoutItemEntity layoutItem, com.example.castapp.utils.TextFormatManager textFormatManager) {
    }
    
    /**
     * 🎨 刷新文本窗口背景颜色
     * 在布局恢复后，恢复文本窗口的背景颜色设置
     */
    private final void refreshTextWindowColor(com.example.castapp.ui.windowsettings.TransformHandler transformHandler, com.example.castapp.database.entity.WindowLayoutItemEntity layoutItem) {
    }
    
    /**
     * 📁 创建缺失的媒体窗口
     * 当布局恢复时发现缺失媒体窗口时，根据布局项信息创建新的媒体窗口
     */
    private final void createMissingMediaWindow(com.example.castapp.database.entity.WindowLayoutItemEntity layoutItem) {
    }
    
    /**
     * 📝 创建缺失的文本窗口
     * 当布局恢复时发现缺失文本窗口时，根据布局项信息创建新的文本窗口
     */
    private final void createMissingTextWindow(com.example.castapp.database.entity.WindowLayoutItemEntity layoutItem) {
    }
    
    /**
     * 🎯 判断是否为真实的投屏窗口（非本地窗口）
     */
    private final boolean isRealCastWindow(java.lang.String connectionId) {
        return false;
    }
    
    /**
     * 🎯 恢复横屏模式状态
     * 通过WindowSettingsManager发送横屏控制消息
     */
    private final void restoreLandscapeModeState(java.lang.String connectionId, boolean isEnabled) {
    }
}