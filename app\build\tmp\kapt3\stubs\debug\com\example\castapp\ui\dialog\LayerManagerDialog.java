package com.example.castapp.ui.dialog;

/**
 * 层级管理BottomSheet对话框
 * 专门用于调整投屏窗口的层级顺序
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000l\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\u0018\u00002\u00020\u0001BU\u0012\u0012\u0010\u0002\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00040\u0003\u0012\u001c\b\u0002\u0010\u0006\u001a\u0016\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u0004\u0012\u0004\u0012\u00020\b\u0018\u00010\u0007\u0012\u001c\b\u0002\u0010\t\u001a\u0016\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\b\u0018\u00010\n\u00a2\u0006\u0002\u0010\fJ\u0010\u0010\u001c\u001a\u00020\b2\u0006\u0010\u001d\u001a\u00020\u0014H\u0002J&\u0010\u001e\u001a\u0004\u0018\u00010\u00142\u0006\u0010\u001f\u001a\u00020 2\b\u0010!\u001a\u0004\u0018\u00010\"2\b\u0010#\u001a\u0004\u0018\u00010$H\u0016J\u0010\u0010%\u001a\u00020\b2\u0006\u0010&\u001a\u00020\'H\u0016J\u001a\u0010(\u001a\u00020\b2\u0006\u0010\u001d\u001a\u00020\u00142\b\u0010#\u001a\u0004\u0018\u00010$H\u0016J\u0006\u0010)\u001a\u00020\bJ\b\u0010*\u001a\u00020\bH\u0002J\b\u0010+\u001a\u00020\bH\u0002J\b\u0010,\u001a\u00020\bH\u0002R\u000e\u0010\r\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082.\u00a2\u0006\u0002\n\u0000R\"\u0010\u0015\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0016\u0010\u0017\"\u0004\b\u0018\u0010\u0019R\"\u0010\t\u001a\u0016\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\b\u0018\u00010\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\"\u0010\u0006\u001a\u0016\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u0004\u0012\u0004\u0012\u00020\b\u0018\u00010\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u001bX\u0082.\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0002\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00040\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006-"}, d2 = {"Lcom/example/castapp/ui/dialog/LayerManagerDialog;", "Lcom/google/android/material/bottomsheet/BottomSheetDialogFragment;", "windowInfoProvider", "Lkotlin/Function0;", "", "Lcom/example/castapp/model/CastWindowInfo;", "onWindowOrderChanged", "Lkotlin/Function1;", "", "onNoteChanged", "Lkotlin/Function2;", "", "(Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function2;)V", "adapter", "Lcom/example/castapp/ui/adapter/LayerManagerAdapter;", "btnClose", "Landroid/widget/ImageButton;", "itemTouchHelper", "Landroidx/recyclerview/widget/ItemTouchHelper;", "layoutEmptyState", "Landroid/view/View;", "onDialogDismissed", "getOnDialogDismissed", "()Lkotlin/jvm/functions/Function0;", "setOnDialogDismissed", "(Lkotlin/jvm/functions/Function0;)V", "rvLayerDevices", "Landroidx/recyclerview/widget/RecyclerView;", "initViews", "view", "onCreateView", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "savedInstanceState", "Landroid/os/Bundle;", "onDismiss", "dialog", "Landroid/content/DialogInterface;", "onViewCreated", "refreshDeviceList", "setupClickListeners", "setupDragHandles", "setupRecyclerView", "app_debug"})
public final class LayerManagerDialog extends com.google.android.material.bottomsheet.BottomSheetDialogFragment {
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function0<java.util.List<com.example.castapp.model.CastWindowInfo>> windowInfoProvider = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlin.jvm.functions.Function1<java.util.List<com.example.castapp.model.CastWindowInfo>, kotlin.Unit> onWindowOrderChanged = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlin.jvm.functions.Function2<java.lang.String, java.lang.String, kotlin.Unit> onNoteChanged = null;
    private androidx.recyclerview.widget.RecyclerView rvLayerDevices;
    private android.view.View layoutEmptyState;
    private android.widget.ImageButton btnClose;
    private com.example.castapp.ui.adapter.LayerManagerAdapter adapter;
    private androidx.recyclerview.widget.ItemTouchHelper itemTouchHelper;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function0<kotlin.Unit> onDialogDismissed;
    
    public LayerManagerDialog(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<? extends java.util.List<com.example.castapp.model.CastWindowInfo>> windowInfoProvider, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super java.util.List<com.example.castapp.model.CastWindowInfo>, kotlin.Unit> onWindowOrderChanged, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.String, kotlin.Unit> onNoteChanged) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlin.jvm.functions.Function0<kotlin.Unit> getOnDialogDismissed() {
        return null;
    }
    
    public final void setOnDialogDismissed(@org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> p0) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    /**
     * 初始化视图
     */
    private final void initViews(android.view.View view) {
    }
    
    /**
     * 设置RecyclerView
     */
    private final void setupRecyclerView() {
    }
    
    /**
     * 设置拖动手柄
     */
    private final void setupDragHandles() {
    }
    
    /**
     * 设置点击监听器
     */
    private final void setupClickListeners() {
    }
    
    /**
     * 刷新设备列表
     */
    public final void refreshDeviceList() {
    }
    
    @java.lang.Override()
    public void onDismiss(@org.jetbrains.annotations.NotNull()
    android.content.DialogInterface dialog) {
    }
}