package com.example.castapp.ui.adapter;

/**
 * 布局列表适配器 - 重构版
 * 用于显示已保存的窗口布局列表，支持拖拽排序
 * 采用单一数据源架构，避免数据不一致问题
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000d\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010#\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0014\u0018\u00002\f\u0012\b\u0012\u00060\u0002R\u00020\u00000\u00012\u00020\u0003:\u00044567B\u0005\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\u0015\u001a\u00020\u0016J\u0006\u0010\u0017\u001a\u00020\u0016J\b\u0010\u0018\u001a\u00020\u0019H\u0016J\u0006\u0010\u001a\u001a\u00020\u0019J\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00060\u001cJ\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\t0\u001cJ\u0006\u0010\u001e\u001a\u00020\u000bJ\u001c\u0010\u001f\u001a\u00020\u00162\n\u0010 \u001a\u00060\u0002R\u00020\u00002\u0006\u0010!\u001a\u00020\u0019H\u0016J\u001c\u0010\"\u001a\u00060\u0002R\u00020\u00002\u0006\u0010#\u001a\u00020$2\u0006\u0010%\u001a\u00020\u0019H\u0016J\u0018\u0010&\u001a\u00020\u000b2\u0006\u0010\'\u001a\u00020\u00192\u0006\u0010(\u001a\u00020\u0019H\u0016J\u0018\u0010)\u001a\u00020\u00162\u0006\u0010\'\u001a\u00020\u00192\u0006\u0010(\u001a\u00020\u0019H\u0016J\u000e\u0010*\u001a\u00020\u00162\u0006\u0010+\u001a\u00020\u0006J\u000e\u0010,\u001a\u00020\u00162\u0006\u0010-\u001a\u00020\rJ\u000e\u0010.\u001a\u00020\u00162\u0006\u0010-\u001a\u00020\u000fJ\u000e\u0010/\u001a\u00020\u00162\u0006\u0010-\u001a\u00020\u0011J\u000e\u00100\u001a\u00020\u00162\u0006\u0010+\u001a\u00020\u0006J\u0016\u00101\u001a\u00020\u00162\u000e\u00102\u001a\n\u0012\u0004\u0012\u00020\t\u0018\u00010\u001cJ\u000e\u00103\u001a\u00020\u00162\u0006\u0010+\u001a\u00020\u0006R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\f\u001a\u0004\u0018\u00010\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000e\u001a\u0004\u0018\u00010\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0010\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00060\u0013X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u00068"}, d2 = {"Lcom/example/castapp/ui/adapter/LayoutListAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Lcom/example/castapp/ui/adapter/LayoutListAdapter$LayoutViewHolder;", "Lcom/example/castapp/ui/helper/LayoutItemTouchHelperCallback$ItemTouchHelperAdapter;", "()V", "appliedLayoutId", "", "dataList", "", "Lcom/example/castapp/database/entity/WindowLayoutEntity;", "isSelectionMode", "", "onItemMoveListener", "Lcom/example/castapp/ui/adapter/LayoutListAdapter$OnItemMoveListener;", "onLayoutSelectedListener", "Lcom/example/castapp/ui/adapter/LayoutListAdapter$OnLayoutSelectedListener;", "onSelectionChangedListener", "Lcom/example/castapp/ui/adapter/LayoutListAdapter$OnSelectionChangedListener;", "selectedItems", "", "selectedLayoutId", "enterSelectionMode", "", "exitSelectionMode", "getItemCount", "", "getSelectedCount", "getSelectedLayoutIds", "", "getSelectedLayouts", "isInSelectionMode", "onBindViewHolder", "holder", "position", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "onItemMove", "fromPosition", "toPosition", "onItemMoveCompleted", "setAppliedLayoutId", "layoutId", "setOnItemMoveListener", "listener", "setOnLayoutSelectedListener", "setOnSelectionChangedListener", "setSelectedLayoutId", "submitList", "list", "toggleSelection", "LayoutViewHolder", "OnItemMoveListener", "OnLayoutSelectedListener", "OnSelectionChangedListener", "app_debug"})
public final class LayoutListAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<com.example.castapp.ui.adapter.LayoutListAdapter.LayoutViewHolder> implements com.example.castapp.ui.helper.LayoutItemTouchHelperCallback.ItemTouchHelperAdapter {
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.adapter.LayoutListAdapter.OnLayoutSelectedListener onLayoutSelectedListener;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.adapter.LayoutListAdapter.OnItemMoveListener onItemMoveListener;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.adapter.LayoutListAdapter.OnSelectionChangedListener onSelectionChangedListener;
    private long selectedLayoutId = -1L;
    private long appliedLayoutId = -1L;
    private boolean isSelectionMode = false;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Set<java.lang.Long> selectedItems = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.castapp.database.entity.WindowLayoutEntity> dataList = null;
    
    public LayoutListAdapter() {
        super();
    }
    
    /**
     * 设置布局选择监听器
     */
    public final void setOnLayoutSelectedListener(@org.jetbrains.annotations.NotNull()
    com.example.castapp.ui.adapter.LayoutListAdapter.OnLayoutSelectedListener listener) {
    }
    
    /**
     * 设置拖拽完成监听器
     */
    public final void setOnItemMoveListener(@org.jetbrains.annotations.NotNull()
    com.example.castapp.ui.adapter.LayoutListAdapter.OnItemMoveListener listener) {
    }
    
    /**
     * 设置选择状态变化监听器
     */
    public final void setOnSelectionChangedListener(@org.jetbrains.annotations.NotNull()
    com.example.castapp.ui.adapter.LayoutListAdapter.OnSelectionChangedListener listener) {
    }
    
    /**
     * 设置当前选中的布局ID
     */
    public final void setSelectedLayoutId(long layoutId) {
    }
    
    /**
     * 🐾 设置当前应用的布局ID（新增）
     */
    public final void setAppliedLayoutId(long layoutId) {
    }
    
    /**
     * 进入选择模式
     */
    public final void enterSelectionMode() {
    }
    
    /**
     * 退出选择模式
     */
    public final void exitSelectionMode() {
    }
    
    /**
     * 切换指定布局的选择状态
     */
    public final void toggleSelection(long layoutId) {
    }
    
    /**
     * 获取选中的布局列表
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.castapp.database.entity.WindowLayoutEntity> getSelectedLayouts() {
        return null;
    }
    
    /**
     * 获取选中的布局ID列表
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.Long> getSelectedLayoutIds() {
        return null;
    }
    
    /**
     * 检查是否处于选择模式
     */
    public final boolean isInSelectionMode() {
        return false;
    }
    
    /**
     * 获取选中项数量
     */
    public final int getSelectedCount() {
        return 0;
    }
    
    /**
     * 提交新的数据列表 - 使用DiffUtil优化性能
     */
    public final void submitList(@org.jetbrains.annotations.Nullable()
    java.util.List<com.example.castapp.database.entity.WindowLayoutEntity> list) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.example.castapp.ui.adapter.LayoutListAdapter.LayoutViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    com.example.castapp.ui.adapter.LayoutListAdapter.LayoutViewHolder holder, int position) {
    }
    
    @java.lang.Override()
    public int getItemCount() {
        return 0;
    }
    
    /**
     * 当item位置移动时调用
     */
    @java.lang.Override()
    public boolean onItemMove(int fromPosition, int toPosition) {
        return false;
    }
    
    /**
     * 当拖拽完成时调用，保存最终排序结果
     */
    @java.lang.Override()
    public void onItemMoveCompleted(int fromPosition, int toPosition) {
    }
    
    /**
     * 布局项ViewHolder
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J.\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0012\u001a\u00020\u00102\u0006\u0010\u0013\u001a\u00020\u0010R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0014"}, d2 = {"Lcom/example/castapp/ui/adapter/LayoutListAdapter$LayoutViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "(Lcom/example/castapp/ui/adapter/LayoutListAdapter;Landroid/view/View;)V", "layoutDateText", "Landroid/widget/TextView;", "layoutNameText", "layoutWindowCountText", "selectionCheckBox", "Landroid/widget/CheckBox;", "bind", "", "layout", "Lcom/example/castapp/database/entity/WindowLayoutEntity;", "isSelected", "", "isApplied", "isSelectionMode", "isChecked", "app_debug"})
    public final class LayoutViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView layoutNameText = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView layoutDateText = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView layoutWindowCountText = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.CheckBox selectionCheckBox = null;
        
        public LayoutViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView) {
            super(null);
        }
        
        public final void bind(@org.jetbrains.annotations.NotNull()
        com.example.castapp.database.entity.WindowLayoutEntity layout, boolean isSelected, boolean isApplied, boolean isSelectionMode, boolean isChecked) {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\bf\u0018\u00002\u00020\u0001J&\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00052\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bH&\u00a8\u0006\n"}, d2 = {"Lcom/example/castapp/ui/adapter/LayoutListAdapter$OnItemMoveListener;", "", "onItemMoved", "", "fromPosition", "", "toPosition", "layouts", "", "Lcom/example/castapp/database/entity/WindowLayoutEntity;", "app_debug"})
    public static abstract interface OnItemMoveListener {
        
        public abstract void onItemMoved(int fromPosition, int toPosition, @org.jetbrains.annotations.NotNull()
        java.util.List<com.example.castapp.database.entity.WindowLayoutEntity> layouts);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&\u00a8\u0006\u0006"}, d2 = {"Lcom/example/castapp/ui/adapter/LayoutListAdapter$OnLayoutSelectedListener;", "", "onLayoutSelected", "", "layout", "Lcom/example/castapp/database/entity/WindowLayoutEntity;", "app_debug"})
    public static abstract interface OnLayoutSelectedListener {
        
        public abstract void onLayoutSelected(@org.jetbrains.annotations.NotNull()
        com.example.castapp.database.entity.WindowLayoutEntity layout);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J\u0018\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0005H&\u00a8\u0006\u0007"}, d2 = {"Lcom/example/castapp/ui/adapter/LayoutListAdapter$OnSelectionChangedListener;", "", "onSelectionChanged", "", "selectedCount", "", "totalCount", "app_debug"})
    public static abstract interface OnSelectionChangedListener {
        
        public abstract void onSelectionChanged(int selectedCount, int totalCount);
    }
}