package com.example.castapp.ui.dialog;

/**
 * 编辑布局名称对话框
 * 用于修改已保存布局的名称
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\'\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0010\b\u0002\u0010\u0006\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0007\u00a2\u0006\u0002\u0010\tJ\u0018\u0010\u000e\u001a\u00020\b2\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u0012H\u0002J\u0006\u0010\u0013\u001a\u00020\bR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\n\u001a\u0004\u0018\u00010\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0006\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0014"}, d2 = {"Lcom/example/castapp/ui/dialog/EditLayoutDialog;", "", "context", "Landroid/content/Context;", "layout", "Lcom/example/castapp/database/entity/WindowLayoutEntity;", "onEditSuccess", "Lkotlin/Function0;", "", "(Landroid/content/Context;Lcom/example/castapp/database/entity/WindowLayoutEntity;Lkotlin/jvm/functions/Function0;)V", "dialog", "Landroid/app/AlertDialog;", "layoutManager", "Lcom/example/castapp/manager/LayoutManager;", "handleSaveLayoutName", "layoutNameInput", "Landroid/widget/EditText;", "saveButton", "Landroid/widget/Button;", "show", "app_debug"})
public final class EditLayoutDialog {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.database.entity.WindowLayoutEntity layout = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlin.jvm.functions.Function0<kotlin.Unit> onEditSuccess = null;
    @org.jetbrains.annotations.Nullable()
    private android.app.AlertDialog dialog;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.LayoutManager layoutManager = null;
    
    public EditLayoutDialog(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.example.castapp.database.entity.WindowLayoutEntity layout, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onEditSuccess) {
        super();
    }
    
    /**
     * 显示编辑布局名称对话框
     */
    public final void show() {
    }
    
    /**
     * 处理保存布局名称逻辑
     */
    private final void handleSaveLayoutName(android.widget.EditText layoutNameInput, android.widget.Button saveButton) {
    }
}