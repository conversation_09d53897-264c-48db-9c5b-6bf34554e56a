package com.example.castapp.database.dao;

/**
 * 窗口布局数据访问对象
 * 提供布局和布局项的数据库操作方法
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\\\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\b\bg\u0018\u00002\u00020\u0001:\u000234J\u000e\u0010\u0002\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u0016\u0010\t\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u0016\u0010\r\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u0014\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00100\u000fH\'J\u0010\u0010\u0011\u001a\u0004\u0018\u00010\u0007H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0018\u0010\u0012\u001a\u0004\u0018\u00010\u00072\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u0018\u0010\u0013\u001a\u0004\u0018\u00010\u00072\u0006\u0010\u0014\u001a\u00020\u0015H\u00a7@\u00a2\u0006\u0002\u0010\u0016J\u000e\u0010\u0017\u001a\u00020\u0018H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0019\u001a\u00020\u00182\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u001c\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u001b0\u00102\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u001c\u0010\u001c\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001b0\u00100\u000f2\u0006\u0010\n\u001a\u00020\u000bH\'J\u0018\u0010\u001d\u001a\u0004\u0018\u00010\u001e2\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u0014\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020 0\u0010H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0010\u0010!\u001a\u0004\u0018\u00010\u0018H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\"\u001a\u00020\u000b2\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u0016\u0010#\u001a\u00020\u000b2\u0006\u0010$\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0002\u0010%J\u001c\u0010&\u001a\u00020\u00032\f\u0010\'\u001a\b\u0012\u0004\u0012\u00020\u001b0\u0010H\u00a7@\u00a2\u0006\u0002\u0010(J\u0016\u0010)\u001a\u00020\u00182\u0006\u0010\u0014\u001a\u00020\u0015H\u00a7@\u00a2\u0006\u0002\u0010\u0016J\u0016\u0010*\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u001e\u0010+\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010,\u001a\u00020-H\u00a7@\u00a2\u0006\u0002\u0010.J\u0016\u0010/\u001a\u00020\u00032\u0006\u0010$\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0002\u0010%J\u001e\u00100\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000b2\u0006\u00101\u001a\u00020\u0018H\u00a7@\u00a2\u0006\u0002\u00102\u00a8\u00065"}, d2 = {"Lcom/example/castapp/database/dao/WindowLayoutDao;", "", "clearAllAppliedStatus", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteLayout", "layout", "Lcom/example/castapp/database/entity/WindowLayoutEntity;", "(Lcom/example/castapp/database/entity/WindowLayoutEntity;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteLayoutById", "layoutId", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteLayoutItemsByLayoutId", "getAllLayouts", "Lkotlinx/coroutines/flow/Flow;", "", "getCurrentAppliedLayout", "getLayoutById", "getLayoutByName", "name", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getLayoutCount", "", "getLayoutItemCount", "getLayoutItemsByLayoutId", "Lcom/example/castapp/database/entity/WindowLayoutItemEntity;", "getLayoutItemsByLayoutIdFlow", "getLayoutWithItems", "Lcom/example/castapp/database/dao/WindowLayoutDao$LayoutWithItems;", "getLayoutsWithItemCount", "Lcom/example/castapp/database/dao/WindowLayoutDao$LayoutWithItemCount;", "getMaxSortOrder", "insertLayout", "insertLayoutItem", "item", "(Lcom/example/castapp/database/entity/WindowLayoutItemEntity;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertLayoutItems", "items", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "isLayoutNameExists", "updateLayout", "updateLayoutAppliedStatus", "isApplied", "", "(JZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateLayoutItem", "updateLayoutSortOrder", "sortOrder", "(JILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "LayoutWithItemCount", "LayoutWithItems", "app_debug"})
@androidx.room.Dao()
public abstract interface WindowLayoutDao {
    
    /**
     * 插入新的布局
     * @return 新插入布局的ID
     */
    @androidx.room.Insert()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertLayout(@org.jetbrains.annotations.NotNull()
    com.example.castapp.database.entity.WindowLayoutEntity layout, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    /**
     * 更新布局信息
     */
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateLayout(@org.jetbrains.annotations.NotNull()
    com.example.castapp.database.entity.WindowLayoutEntity layout, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 删除指定布局（会级联删除相关的布局项）
     */
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteLayout(@org.jetbrains.annotations.NotNull()
    com.example.castapp.database.entity.WindowLayoutEntity layout, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 根据ID删除布局
     */
    @androidx.room.Query(value = "DELETE FROM window_layouts WHERE id = :layoutId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteLayoutById(long layoutId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 获取所有布局，按排序顺序排列，然后按创建时间倒序排列
     */
    @androidx.room.Query(value = "SELECT * FROM window_layouts ORDER BY sortOrder ASC, createdAt DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.castapp.database.entity.WindowLayoutEntity>> getAllLayouts();
    
    /**
     * 根据ID获取布局
     */
    @androidx.room.Query(value = "SELECT * FROM window_layouts WHERE id = :layoutId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getLayoutById(long layoutId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.castapp.database.entity.WindowLayoutEntity> $completion);
    
    /**
     * 根据名称查找布局
     */
    @androidx.room.Query(value = "SELECT * FROM window_layouts WHERE layoutName = :name")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getLayoutByName(@org.jetbrains.annotations.NotNull()
    java.lang.String name, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.castapp.database.entity.WindowLayoutEntity> $completion);
    
    /**
     * 检查布局名称是否已存在
     */
    @androidx.room.Query(value = "SELECT COUNT(*) FROM window_layouts WHERE layoutName = :name")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object isLayoutNameExists(@org.jetbrains.annotations.NotNull()
    java.lang.String name, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * 更新布局的排序顺序
     */
    @androidx.room.Query(value = "UPDATE window_layouts SET sortOrder = :sortOrder WHERE id = :layoutId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateLayoutSortOrder(long layoutId, int sortOrder, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 获取最大的排序顺序值
     */
    @androidx.room.Query(value = "SELECT MAX(sortOrder) FROM window_layouts")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getMaxSortOrder(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * 🐾 设置布局的应用状态
     */
    @androidx.room.Query(value = "UPDATE window_layouts SET isApplied = :isApplied WHERE id = :layoutId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateLayoutAppliedStatus(long layoutId, boolean isApplied, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 🐾 清除所有布局的应用状态
     */
    @androidx.room.Query(value = "UPDATE window_layouts SET isApplied = 0")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object clearAllAppliedStatus(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 🐾 获取当前应用的布局
     */
    @androidx.room.Query(value = "SELECT * FROM window_layouts WHERE isApplied = 1 LIMIT 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getCurrentAppliedLayout(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.castapp.database.entity.WindowLayoutEntity> $completion);
    
    /**
     * 插入布局项列表
     */
    @androidx.room.Insert()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertLayoutItems(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.castapp.database.entity.WindowLayoutItemEntity> items, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 插入单个布局项
     */
    @androidx.room.Insert()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertLayoutItem(@org.jetbrains.annotations.NotNull()
    com.example.castapp.database.entity.WindowLayoutItemEntity item, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    /**
     * 更新布局项
     */
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateLayoutItem(@org.jetbrains.annotations.NotNull()
    com.example.castapp.database.entity.WindowLayoutItemEntity item, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 删除指定布局的所有布局项
     */
    @androidx.room.Query(value = "DELETE FROM window_layout_items WHERE layoutId = :layoutId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteLayoutItemsByLayoutId(long layoutId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 获取指定布局的所有布局项，按顺序排列
     */
    @androidx.room.Query(value = "SELECT * FROM window_layout_items WHERE layoutId = :layoutId ORDER BY orderIndex ASC")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getLayoutItemsByLayoutId(long layoutId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.castapp.database.entity.WindowLayoutItemEntity>> $completion);
    
    /**
     * 获取指定布局的所有布局项，按顺序排列（Flow版本）
     */
    @androidx.room.Query(value = "SELECT * FROM window_layout_items WHERE layoutId = :layoutId ORDER BY orderIndex ASC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.castapp.database.entity.WindowLayoutItemEntity>> getLayoutItemsByLayoutIdFlow(long layoutId);
    
    /**
     * 获取布局及其包含的所有窗口项
     */
    @androidx.room.Transaction()
    @androidx.room.Query(value = "SELECT * FROM window_layouts WHERE id = :layoutId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getLayoutWithItems(long layoutId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.castapp.database.dao.WindowLayoutDao.LayoutWithItems> $completion);
    
    /**
     * 获取所有布局及其包含的窗口项数量统计
     */
    @androidx.room.Query(value = "\n        SELECT l.*, COUNT(i.id) as actualWindowCount \n        FROM window_layouts l \n        LEFT JOIN window_layout_items i ON l.id = i.layoutId \n        GROUP BY l.id \n        ORDER BY l.createdAt DESC\n    ")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getLayoutsWithItemCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.castapp.database.dao.WindowLayoutDao.LayoutWithItemCount>> $completion);
    
    /**
     * 获取布局总数
     */
    @androidx.room.Query(value = "SELECT COUNT(*) FROM window_layouts")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getLayoutCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * 获取指定布局的窗口项数量
     */
    @androidx.room.Query(value = "SELECT COUNT(*) FROM window_layout_items WHERE layoutId = :layoutId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getLayoutItemCount(long layoutId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * 布局及其窗口项数量统计
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\t\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\t\u0010\u000b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\f\u001a\u00020\u0005H\u00c6\u0003J\u001d\u0010\r\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u000e\u001a\u00020\u000f2\b\u0010\u0010\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0011\u001a\u00020\u0005H\u00d6\u0001J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\n\u00a8\u0006\u0014"}, d2 = {"Lcom/example/castapp/database/dao/WindowLayoutDao$LayoutWithItemCount;", "", "layout", "Lcom/example/castapp/database/entity/WindowLayoutEntity;", "actualWindowCount", "", "(Lcom/example/castapp/database/entity/WindowLayoutEntity;I)V", "getActualWindowCount", "()I", "getLayout", "()Lcom/example/castapp/database/entity/WindowLayoutEntity;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "toString", "", "app_debug"})
    public static final class LayoutWithItemCount {
        @androidx.room.Embedded()
        @org.jetbrains.annotations.NotNull()
        private final com.example.castapp.database.entity.WindowLayoutEntity layout = null;
        private final int actualWindowCount = 0;
        
        public LayoutWithItemCount(@org.jetbrains.annotations.NotNull()
        com.example.castapp.database.entity.WindowLayoutEntity layout, int actualWindowCount) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.database.entity.WindowLayoutEntity getLayout() {
            return null;
        }
        
        public final int getActualWindowCount() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.database.entity.WindowLayoutEntity component1() {
            return null;
        }
        
        public final int component2() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.database.dao.WindowLayoutDao.LayoutWithItemCount copy(@org.jetbrains.annotations.NotNull()
        com.example.castapp.database.entity.WindowLayoutEntity layout, int actualWindowCount) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    /**
     * 布局及其包含的窗口项
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u001b\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a2\u0006\u0002\u0010\u0007J\t\u0010\f\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J#\u0010\u000e\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0001J\u0013\u0010\u000f\u001a\u00020\u00102\b\u0010\u0011\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0015H\u00d6\u0001R\u001c\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00058\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u0016"}, d2 = {"Lcom/example/castapp/database/dao/WindowLayoutDao$LayoutWithItems;", "", "layout", "Lcom/example/castapp/database/entity/WindowLayoutEntity;", "items", "", "Lcom/example/castapp/database/entity/WindowLayoutItemEntity;", "(Lcom/example/castapp/database/entity/WindowLayoutEntity;Ljava/util/List;)V", "getItems", "()Ljava/util/List;", "getLayout", "()Lcom/example/castapp/database/entity/WindowLayoutEntity;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "", "app_debug"})
    public static final class LayoutWithItems {
        @androidx.room.Embedded()
        @org.jetbrains.annotations.NotNull()
        private final com.example.castapp.database.entity.WindowLayoutEntity layout = null;
        @androidx.room.Relation(parentColumn = "id", entityColumn = "layoutId")
        @org.jetbrains.annotations.NotNull()
        private final java.util.List<com.example.castapp.database.entity.WindowLayoutItemEntity> items = null;
        
        public LayoutWithItems(@org.jetbrains.annotations.NotNull()
        com.example.castapp.database.entity.WindowLayoutEntity layout, @org.jetbrains.annotations.NotNull()
        java.util.List<com.example.castapp.database.entity.WindowLayoutItemEntity> items) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.database.entity.WindowLayoutEntity getLayout() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<com.example.castapp.database.entity.WindowLayoutItemEntity> getItems() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.database.entity.WindowLayoutEntity component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<com.example.castapp.database.entity.WindowLayoutItemEntity> component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.database.dao.WindowLayoutDao.LayoutWithItems copy(@org.jetbrains.annotations.NotNull()
        com.example.castapp.database.entity.WindowLayoutEntity layout, @org.jetbrains.annotations.NotNull()
        java.util.List<com.example.castapp.database.entity.WindowLayoutItemEntity> items) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}