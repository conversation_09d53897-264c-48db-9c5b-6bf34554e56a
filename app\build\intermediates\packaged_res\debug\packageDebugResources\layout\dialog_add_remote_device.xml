<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="20dp"
    android:background="@android:color/white">

    <!-- 标题 -->
    <TextView
        android:id="@+id/dialog_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="添加设备"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="#333333"
        android:gravity="center"
        android:layout_marginBottom="20dp" />

    <!-- 设备名称输入区域 -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="设备名称"
        android:layout_marginBottom="12dp"
        app:boxBackgroundMode="outline"
        app:boxCornerRadiusTopStart="8dp"
        app:boxCornerRadiusTopEnd="8dp"
        app:boxCornerRadiusBottomStart="8dp"
        app:boxCornerRadiusBottomEnd="8dp"
        app:boxStrokeColor="#2196F3"
        app:hintTextColor="#666666"
        app:startIconDrawable="@drawable/ic_cast"
        app:startIconTint="#666666"
        app:placeholderText="例如: 客厅设备"
        app:placeholderTextColor="#AAAAAA">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/device_name_input"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:textSize="16sp"
            android:textColor="#333333"
            android:maxLines="1"
            android:imeOptions="actionNext" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- IP地址输入区域 -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="IP地址"
        android:layout_marginBottom="12dp"
        app:boxBackgroundMode="outline"
        app:boxCornerRadiusTopStart="8dp"
        app:boxCornerRadiusTopEnd="8dp"
        app:boxCornerRadiusBottomStart="8dp"
        app:boxCornerRadiusBottomEnd="8dp"
        app:boxStrokeColor="#2196F3"
        app:hintTextColor="#666666"
        app:startIconDrawable="@drawable/ic_remote_control"
        app:startIconTint="#666666"
        app:placeholderText="例如: *************"
        app:placeholderTextColor="#AAAAAA">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/ip_address_input"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:textSize="16sp"
            android:textColor="#333333"
            android:maxLines="1"
            android:imeOptions="actionDone" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- 端口信息显示 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp"
        android:padding="12dp"
        android:background="@drawable/rounded_background"
        android:backgroundTint="#E8F5E8">

        <ImageView
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/ic_notification"
            android:tint="#4CAF50"
            android:layout_marginEnd="8dp" />

        <TextView
            android:id="@+id/port_info_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="固定使用端口"
            android:textSize="12sp"
            android:textColor="#4CAF50"
            android:lineSpacingExtra="2dp" />

    </LinearLayout>

    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">

        <Button
            android:id="@+id/cancel_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="取消"
            android:textColor="#666666"
            android:background="?android:attr/selectableItemBackground"
            android:layout_marginEnd="12dp"
            android:minWidth="80dp" />

        <Button
            android:id="@+id/confirm_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="添加"
            android:textColor="@android:color/white"
            android:backgroundTint="@color/primary_blue"
            android:minWidth="80dp" />

    </LinearLayout>

</LinearLayout>
