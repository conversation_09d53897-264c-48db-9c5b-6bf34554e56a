<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/dialog_background"
    android:padding="16dp"
    android:elevation="8dp">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/dialog_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="🎛️ 远程接收端设置控制"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary" />

        <ImageButton
            android:id="@+id/close_dialog_button"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_close"
            android:contentDescription="关闭窗口"
            android:scaleType="centerInside"
            android:alpha="0.8"/>
    </LinearLayout>

    <!-- 连接状态显示 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@drawable/info_card_background"
        android:padding="12dp"
        android:layout_marginBottom="16dp"
        android:gravity="center_vertical">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="📡 连接状态"
            android:textStyle="bold"
            android:textSize="14sp"
            android:textColor="@color/text_primary" />

        <TextView
            android:id="@+id/connection_status_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="已连接"
            android:textSize="14sp"
            android:textColor="#4CAF50"
            android:textStyle="bold" />

    </LinearLayout>

    <!-- 音视频服务控制卡片 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/info_card_background"
        android:padding="12dp"
        android:layout_marginBottom="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="📺 音视频服务"
                android:textStyle="bold"
                android:textSize="14sp"
                android:textColor="@color/text_primary" />

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/remote_audio_video_switch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="false"
                android:thumbTint="@color/white"
                android:trackTint="@color/primary_blue" />

        </LinearLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="远程控制接收端的音视频服务开关"
            android:textSize="12sp"
            android:textColor="@color/text_secondary"
            android:layout_marginTop="4dp" />

    </LinearLayout>

    <!-- 播放模式控制卡片 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/info_card_background"
        android:padding="12dp"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="🔊 播放模式"
            android:textStyle="bold"
            android:textSize="14sp"
            android:textColor="@color/text_primary"
            android:layout_marginBottom="8dp" />

        <RadioGroup
            android:id="@+id/remote_audio_output_mode_group"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <RadioButton
                android:id="@+id/remote_speaker_mode_radio"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="🔊 扬声器"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:checked="true" />

            <RadioButton
                android:id="@+id/remote_earpiece_mode_radio"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="📞 听筒"
                android:textSize="14sp"
                android:textColor="@color/text_secondary" />

        </RadioGroup>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="远程切换接收端的音频播放模式"
            android:textSize="12sp"
            android:textColor="@color/text_secondary"
            android:layout_marginTop="4dp" />

    </LinearLayout>

    <!-- 音量控制卡片 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/info_card_background"
        android:padding="12dp"
        android:layout_marginBottom="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="🔊 音量控制"
                android:textStyle="bold"
                android:textSize="14sp"
                android:textColor="@color/text_primary" />

            <TextView
                android:id="@+id/remote_volume_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="80%"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:minWidth="40dp"
                android:gravity="end" />

        </LinearLayout>

        <SeekBar
            android:id="@+id/remote_volume_seekbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:max="100"
            android:progress="80"
            android:progressTint="@color/primary_blue"
            android:thumbTint="@color/primary_blue" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="远程调整接收端的音量大小"
            android:textSize="12sp"
            android:textColor="@color/text_secondary"
            android:layout_marginTop="4dp" />

    </LinearLayout>

    <!-- 实时控制提示 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@drawable/info_card_background"
        android:padding="8dp"
        android:gravity="center">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="⚡ 实时控制模式：设置变更将立即同步到接收端"
            android:textSize="12sp"
            android:textColor="@color/text_secondary"
            android:gravity="center" />

    </LinearLayout>

</LinearLayout>
