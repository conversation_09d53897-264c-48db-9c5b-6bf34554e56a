package com.example.castapp.ui.fragment;

/**
 * 接收端标签页Fragment
 * 显示和管理远程接收端设备列表
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000e\n\u0002\u0010 \n\u0002\b\u0002\u0018\u0000 12\u00020\u0001:\u00011B\u0005\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0016\u001a\u00020\u00072\u0006\u0010\u0017\u001a\u00020\nJ\u0010\u0010\u0018\u001a\u00020\u00072\u0006\u0010\u0019\u001a\u00020\u001aH\u0002J&\u0010\u001b\u001a\u0004\u0018\u00010\u001a2\u0006\u0010\u001c\u001a\u00020\u001d2\b\u0010\u001e\u001a\u0004\u0018\u00010\u001f2\b\u0010 \u001a\u0004\u0018\u00010!H\u0016J\u001a\u0010\"\u001a\u00020\u00072\u0006\u0010\u0019\u001a\u00020\u001a2\b\u0010 \u001a\u0004\u0018\u00010!H\u0016J\u000e\u0010#\u001a\u00020\u00072\u0006\u0010\u0017\u001a\u00020\nJ\u0014\u0010$\u001a\u00020\u00072\f\u0010%\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006J\u001a\u0010&\u001a\u00020\u00072\u0012\u0010%\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00070\tJ\u001a\u0010\'\u001a\u00020\u00072\u0012\u0010%\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00070\tJ\u001a\u0010(\u001a\u00020\u00072\u0012\u0010%\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00070\tJ\u001a\u0010)\u001a\u00020\u00072\u0012\u0010%\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00070\tJ\b\u0010*\u001a\u00020\u0007H\u0002J\b\u0010+\u001a\u00020\u0007H\u0002J\u000e\u0010,\u001a\u00020\u00072\u0006\u0010\u0017\u001a\u00020\nJ\b\u0010-\u001a\u00020\u0007H\u0002J\u0014\u0010.\u001a\u00020\u00072\f\u0010/\u001a\b\u0012\u0004\u0012\u00020\n00R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\b\u001a\u0010\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u000b\u001a\u0010\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\f\u001a\u0010\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\r\u001a\u0010\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0013X\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\n0\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00062"}, d2 = {"Lcom/example/castapp/ui/fragment/RemoteReceiverTabFragment;", "Landroidx/fragment/app/Fragment;", "()V", "addReceiverConnectionButton", "Landroid/widget/ImageButton;", "onAddReceiverClickListener", "Lkotlin/Function0;", "", "onConnectClickListener", "Lkotlin/Function1;", "Lcom/example/castapp/model/RemoteReceiverConnection;", "onControlClickListener", "onDeleteClickListener", "onEditClickListener", "receiverAdapter", "Lcom/example/castapp/ui/adapter/RemoteReceiverDeviceAdapter;", "receiverConnectionCountText", "Landroid/widget/TextView;", "receiverConnectionsRecyclerView", "Landroidx/recyclerview/widget/RecyclerView;", "receivers", "", "addReceiver", "receiver", "initViews", "view", "Landroid/view/View;", "onCreateView", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "savedInstanceState", "Landroid/os/Bundle;", "onViewCreated", "removeReceiver", "setOnAddReceiverClickListener", "listener", "setOnConnectClickListener", "setOnControlClickListener", "setOnDeleteClickListener", "setOnEditClickListener", "setupClickListeners", "setupRecyclerView", "updateReceiver", "updateReceiverCount", "updateReceivers", "newReceivers", "", "Companion", "app_debug"})
public final class RemoteReceiverTabFragment extends androidx.fragment.app.Fragment {
    private android.widget.TextView receiverConnectionCountText;
    private android.widget.ImageButton addReceiverConnectionButton;
    private androidx.recyclerview.widget.RecyclerView receiverConnectionsRecyclerView;
    private com.example.castapp.ui.adapter.RemoteReceiverDeviceAdapter receiverAdapter;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.castapp.model.RemoteReceiverConnection> receivers = null;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super com.example.castapp.model.RemoteReceiverConnection, kotlin.Unit> onConnectClickListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super com.example.castapp.model.RemoteReceiverConnection, kotlin.Unit> onControlClickListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super com.example.castapp.model.RemoteReceiverConnection, kotlin.Unit> onEditClickListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super com.example.castapp.model.RemoteReceiverConnection, kotlin.Unit> onDeleteClickListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function0<kotlin.Unit> onAddReceiverClickListener;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.ui.fragment.RemoteReceiverTabFragment.Companion Companion = null;
    
    public RemoteReceiverTabFragment() {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void initViews(android.view.View view) {
    }
    
    private final void setupRecyclerView() {
    }
    
    private final void setupClickListeners() {
    }
    
    /**
     * 设置连接点击监听器
     */
    public final void setOnConnectClickListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.castapp.model.RemoteReceiverConnection, kotlin.Unit> listener) {
    }
    
    /**
     * 设置控制点击监听器
     */
    public final void setOnControlClickListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.castapp.model.RemoteReceiverConnection, kotlin.Unit> listener) {
    }
    
    /**
     * 设置编辑点击监听器
     */
    public final void setOnEditClickListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.castapp.model.RemoteReceiverConnection, kotlin.Unit> listener) {
    }
    
    /**
     * 设置删除点击监听器
     */
    public final void setOnDeleteClickListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.castapp.model.RemoteReceiverConnection, kotlin.Unit> listener) {
    }
    
    /**
     * 设置添加接收端点击监听器
     */
    public final void setOnAddReceiverClickListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> listener) {
    }
    
    /**
     * 更新接收端列表
     */
    public final void updateReceivers(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.castapp.model.RemoteReceiverConnection> newReceivers) {
    }
    
    /**
     * 添加接收端
     */
    public final void addReceiver(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.RemoteReceiverConnection receiver) {
    }
    
    /**
     * 更新接收端状态
     */
    public final void updateReceiver(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.RemoteReceiverConnection receiver) {
    }
    
    /**
     * 删除接收端
     */
    public final void removeReceiver(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.RemoteReceiverConnection receiver) {
    }
    
    /**
     * 更新接收端数量显示
     */
    private final void updateReceiverCount() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0003\u001a\u00020\u0004\u00a8\u0006\u0005"}, d2 = {"Lcom/example/castapp/ui/fragment/RemoteReceiverTabFragment$Companion;", "", "()V", "newInstance", "Lcom/example/castapp/ui/fragment/RemoteReceiverTabFragment;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.ui.fragment.RemoteReceiverTabFragment newInstance() {
            return null;
        }
    }
}