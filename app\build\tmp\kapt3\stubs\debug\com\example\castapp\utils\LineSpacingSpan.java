package com.example.castapp.utils;

/**
 * 🎨 自定义行间距样式
 * 用于为文字添加行间距效果，通过调整行高实现
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\r\n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\u0019\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0002\u0010\u0006J<\u0010\u0007\u001a\u00020\b2\b\u0010\t\u001a\u0004\u0018\u00010\n2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\f2\u0006\u0010\u000e\u001a\u00020\f2\u0006\u0010\u000f\u001a\u00020\f2\b\u0010\u0010\u001a\u0004\u0018\u00010\u0011H\u0016J\u0006\u0010\u0012\u001a\u00020\u0003R\u0010\u0010\u0004\u001a\u0004\u0018\u00010\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0013"}, d2 = {"Lcom/example/castapp/utils/LineSpacingSpan;", "Landroid/text/style/LineHeightSpan;", "lineSpacingExtra", "", "context", "Landroid/content/Context;", "(FLandroid/content/Context;)V", "chooseHeight", "", "text", "", "start", "", "end", "spanstartv", "v", "fm", "Landroid/graphics/Paint$FontMetricsInt;", "getLineSpacing", "app_debug"})
public final class LineSpacingSpan implements android.text.style.LineHeightSpan {
    private final float lineSpacingExtra = 0.0F;
    @org.jetbrains.annotations.Nullable()
    private final android.content.Context context = null;
    
    public LineSpacingSpan(float lineSpacingExtra, @org.jetbrains.annotations.Nullable()
    android.content.Context context) {
        super();
    }
    
    @java.lang.Override()
    public void chooseHeight(@org.jetbrains.annotations.Nullable()
    java.lang.CharSequence text, int start, int end, int spanstartv, int v, @org.jetbrains.annotations.Nullable()
    android.graphics.Paint.FontMetricsInt fm) {
    }
    
    /**
     * 获取行间距值
     */
    public final float getLineSpacing() {
        return 0.0F;
    }
}