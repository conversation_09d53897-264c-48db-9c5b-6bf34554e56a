package com.example.castapp.ui.dialog;

/**
 * 保存布局对话框
 * 用于输入布局名称并保存当前窗口布局
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\u0018\u00002\u00020\u0001B-\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012\u0010\b\u0002\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\t\u0018\u00010\b\u00a2\u0006\u0002\u0010\nJ\u0006\u0010\u000f\u001a\u00020\tJ\u0010\u0010\u0010\u001a\u00020\t2\u0006\u0010\u0011\u001a\u00020\u0012H\u0002J\u0010\u0010\u0013\u001a\u00020\t2\u0006\u0010\u0011\u001a\u00020\u0012H\u0002J\u0006\u0010\u0014\u001a\u00020\tR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000b\u001a\u0004\u0018\u00010\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\t\u0018\u00010\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0015"}, d2 = {"Lcom/example/castapp/ui/dialog/SaveLayoutDialog;", "", "context", "Landroid/content/Context;", "windowInfoList", "", "Lcom/example/castapp/model/CastWindowInfo;", "onSaveSuccess", "Lkotlin/Function0;", "", "(Landroid/content/Context;Ljava/util/List;Lkotlin/jvm/functions/Function0;)V", "dialog", "Landroid/app/AlertDialog;", "layoutManager", "Lcom/example/castapp/manager/LayoutManager;", "dismiss", "handleSaveLayout", "layoutNameInput", "Landroid/widget/EditText;", "setupDialogButtons", "show", "app_debug"})
public final class SaveLayoutDialog {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.castapp.model.CastWindowInfo> windowInfoList = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlin.jvm.functions.Function0<kotlin.Unit> onSaveSuccess = null;
    @org.jetbrains.annotations.Nullable()
    private android.app.AlertDialog dialog;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.LayoutManager layoutManager = null;
    
    public SaveLayoutDialog(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.castapp.model.CastWindowInfo> windowInfoList, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onSaveSuccess) {
        super();
    }
    
    /**
     * 显示保存布局对话框
     */
    public final void show() {
    }
    
    /**
     * 设置对话框按钮
     */
    private final void setupDialogButtons(android.widget.EditText layoutNameInput) {
    }
    
    /**
     * 处理保存布局逻辑
     */
    private final void handleSaveLayout(android.widget.EditText layoutNameInput) {
    }
    
    /**
     * 关闭对话框
     */
    public final void dismiss() {
    }
}