package com.example.castapp.database.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.LongSparseArray;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.room.util.RelationUtil;
import androidx.room.util.StringUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.castapp.database.converter.DateConverter;
import com.example.castapp.database.entity.WindowLayoutEntity;
import com.example.castapp.database.entity.WindowLayoutItemEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.StringBuilder;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class WindowLayoutDao_Impl implements WindowLayoutDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<WindowLayoutEntity> __insertionAdapterOfWindowLayoutEntity;

  private final DateConverter __dateConverter = new DateConverter();

  private final EntityInsertionAdapter<WindowLayoutItemEntity> __insertionAdapterOfWindowLayoutItemEntity;

  private final EntityDeletionOrUpdateAdapter<WindowLayoutEntity> __deletionAdapterOfWindowLayoutEntity;

  private final EntityDeletionOrUpdateAdapter<WindowLayoutEntity> __updateAdapterOfWindowLayoutEntity;

  private final EntityDeletionOrUpdateAdapter<WindowLayoutItemEntity> __updateAdapterOfWindowLayoutItemEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteLayoutById;

  private final SharedSQLiteStatement __preparedStmtOfUpdateLayoutSortOrder;

  private final SharedSQLiteStatement __preparedStmtOfUpdateLayoutAppliedStatus;

  private final SharedSQLiteStatement __preparedStmtOfClearAllAppliedStatus;

  private final SharedSQLiteStatement __preparedStmtOfDeleteLayoutItemsByLayoutId;

  public WindowLayoutDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfWindowLayoutEntity = new EntityInsertionAdapter<WindowLayoutEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR ABORT INTO `window_layouts` (`id`,`layoutName`,`description`,`createdAt`,`updatedAt`,`windowCount`,`sortOrder`,`isApplied`) VALUES (nullif(?, 0),?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final WindowLayoutEntity entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getLayoutName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getLayoutName());
        }
        if (entity.getDescription() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getDescription());
        }
        final Long _tmp = __dateConverter.dateToTimestamp(entity.getCreatedAt());
        if (_tmp == null) {
          statement.bindNull(4);
        } else {
          statement.bindLong(4, _tmp);
        }
        final Long _tmp_1 = __dateConverter.dateToTimestamp(entity.getUpdatedAt());
        if (_tmp_1 == null) {
          statement.bindNull(5);
        } else {
          statement.bindLong(5, _tmp_1);
        }
        statement.bindLong(6, entity.getWindowCount());
        statement.bindLong(7, entity.getSortOrder());
        final int _tmp_2 = entity.isApplied() ? 1 : 0;
        statement.bindLong(8, _tmp_2);
      }
    };
    this.__insertionAdapterOfWindowLayoutItemEntity = new EntityInsertionAdapter<WindowLayoutItemEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR ABORT INTO `window_layout_items` (`id`,`layoutId`,`orderIndex`,`deviceName`,`deviceId`,`ipAddress`,`port`,`positionX`,`positionY`,`scaleFactor`,`rotationAngle`,`isCropping`,`isDragEnabled`,`isScaleEnabled`,`isRotationEnabled`,`isVisible`,`isMirrored`,`cornerRadius`,`alpha`,`isControlEnabled`,`isBorderEnabled`,`borderColor`,`borderWidth`,`cropRect`,`baseWindowWidth`,`baseWindowHeight`,`note`,`textContent`,`textIsBold`,`textIsItalic`,`textFontSize`,`textFontName`,`textFontFamily`,`textLineSpacing`,`textAlignment`,`richTextData`,`windowColorEnabled`,`windowBackgroundColor`,`mediaFileUri`,`mediaFileName`,`mediaContentType`,`videoPlayEnabled`,`videoLoopCount`,`videoVolume`,`isLandscapeModeEnabled`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final WindowLayoutItemEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getLayoutId());
        statement.bindLong(3, entity.getOrderIndex());
        if (entity.getDeviceName() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getDeviceName());
        }
        if (entity.getDeviceId() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getDeviceId());
        }
        if (entity.getIpAddress() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getIpAddress());
        }
        statement.bindLong(7, entity.getPort());
        statement.bindDouble(8, entity.getPositionX());
        statement.bindDouble(9, entity.getPositionY());
        statement.bindDouble(10, entity.getScaleFactor());
        statement.bindDouble(11, entity.getRotationAngle());
        final int _tmp = entity.isCropping() ? 1 : 0;
        statement.bindLong(12, _tmp);
        final int _tmp_1 = entity.isDragEnabled() ? 1 : 0;
        statement.bindLong(13, _tmp_1);
        final int _tmp_2 = entity.isScaleEnabled() ? 1 : 0;
        statement.bindLong(14, _tmp_2);
        final int _tmp_3 = entity.isRotationEnabled() ? 1 : 0;
        statement.bindLong(15, _tmp_3);
        final int _tmp_4 = entity.isVisible() ? 1 : 0;
        statement.bindLong(16, _tmp_4);
        final int _tmp_5 = entity.isMirrored() ? 1 : 0;
        statement.bindLong(17, _tmp_5);
        statement.bindDouble(18, entity.getCornerRadius());
        statement.bindDouble(19, entity.getAlpha());
        final int _tmp_6 = entity.isControlEnabled() ? 1 : 0;
        statement.bindLong(20, _tmp_6);
        final int _tmp_7 = entity.isBorderEnabled() ? 1 : 0;
        statement.bindLong(21, _tmp_7);
        statement.bindLong(22, entity.getBorderColor());
        statement.bindDouble(23, entity.getBorderWidth());
        if (entity.getCropRect() == null) {
          statement.bindNull(24);
        } else {
          statement.bindString(24, entity.getCropRect());
        }
        statement.bindLong(25, entity.getBaseWindowWidth());
        statement.bindLong(26, entity.getBaseWindowHeight());
        if (entity.getNote() == null) {
          statement.bindNull(27);
        } else {
          statement.bindString(27, entity.getNote());
        }
        if (entity.getTextContent() == null) {
          statement.bindNull(28);
        } else {
          statement.bindString(28, entity.getTextContent());
        }
        final int _tmp_8 = entity.getTextIsBold() ? 1 : 0;
        statement.bindLong(29, _tmp_8);
        final int _tmp_9 = entity.getTextIsItalic() ? 1 : 0;
        statement.bindLong(30, _tmp_9);
        statement.bindLong(31, entity.getTextFontSize());
        if (entity.getTextFontName() == null) {
          statement.bindNull(32);
        } else {
          statement.bindString(32, entity.getTextFontName());
        }
        if (entity.getTextFontFamily() == null) {
          statement.bindNull(33);
        } else {
          statement.bindString(33, entity.getTextFontFamily());
        }
        statement.bindDouble(34, entity.getTextLineSpacing());
        statement.bindLong(35, entity.getTextAlignment());
        if (entity.getRichTextData() == null) {
          statement.bindNull(36);
        } else {
          statement.bindString(36, entity.getRichTextData());
        }
        final int _tmp_10 = entity.getWindowColorEnabled() ? 1 : 0;
        statement.bindLong(37, _tmp_10);
        statement.bindLong(38, entity.getWindowBackgroundColor());
        if (entity.getMediaFileUri() == null) {
          statement.bindNull(39);
        } else {
          statement.bindString(39, entity.getMediaFileUri());
        }
        if (entity.getMediaFileName() == null) {
          statement.bindNull(40);
        } else {
          statement.bindString(40, entity.getMediaFileName());
        }
        if (entity.getMediaContentType() == null) {
          statement.bindNull(41);
        } else {
          statement.bindString(41, entity.getMediaContentType());
        }
        final int _tmp_11 = entity.getVideoPlayEnabled() ? 1 : 0;
        statement.bindLong(42, _tmp_11);
        statement.bindLong(43, entity.getVideoLoopCount());
        statement.bindLong(44, entity.getVideoVolume());
        final int _tmp_12 = entity.isLandscapeModeEnabled() ? 1 : 0;
        statement.bindLong(45, _tmp_12);
      }
    };
    this.__deletionAdapterOfWindowLayoutEntity = new EntityDeletionOrUpdateAdapter<WindowLayoutEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `window_layouts` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final WindowLayoutEntity entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfWindowLayoutEntity = new EntityDeletionOrUpdateAdapter<WindowLayoutEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `window_layouts` SET `id` = ?,`layoutName` = ?,`description` = ?,`createdAt` = ?,`updatedAt` = ?,`windowCount` = ?,`sortOrder` = ?,`isApplied` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final WindowLayoutEntity entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getLayoutName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getLayoutName());
        }
        if (entity.getDescription() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getDescription());
        }
        final Long _tmp = __dateConverter.dateToTimestamp(entity.getCreatedAt());
        if (_tmp == null) {
          statement.bindNull(4);
        } else {
          statement.bindLong(4, _tmp);
        }
        final Long _tmp_1 = __dateConverter.dateToTimestamp(entity.getUpdatedAt());
        if (_tmp_1 == null) {
          statement.bindNull(5);
        } else {
          statement.bindLong(5, _tmp_1);
        }
        statement.bindLong(6, entity.getWindowCount());
        statement.bindLong(7, entity.getSortOrder());
        final int _tmp_2 = entity.isApplied() ? 1 : 0;
        statement.bindLong(8, _tmp_2);
        statement.bindLong(9, entity.getId());
      }
    };
    this.__updateAdapterOfWindowLayoutItemEntity = new EntityDeletionOrUpdateAdapter<WindowLayoutItemEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `window_layout_items` SET `id` = ?,`layoutId` = ?,`orderIndex` = ?,`deviceName` = ?,`deviceId` = ?,`ipAddress` = ?,`port` = ?,`positionX` = ?,`positionY` = ?,`scaleFactor` = ?,`rotationAngle` = ?,`isCropping` = ?,`isDragEnabled` = ?,`isScaleEnabled` = ?,`isRotationEnabled` = ?,`isVisible` = ?,`isMirrored` = ?,`cornerRadius` = ?,`alpha` = ?,`isControlEnabled` = ?,`isBorderEnabled` = ?,`borderColor` = ?,`borderWidth` = ?,`cropRect` = ?,`baseWindowWidth` = ?,`baseWindowHeight` = ?,`note` = ?,`textContent` = ?,`textIsBold` = ?,`textIsItalic` = ?,`textFontSize` = ?,`textFontName` = ?,`textFontFamily` = ?,`textLineSpacing` = ?,`textAlignment` = ?,`richTextData` = ?,`windowColorEnabled` = ?,`windowBackgroundColor` = ?,`mediaFileUri` = ?,`mediaFileName` = ?,`mediaContentType` = ?,`videoPlayEnabled` = ?,`videoLoopCount` = ?,`videoVolume` = ?,`isLandscapeModeEnabled` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final WindowLayoutItemEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getLayoutId());
        statement.bindLong(3, entity.getOrderIndex());
        if (entity.getDeviceName() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getDeviceName());
        }
        if (entity.getDeviceId() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getDeviceId());
        }
        if (entity.getIpAddress() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getIpAddress());
        }
        statement.bindLong(7, entity.getPort());
        statement.bindDouble(8, entity.getPositionX());
        statement.bindDouble(9, entity.getPositionY());
        statement.bindDouble(10, entity.getScaleFactor());
        statement.bindDouble(11, entity.getRotationAngle());
        final int _tmp = entity.isCropping() ? 1 : 0;
        statement.bindLong(12, _tmp);
        final int _tmp_1 = entity.isDragEnabled() ? 1 : 0;
        statement.bindLong(13, _tmp_1);
        final int _tmp_2 = entity.isScaleEnabled() ? 1 : 0;
        statement.bindLong(14, _tmp_2);
        final int _tmp_3 = entity.isRotationEnabled() ? 1 : 0;
        statement.bindLong(15, _tmp_3);
        final int _tmp_4 = entity.isVisible() ? 1 : 0;
        statement.bindLong(16, _tmp_4);
        final int _tmp_5 = entity.isMirrored() ? 1 : 0;
        statement.bindLong(17, _tmp_5);
        statement.bindDouble(18, entity.getCornerRadius());
        statement.bindDouble(19, entity.getAlpha());
        final int _tmp_6 = entity.isControlEnabled() ? 1 : 0;
        statement.bindLong(20, _tmp_6);
        final int _tmp_7 = entity.isBorderEnabled() ? 1 : 0;
        statement.bindLong(21, _tmp_7);
        statement.bindLong(22, entity.getBorderColor());
        statement.bindDouble(23, entity.getBorderWidth());
        if (entity.getCropRect() == null) {
          statement.bindNull(24);
        } else {
          statement.bindString(24, entity.getCropRect());
        }
        statement.bindLong(25, entity.getBaseWindowWidth());
        statement.bindLong(26, entity.getBaseWindowHeight());
        if (entity.getNote() == null) {
          statement.bindNull(27);
        } else {
          statement.bindString(27, entity.getNote());
        }
        if (entity.getTextContent() == null) {
          statement.bindNull(28);
        } else {
          statement.bindString(28, entity.getTextContent());
        }
        final int _tmp_8 = entity.getTextIsBold() ? 1 : 0;
        statement.bindLong(29, _tmp_8);
        final int _tmp_9 = entity.getTextIsItalic() ? 1 : 0;
        statement.bindLong(30, _tmp_9);
        statement.bindLong(31, entity.getTextFontSize());
        if (entity.getTextFontName() == null) {
          statement.bindNull(32);
        } else {
          statement.bindString(32, entity.getTextFontName());
        }
        if (entity.getTextFontFamily() == null) {
          statement.bindNull(33);
        } else {
          statement.bindString(33, entity.getTextFontFamily());
        }
        statement.bindDouble(34, entity.getTextLineSpacing());
        statement.bindLong(35, entity.getTextAlignment());
        if (entity.getRichTextData() == null) {
          statement.bindNull(36);
        } else {
          statement.bindString(36, entity.getRichTextData());
        }
        final int _tmp_10 = entity.getWindowColorEnabled() ? 1 : 0;
        statement.bindLong(37, _tmp_10);
        statement.bindLong(38, entity.getWindowBackgroundColor());
        if (entity.getMediaFileUri() == null) {
          statement.bindNull(39);
        } else {
          statement.bindString(39, entity.getMediaFileUri());
        }
        if (entity.getMediaFileName() == null) {
          statement.bindNull(40);
        } else {
          statement.bindString(40, entity.getMediaFileName());
        }
        if (entity.getMediaContentType() == null) {
          statement.bindNull(41);
        } else {
          statement.bindString(41, entity.getMediaContentType());
        }
        final int _tmp_11 = entity.getVideoPlayEnabled() ? 1 : 0;
        statement.bindLong(42, _tmp_11);
        statement.bindLong(43, entity.getVideoLoopCount());
        statement.bindLong(44, entity.getVideoVolume());
        final int _tmp_12 = entity.isLandscapeModeEnabled() ? 1 : 0;
        statement.bindLong(45, _tmp_12);
        statement.bindLong(46, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteLayoutById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM window_layouts WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateLayoutSortOrder = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE window_layouts SET sortOrder = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateLayoutAppliedStatus = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE window_layouts SET isApplied = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfClearAllAppliedStatus = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE window_layouts SET isApplied = 0";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteLayoutItemsByLayoutId = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM window_layout_items WHERE layoutId = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertLayout(final WindowLayoutEntity layout,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfWindowLayoutEntity.insertAndReturnId(layout);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertLayoutItems(final List<WindowLayoutItemEntity> items,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfWindowLayoutItemEntity.insert(items);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertLayoutItem(final WindowLayoutItemEntity item,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfWindowLayoutItemEntity.insertAndReturnId(item);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteLayout(final WindowLayoutEntity layout,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfWindowLayoutEntity.handle(layout);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateLayout(final WindowLayoutEntity layout,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfWindowLayoutEntity.handle(layout);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateLayoutItem(final WindowLayoutItemEntity item,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfWindowLayoutItemEntity.handle(item);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteLayoutById(final long layoutId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteLayoutById.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, layoutId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteLayoutById.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateLayoutSortOrder(final long layoutId, final int sortOrder,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateLayoutSortOrder.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, sortOrder);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, layoutId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateLayoutSortOrder.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateLayoutAppliedStatus(final long layoutId, final boolean isApplied,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateLayoutAppliedStatus.acquire();
        int _argIndex = 1;
        final int _tmp = isApplied ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, layoutId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateLayoutAppliedStatus.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object clearAllAppliedStatus(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfClearAllAppliedStatus.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfClearAllAppliedStatus.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteLayoutItemsByLayoutId(final long layoutId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteLayoutItemsByLayoutId.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, layoutId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteLayoutItemsByLayoutId.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<WindowLayoutEntity>> getAllLayouts() {
    final String _sql = "SELECT * FROM window_layouts ORDER BY sortOrder ASC, createdAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"window_layouts"}, new Callable<List<WindowLayoutEntity>>() {
      @Override
      @NonNull
      public List<WindowLayoutEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfLayoutName = CursorUtil.getColumnIndexOrThrow(_cursor, "layoutName");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfWindowCount = CursorUtil.getColumnIndexOrThrow(_cursor, "windowCount");
          final int _cursorIndexOfSortOrder = CursorUtil.getColumnIndexOrThrow(_cursor, "sortOrder");
          final int _cursorIndexOfIsApplied = CursorUtil.getColumnIndexOrThrow(_cursor, "isApplied");
          final List<WindowLayoutEntity> _result = new ArrayList<WindowLayoutEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final WindowLayoutEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpLayoutName;
            if (_cursor.isNull(_cursorIndexOfLayoutName)) {
              _tmpLayoutName = null;
            } else {
              _tmpLayoutName = _cursor.getString(_cursorIndexOfLayoutName);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final Date _tmpCreatedAt;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = __dateConverter.fromTimestamp(_tmp);
            final Date _tmpUpdatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            _tmpUpdatedAt = __dateConverter.fromTimestamp(_tmp_1);
            final int _tmpWindowCount;
            _tmpWindowCount = _cursor.getInt(_cursorIndexOfWindowCount);
            final int _tmpSortOrder;
            _tmpSortOrder = _cursor.getInt(_cursorIndexOfSortOrder);
            final boolean _tmpIsApplied;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsApplied);
            _tmpIsApplied = _tmp_2 != 0;
            _item = new WindowLayoutEntity(_tmpId,_tmpLayoutName,_tmpDescription,_tmpCreatedAt,_tmpUpdatedAt,_tmpWindowCount,_tmpSortOrder,_tmpIsApplied);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getLayoutById(final long layoutId,
      final Continuation<? super WindowLayoutEntity> $completion) {
    final String _sql = "SELECT * FROM window_layouts WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, layoutId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<WindowLayoutEntity>() {
      @Override
      @Nullable
      public WindowLayoutEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfLayoutName = CursorUtil.getColumnIndexOrThrow(_cursor, "layoutName");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfWindowCount = CursorUtil.getColumnIndexOrThrow(_cursor, "windowCount");
          final int _cursorIndexOfSortOrder = CursorUtil.getColumnIndexOrThrow(_cursor, "sortOrder");
          final int _cursorIndexOfIsApplied = CursorUtil.getColumnIndexOrThrow(_cursor, "isApplied");
          final WindowLayoutEntity _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpLayoutName;
            if (_cursor.isNull(_cursorIndexOfLayoutName)) {
              _tmpLayoutName = null;
            } else {
              _tmpLayoutName = _cursor.getString(_cursorIndexOfLayoutName);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final Date _tmpCreatedAt;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = __dateConverter.fromTimestamp(_tmp);
            final Date _tmpUpdatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            _tmpUpdatedAt = __dateConverter.fromTimestamp(_tmp_1);
            final int _tmpWindowCount;
            _tmpWindowCount = _cursor.getInt(_cursorIndexOfWindowCount);
            final int _tmpSortOrder;
            _tmpSortOrder = _cursor.getInt(_cursorIndexOfSortOrder);
            final boolean _tmpIsApplied;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsApplied);
            _tmpIsApplied = _tmp_2 != 0;
            _result = new WindowLayoutEntity(_tmpId,_tmpLayoutName,_tmpDescription,_tmpCreatedAt,_tmpUpdatedAt,_tmpWindowCount,_tmpSortOrder,_tmpIsApplied);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getLayoutByName(final String name,
      final Continuation<? super WindowLayoutEntity> $completion) {
    final String _sql = "SELECT * FROM window_layouts WHERE layoutName = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (name == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, name);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<WindowLayoutEntity>() {
      @Override
      @Nullable
      public WindowLayoutEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfLayoutName = CursorUtil.getColumnIndexOrThrow(_cursor, "layoutName");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfWindowCount = CursorUtil.getColumnIndexOrThrow(_cursor, "windowCount");
          final int _cursorIndexOfSortOrder = CursorUtil.getColumnIndexOrThrow(_cursor, "sortOrder");
          final int _cursorIndexOfIsApplied = CursorUtil.getColumnIndexOrThrow(_cursor, "isApplied");
          final WindowLayoutEntity _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpLayoutName;
            if (_cursor.isNull(_cursorIndexOfLayoutName)) {
              _tmpLayoutName = null;
            } else {
              _tmpLayoutName = _cursor.getString(_cursorIndexOfLayoutName);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final Date _tmpCreatedAt;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = __dateConverter.fromTimestamp(_tmp);
            final Date _tmpUpdatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            _tmpUpdatedAt = __dateConverter.fromTimestamp(_tmp_1);
            final int _tmpWindowCount;
            _tmpWindowCount = _cursor.getInt(_cursorIndexOfWindowCount);
            final int _tmpSortOrder;
            _tmpSortOrder = _cursor.getInt(_cursorIndexOfSortOrder);
            final boolean _tmpIsApplied;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsApplied);
            _tmpIsApplied = _tmp_2 != 0;
            _result = new WindowLayoutEntity(_tmpId,_tmpLayoutName,_tmpDescription,_tmpCreatedAt,_tmpUpdatedAt,_tmpWindowCount,_tmpSortOrder,_tmpIsApplied);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object isLayoutNameExists(final String name,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM window_layouts WHERE layoutName = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (name == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, name);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getMaxSortOrder(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT MAX(sortOrder) FROM window_layouts";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @Nullable
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCurrentAppliedLayout(
      final Continuation<? super WindowLayoutEntity> $completion) {
    final String _sql = "SELECT * FROM window_layouts WHERE isApplied = 1 LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<WindowLayoutEntity>() {
      @Override
      @Nullable
      public WindowLayoutEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfLayoutName = CursorUtil.getColumnIndexOrThrow(_cursor, "layoutName");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfWindowCount = CursorUtil.getColumnIndexOrThrow(_cursor, "windowCount");
          final int _cursorIndexOfSortOrder = CursorUtil.getColumnIndexOrThrow(_cursor, "sortOrder");
          final int _cursorIndexOfIsApplied = CursorUtil.getColumnIndexOrThrow(_cursor, "isApplied");
          final WindowLayoutEntity _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpLayoutName;
            if (_cursor.isNull(_cursorIndexOfLayoutName)) {
              _tmpLayoutName = null;
            } else {
              _tmpLayoutName = _cursor.getString(_cursorIndexOfLayoutName);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final Date _tmpCreatedAt;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = __dateConverter.fromTimestamp(_tmp);
            final Date _tmpUpdatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            _tmpUpdatedAt = __dateConverter.fromTimestamp(_tmp_1);
            final int _tmpWindowCount;
            _tmpWindowCount = _cursor.getInt(_cursorIndexOfWindowCount);
            final int _tmpSortOrder;
            _tmpSortOrder = _cursor.getInt(_cursorIndexOfSortOrder);
            final boolean _tmpIsApplied;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsApplied);
            _tmpIsApplied = _tmp_2 != 0;
            _result = new WindowLayoutEntity(_tmpId,_tmpLayoutName,_tmpDescription,_tmpCreatedAt,_tmpUpdatedAt,_tmpWindowCount,_tmpSortOrder,_tmpIsApplied);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getLayoutItemsByLayoutId(final long layoutId,
      final Continuation<? super List<WindowLayoutItemEntity>> $completion) {
    final String _sql = "SELECT * FROM window_layout_items WHERE layoutId = ? ORDER BY orderIndex ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, layoutId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<WindowLayoutItemEntity>>() {
      @Override
      @NonNull
      public List<WindowLayoutItemEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfLayoutId = CursorUtil.getColumnIndexOrThrow(_cursor, "layoutId");
          final int _cursorIndexOfOrderIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "orderIndex");
          final int _cursorIndexOfDeviceName = CursorUtil.getColumnIndexOrThrow(_cursor, "deviceName");
          final int _cursorIndexOfDeviceId = CursorUtil.getColumnIndexOrThrow(_cursor, "deviceId");
          final int _cursorIndexOfIpAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "ipAddress");
          final int _cursorIndexOfPort = CursorUtil.getColumnIndexOrThrow(_cursor, "port");
          final int _cursorIndexOfPositionX = CursorUtil.getColumnIndexOrThrow(_cursor, "positionX");
          final int _cursorIndexOfPositionY = CursorUtil.getColumnIndexOrThrow(_cursor, "positionY");
          final int _cursorIndexOfScaleFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "scaleFactor");
          final int _cursorIndexOfRotationAngle = CursorUtil.getColumnIndexOrThrow(_cursor, "rotationAngle");
          final int _cursorIndexOfIsCropping = CursorUtil.getColumnIndexOrThrow(_cursor, "isCropping");
          final int _cursorIndexOfIsDragEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "isDragEnabled");
          final int _cursorIndexOfIsScaleEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "isScaleEnabled");
          final int _cursorIndexOfIsRotationEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "isRotationEnabled");
          final int _cursorIndexOfIsVisible = CursorUtil.getColumnIndexOrThrow(_cursor, "isVisible");
          final int _cursorIndexOfIsMirrored = CursorUtil.getColumnIndexOrThrow(_cursor, "isMirrored");
          final int _cursorIndexOfCornerRadius = CursorUtil.getColumnIndexOrThrow(_cursor, "cornerRadius");
          final int _cursorIndexOfAlpha = CursorUtil.getColumnIndexOrThrow(_cursor, "alpha");
          final int _cursorIndexOfIsControlEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "isControlEnabled");
          final int _cursorIndexOfIsBorderEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "isBorderEnabled");
          final int _cursorIndexOfBorderColor = CursorUtil.getColumnIndexOrThrow(_cursor, "borderColor");
          final int _cursorIndexOfBorderWidth = CursorUtil.getColumnIndexOrThrow(_cursor, "borderWidth");
          final int _cursorIndexOfCropRect = CursorUtil.getColumnIndexOrThrow(_cursor, "cropRect");
          final int _cursorIndexOfBaseWindowWidth = CursorUtil.getColumnIndexOrThrow(_cursor, "baseWindowWidth");
          final int _cursorIndexOfBaseWindowHeight = CursorUtil.getColumnIndexOrThrow(_cursor, "baseWindowHeight");
          final int _cursorIndexOfNote = CursorUtil.getColumnIndexOrThrow(_cursor, "note");
          final int _cursorIndexOfTextContent = CursorUtil.getColumnIndexOrThrow(_cursor, "textContent");
          final int _cursorIndexOfTextIsBold = CursorUtil.getColumnIndexOrThrow(_cursor, "textIsBold");
          final int _cursorIndexOfTextIsItalic = CursorUtil.getColumnIndexOrThrow(_cursor, "textIsItalic");
          final int _cursorIndexOfTextFontSize = CursorUtil.getColumnIndexOrThrow(_cursor, "textFontSize");
          final int _cursorIndexOfTextFontName = CursorUtil.getColumnIndexOrThrow(_cursor, "textFontName");
          final int _cursorIndexOfTextFontFamily = CursorUtil.getColumnIndexOrThrow(_cursor, "textFontFamily");
          final int _cursorIndexOfTextLineSpacing = CursorUtil.getColumnIndexOrThrow(_cursor, "textLineSpacing");
          final int _cursorIndexOfTextAlignment = CursorUtil.getColumnIndexOrThrow(_cursor, "textAlignment");
          final int _cursorIndexOfRichTextData = CursorUtil.getColumnIndexOrThrow(_cursor, "richTextData");
          final int _cursorIndexOfWindowColorEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "windowColorEnabled");
          final int _cursorIndexOfWindowBackgroundColor = CursorUtil.getColumnIndexOrThrow(_cursor, "windowBackgroundColor");
          final int _cursorIndexOfMediaFileUri = CursorUtil.getColumnIndexOrThrow(_cursor, "mediaFileUri");
          final int _cursorIndexOfMediaFileName = CursorUtil.getColumnIndexOrThrow(_cursor, "mediaFileName");
          final int _cursorIndexOfMediaContentType = CursorUtil.getColumnIndexOrThrow(_cursor, "mediaContentType");
          final int _cursorIndexOfVideoPlayEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "videoPlayEnabled");
          final int _cursorIndexOfVideoLoopCount = CursorUtil.getColumnIndexOrThrow(_cursor, "videoLoopCount");
          final int _cursorIndexOfVideoVolume = CursorUtil.getColumnIndexOrThrow(_cursor, "videoVolume");
          final int _cursorIndexOfIsLandscapeModeEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "isLandscapeModeEnabled");
          final List<WindowLayoutItemEntity> _result = new ArrayList<WindowLayoutItemEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final WindowLayoutItemEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final long _tmpLayoutId;
            _tmpLayoutId = _cursor.getLong(_cursorIndexOfLayoutId);
            final int _tmpOrderIndex;
            _tmpOrderIndex = _cursor.getInt(_cursorIndexOfOrderIndex);
            final String _tmpDeviceName;
            if (_cursor.isNull(_cursorIndexOfDeviceName)) {
              _tmpDeviceName = null;
            } else {
              _tmpDeviceName = _cursor.getString(_cursorIndexOfDeviceName);
            }
            final String _tmpDeviceId;
            if (_cursor.isNull(_cursorIndexOfDeviceId)) {
              _tmpDeviceId = null;
            } else {
              _tmpDeviceId = _cursor.getString(_cursorIndexOfDeviceId);
            }
            final String _tmpIpAddress;
            if (_cursor.isNull(_cursorIndexOfIpAddress)) {
              _tmpIpAddress = null;
            } else {
              _tmpIpAddress = _cursor.getString(_cursorIndexOfIpAddress);
            }
            final int _tmpPort;
            _tmpPort = _cursor.getInt(_cursorIndexOfPort);
            final float _tmpPositionX;
            _tmpPositionX = _cursor.getFloat(_cursorIndexOfPositionX);
            final float _tmpPositionY;
            _tmpPositionY = _cursor.getFloat(_cursorIndexOfPositionY);
            final float _tmpScaleFactor;
            _tmpScaleFactor = _cursor.getFloat(_cursorIndexOfScaleFactor);
            final float _tmpRotationAngle;
            _tmpRotationAngle = _cursor.getFloat(_cursorIndexOfRotationAngle);
            final boolean _tmpIsCropping;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCropping);
            _tmpIsCropping = _tmp != 0;
            final boolean _tmpIsDragEnabled;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsDragEnabled);
            _tmpIsDragEnabled = _tmp_1 != 0;
            final boolean _tmpIsScaleEnabled;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsScaleEnabled);
            _tmpIsScaleEnabled = _tmp_2 != 0;
            final boolean _tmpIsRotationEnabled;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsRotationEnabled);
            _tmpIsRotationEnabled = _tmp_3 != 0;
            final boolean _tmpIsVisible;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfIsVisible);
            _tmpIsVisible = _tmp_4 != 0;
            final boolean _tmpIsMirrored;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsMirrored);
            _tmpIsMirrored = _tmp_5 != 0;
            final float _tmpCornerRadius;
            _tmpCornerRadius = _cursor.getFloat(_cursorIndexOfCornerRadius);
            final float _tmpAlpha;
            _tmpAlpha = _cursor.getFloat(_cursorIndexOfAlpha);
            final boolean _tmpIsControlEnabled;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsControlEnabled);
            _tmpIsControlEnabled = _tmp_6 != 0;
            final boolean _tmpIsBorderEnabled;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfIsBorderEnabled);
            _tmpIsBorderEnabled = _tmp_7 != 0;
            final int _tmpBorderColor;
            _tmpBorderColor = _cursor.getInt(_cursorIndexOfBorderColor);
            final float _tmpBorderWidth;
            _tmpBorderWidth = _cursor.getFloat(_cursorIndexOfBorderWidth);
            final String _tmpCropRect;
            if (_cursor.isNull(_cursorIndexOfCropRect)) {
              _tmpCropRect = null;
            } else {
              _tmpCropRect = _cursor.getString(_cursorIndexOfCropRect);
            }
            final int _tmpBaseWindowWidth;
            _tmpBaseWindowWidth = _cursor.getInt(_cursorIndexOfBaseWindowWidth);
            final int _tmpBaseWindowHeight;
            _tmpBaseWindowHeight = _cursor.getInt(_cursorIndexOfBaseWindowHeight);
            final String _tmpNote;
            if (_cursor.isNull(_cursorIndexOfNote)) {
              _tmpNote = null;
            } else {
              _tmpNote = _cursor.getString(_cursorIndexOfNote);
            }
            final String _tmpTextContent;
            if (_cursor.isNull(_cursorIndexOfTextContent)) {
              _tmpTextContent = null;
            } else {
              _tmpTextContent = _cursor.getString(_cursorIndexOfTextContent);
            }
            final boolean _tmpTextIsBold;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfTextIsBold);
            _tmpTextIsBold = _tmp_8 != 0;
            final boolean _tmpTextIsItalic;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfTextIsItalic);
            _tmpTextIsItalic = _tmp_9 != 0;
            final int _tmpTextFontSize;
            _tmpTextFontSize = _cursor.getInt(_cursorIndexOfTextFontSize);
            final String _tmpTextFontName;
            if (_cursor.isNull(_cursorIndexOfTextFontName)) {
              _tmpTextFontName = null;
            } else {
              _tmpTextFontName = _cursor.getString(_cursorIndexOfTextFontName);
            }
            final String _tmpTextFontFamily;
            if (_cursor.isNull(_cursorIndexOfTextFontFamily)) {
              _tmpTextFontFamily = null;
            } else {
              _tmpTextFontFamily = _cursor.getString(_cursorIndexOfTextFontFamily);
            }
            final float _tmpTextLineSpacing;
            _tmpTextLineSpacing = _cursor.getFloat(_cursorIndexOfTextLineSpacing);
            final int _tmpTextAlignment;
            _tmpTextAlignment = _cursor.getInt(_cursorIndexOfTextAlignment);
            final String _tmpRichTextData;
            if (_cursor.isNull(_cursorIndexOfRichTextData)) {
              _tmpRichTextData = null;
            } else {
              _tmpRichTextData = _cursor.getString(_cursorIndexOfRichTextData);
            }
            final boolean _tmpWindowColorEnabled;
            final int _tmp_10;
            _tmp_10 = _cursor.getInt(_cursorIndexOfWindowColorEnabled);
            _tmpWindowColorEnabled = _tmp_10 != 0;
            final int _tmpWindowBackgroundColor;
            _tmpWindowBackgroundColor = _cursor.getInt(_cursorIndexOfWindowBackgroundColor);
            final String _tmpMediaFileUri;
            if (_cursor.isNull(_cursorIndexOfMediaFileUri)) {
              _tmpMediaFileUri = null;
            } else {
              _tmpMediaFileUri = _cursor.getString(_cursorIndexOfMediaFileUri);
            }
            final String _tmpMediaFileName;
            if (_cursor.isNull(_cursorIndexOfMediaFileName)) {
              _tmpMediaFileName = null;
            } else {
              _tmpMediaFileName = _cursor.getString(_cursorIndexOfMediaFileName);
            }
            final String _tmpMediaContentType;
            if (_cursor.isNull(_cursorIndexOfMediaContentType)) {
              _tmpMediaContentType = null;
            } else {
              _tmpMediaContentType = _cursor.getString(_cursorIndexOfMediaContentType);
            }
            final boolean _tmpVideoPlayEnabled;
            final int _tmp_11;
            _tmp_11 = _cursor.getInt(_cursorIndexOfVideoPlayEnabled);
            _tmpVideoPlayEnabled = _tmp_11 != 0;
            final int _tmpVideoLoopCount;
            _tmpVideoLoopCount = _cursor.getInt(_cursorIndexOfVideoLoopCount);
            final int _tmpVideoVolume;
            _tmpVideoVolume = _cursor.getInt(_cursorIndexOfVideoVolume);
            final boolean _tmpIsLandscapeModeEnabled;
            final int _tmp_12;
            _tmp_12 = _cursor.getInt(_cursorIndexOfIsLandscapeModeEnabled);
            _tmpIsLandscapeModeEnabled = _tmp_12 != 0;
            _item = new WindowLayoutItemEntity(_tmpId,_tmpLayoutId,_tmpOrderIndex,_tmpDeviceName,_tmpDeviceId,_tmpIpAddress,_tmpPort,_tmpPositionX,_tmpPositionY,_tmpScaleFactor,_tmpRotationAngle,_tmpIsCropping,_tmpIsDragEnabled,_tmpIsScaleEnabled,_tmpIsRotationEnabled,_tmpIsVisible,_tmpIsMirrored,_tmpCornerRadius,_tmpAlpha,_tmpIsControlEnabled,_tmpIsBorderEnabled,_tmpBorderColor,_tmpBorderWidth,_tmpCropRect,_tmpBaseWindowWidth,_tmpBaseWindowHeight,_tmpNote,_tmpTextContent,_tmpTextIsBold,_tmpTextIsItalic,_tmpTextFontSize,_tmpTextFontName,_tmpTextFontFamily,_tmpTextLineSpacing,_tmpTextAlignment,_tmpRichTextData,_tmpWindowColorEnabled,_tmpWindowBackgroundColor,_tmpMediaFileUri,_tmpMediaFileName,_tmpMediaContentType,_tmpVideoPlayEnabled,_tmpVideoLoopCount,_tmpVideoVolume,_tmpIsLandscapeModeEnabled);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<WindowLayoutItemEntity>> getLayoutItemsByLayoutIdFlow(final long layoutId) {
    final String _sql = "SELECT * FROM window_layout_items WHERE layoutId = ? ORDER BY orderIndex ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, layoutId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"window_layout_items"}, new Callable<List<WindowLayoutItemEntity>>() {
      @Override
      @NonNull
      public List<WindowLayoutItemEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfLayoutId = CursorUtil.getColumnIndexOrThrow(_cursor, "layoutId");
          final int _cursorIndexOfOrderIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "orderIndex");
          final int _cursorIndexOfDeviceName = CursorUtil.getColumnIndexOrThrow(_cursor, "deviceName");
          final int _cursorIndexOfDeviceId = CursorUtil.getColumnIndexOrThrow(_cursor, "deviceId");
          final int _cursorIndexOfIpAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "ipAddress");
          final int _cursorIndexOfPort = CursorUtil.getColumnIndexOrThrow(_cursor, "port");
          final int _cursorIndexOfPositionX = CursorUtil.getColumnIndexOrThrow(_cursor, "positionX");
          final int _cursorIndexOfPositionY = CursorUtil.getColumnIndexOrThrow(_cursor, "positionY");
          final int _cursorIndexOfScaleFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "scaleFactor");
          final int _cursorIndexOfRotationAngle = CursorUtil.getColumnIndexOrThrow(_cursor, "rotationAngle");
          final int _cursorIndexOfIsCropping = CursorUtil.getColumnIndexOrThrow(_cursor, "isCropping");
          final int _cursorIndexOfIsDragEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "isDragEnabled");
          final int _cursorIndexOfIsScaleEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "isScaleEnabled");
          final int _cursorIndexOfIsRotationEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "isRotationEnabled");
          final int _cursorIndexOfIsVisible = CursorUtil.getColumnIndexOrThrow(_cursor, "isVisible");
          final int _cursorIndexOfIsMirrored = CursorUtil.getColumnIndexOrThrow(_cursor, "isMirrored");
          final int _cursorIndexOfCornerRadius = CursorUtil.getColumnIndexOrThrow(_cursor, "cornerRadius");
          final int _cursorIndexOfAlpha = CursorUtil.getColumnIndexOrThrow(_cursor, "alpha");
          final int _cursorIndexOfIsControlEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "isControlEnabled");
          final int _cursorIndexOfIsBorderEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "isBorderEnabled");
          final int _cursorIndexOfBorderColor = CursorUtil.getColumnIndexOrThrow(_cursor, "borderColor");
          final int _cursorIndexOfBorderWidth = CursorUtil.getColumnIndexOrThrow(_cursor, "borderWidth");
          final int _cursorIndexOfCropRect = CursorUtil.getColumnIndexOrThrow(_cursor, "cropRect");
          final int _cursorIndexOfBaseWindowWidth = CursorUtil.getColumnIndexOrThrow(_cursor, "baseWindowWidth");
          final int _cursorIndexOfBaseWindowHeight = CursorUtil.getColumnIndexOrThrow(_cursor, "baseWindowHeight");
          final int _cursorIndexOfNote = CursorUtil.getColumnIndexOrThrow(_cursor, "note");
          final int _cursorIndexOfTextContent = CursorUtil.getColumnIndexOrThrow(_cursor, "textContent");
          final int _cursorIndexOfTextIsBold = CursorUtil.getColumnIndexOrThrow(_cursor, "textIsBold");
          final int _cursorIndexOfTextIsItalic = CursorUtil.getColumnIndexOrThrow(_cursor, "textIsItalic");
          final int _cursorIndexOfTextFontSize = CursorUtil.getColumnIndexOrThrow(_cursor, "textFontSize");
          final int _cursorIndexOfTextFontName = CursorUtil.getColumnIndexOrThrow(_cursor, "textFontName");
          final int _cursorIndexOfTextFontFamily = CursorUtil.getColumnIndexOrThrow(_cursor, "textFontFamily");
          final int _cursorIndexOfTextLineSpacing = CursorUtil.getColumnIndexOrThrow(_cursor, "textLineSpacing");
          final int _cursorIndexOfTextAlignment = CursorUtil.getColumnIndexOrThrow(_cursor, "textAlignment");
          final int _cursorIndexOfRichTextData = CursorUtil.getColumnIndexOrThrow(_cursor, "richTextData");
          final int _cursorIndexOfWindowColorEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "windowColorEnabled");
          final int _cursorIndexOfWindowBackgroundColor = CursorUtil.getColumnIndexOrThrow(_cursor, "windowBackgroundColor");
          final int _cursorIndexOfMediaFileUri = CursorUtil.getColumnIndexOrThrow(_cursor, "mediaFileUri");
          final int _cursorIndexOfMediaFileName = CursorUtil.getColumnIndexOrThrow(_cursor, "mediaFileName");
          final int _cursorIndexOfMediaContentType = CursorUtil.getColumnIndexOrThrow(_cursor, "mediaContentType");
          final int _cursorIndexOfVideoPlayEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "videoPlayEnabled");
          final int _cursorIndexOfVideoLoopCount = CursorUtil.getColumnIndexOrThrow(_cursor, "videoLoopCount");
          final int _cursorIndexOfVideoVolume = CursorUtil.getColumnIndexOrThrow(_cursor, "videoVolume");
          final int _cursorIndexOfIsLandscapeModeEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "isLandscapeModeEnabled");
          final List<WindowLayoutItemEntity> _result = new ArrayList<WindowLayoutItemEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final WindowLayoutItemEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final long _tmpLayoutId;
            _tmpLayoutId = _cursor.getLong(_cursorIndexOfLayoutId);
            final int _tmpOrderIndex;
            _tmpOrderIndex = _cursor.getInt(_cursorIndexOfOrderIndex);
            final String _tmpDeviceName;
            if (_cursor.isNull(_cursorIndexOfDeviceName)) {
              _tmpDeviceName = null;
            } else {
              _tmpDeviceName = _cursor.getString(_cursorIndexOfDeviceName);
            }
            final String _tmpDeviceId;
            if (_cursor.isNull(_cursorIndexOfDeviceId)) {
              _tmpDeviceId = null;
            } else {
              _tmpDeviceId = _cursor.getString(_cursorIndexOfDeviceId);
            }
            final String _tmpIpAddress;
            if (_cursor.isNull(_cursorIndexOfIpAddress)) {
              _tmpIpAddress = null;
            } else {
              _tmpIpAddress = _cursor.getString(_cursorIndexOfIpAddress);
            }
            final int _tmpPort;
            _tmpPort = _cursor.getInt(_cursorIndexOfPort);
            final float _tmpPositionX;
            _tmpPositionX = _cursor.getFloat(_cursorIndexOfPositionX);
            final float _tmpPositionY;
            _tmpPositionY = _cursor.getFloat(_cursorIndexOfPositionY);
            final float _tmpScaleFactor;
            _tmpScaleFactor = _cursor.getFloat(_cursorIndexOfScaleFactor);
            final float _tmpRotationAngle;
            _tmpRotationAngle = _cursor.getFloat(_cursorIndexOfRotationAngle);
            final boolean _tmpIsCropping;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCropping);
            _tmpIsCropping = _tmp != 0;
            final boolean _tmpIsDragEnabled;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsDragEnabled);
            _tmpIsDragEnabled = _tmp_1 != 0;
            final boolean _tmpIsScaleEnabled;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsScaleEnabled);
            _tmpIsScaleEnabled = _tmp_2 != 0;
            final boolean _tmpIsRotationEnabled;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsRotationEnabled);
            _tmpIsRotationEnabled = _tmp_3 != 0;
            final boolean _tmpIsVisible;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfIsVisible);
            _tmpIsVisible = _tmp_4 != 0;
            final boolean _tmpIsMirrored;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsMirrored);
            _tmpIsMirrored = _tmp_5 != 0;
            final float _tmpCornerRadius;
            _tmpCornerRadius = _cursor.getFloat(_cursorIndexOfCornerRadius);
            final float _tmpAlpha;
            _tmpAlpha = _cursor.getFloat(_cursorIndexOfAlpha);
            final boolean _tmpIsControlEnabled;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsControlEnabled);
            _tmpIsControlEnabled = _tmp_6 != 0;
            final boolean _tmpIsBorderEnabled;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfIsBorderEnabled);
            _tmpIsBorderEnabled = _tmp_7 != 0;
            final int _tmpBorderColor;
            _tmpBorderColor = _cursor.getInt(_cursorIndexOfBorderColor);
            final float _tmpBorderWidth;
            _tmpBorderWidth = _cursor.getFloat(_cursorIndexOfBorderWidth);
            final String _tmpCropRect;
            if (_cursor.isNull(_cursorIndexOfCropRect)) {
              _tmpCropRect = null;
            } else {
              _tmpCropRect = _cursor.getString(_cursorIndexOfCropRect);
            }
            final int _tmpBaseWindowWidth;
            _tmpBaseWindowWidth = _cursor.getInt(_cursorIndexOfBaseWindowWidth);
            final int _tmpBaseWindowHeight;
            _tmpBaseWindowHeight = _cursor.getInt(_cursorIndexOfBaseWindowHeight);
            final String _tmpNote;
            if (_cursor.isNull(_cursorIndexOfNote)) {
              _tmpNote = null;
            } else {
              _tmpNote = _cursor.getString(_cursorIndexOfNote);
            }
            final String _tmpTextContent;
            if (_cursor.isNull(_cursorIndexOfTextContent)) {
              _tmpTextContent = null;
            } else {
              _tmpTextContent = _cursor.getString(_cursorIndexOfTextContent);
            }
            final boolean _tmpTextIsBold;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfTextIsBold);
            _tmpTextIsBold = _tmp_8 != 0;
            final boolean _tmpTextIsItalic;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfTextIsItalic);
            _tmpTextIsItalic = _tmp_9 != 0;
            final int _tmpTextFontSize;
            _tmpTextFontSize = _cursor.getInt(_cursorIndexOfTextFontSize);
            final String _tmpTextFontName;
            if (_cursor.isNull(_cursorIndexOfTextFontName)) {
              _tmpTextFontName = null;
            } else {
              _tmpTextFontName = _cursor.getString(_cursorIndexOfTextFontName);
            }
            final String _tmpTextFontFamily;
            if (_cursor.isNull(_cursorIndexOfTextFontFamily)) {
              _tmpTextFontFamily = null;
            } else {
              _tmpTextFontFamily = _cursor.getString(_cursorIndexOfTextFontFamily);
            }
            final float _tmpTextLineSpacing;
            _tmpTextLineSpacing = _cursor.getFloat(_cursorIndexOfTextLineSpacing);
            final int _tmpTextAlignment;
            _tmpTextAlignment = _cursor.getInt(_cursorIndexOfTextAlignment);
            final String _tmpRichTextData;
            if (_cursor.isNull(_cursorIndexOfRichTextData)) {
              _tmpRichTextData = null;
            } else {
              _tmpRichTextData = _cursor.getString(_cursorIndexOfRichTextData);
            }
            final boolean _tmpWindowColorEnabled;
            final int _tmp_10;
            _tmp_10 = _cursor.getInt(_cursorIndexOfWindowColorEnabled);
            _tmpWindowColorEnabled = _tmp_10 != 0;
            final int _tmpWindowBackgroundColor;
            _tmpWindowBackgroundColor = _cursor.getInt(_cursorIndexOfWindowBackgroundColor);
            final String _tmpMediaFileUri;
            if (_cursor.isNull(_cursorIndexOfMediaFileUri)) {
              _tmpMediaFileUri = null;
            } else {
              _tmpMediaFileUri = _cursor.getString(_cursorIndexOfMediaFileUri);
            }
            final String _tmpMediaFileName;
            if (_cursor.isNull(_cursorIndexOfMediaFileName)) {
              _tmpMediaFileName = null;
            } else {
              _tmpMediaFileName = _cursor.getString(_cursorIndexOfMediaFileName);
            }
            final String _tmpMediaContentType;
            if (_cursor.isNull(_cursorIndexOfMediaContentType)) {
              _tmpMediaContentType = null;
            } else {
              _tmpMediaContentType = _cursor.getString(_cursorIndexOfMediaContentType);
            }
            final boolean _tmpVideoPlayEnabled;
            final int _tmp_11;
            _tmp_11 = _cursor.getInt(_cursorIndexOfVideoPlayEnabled);
            _tmpVideoPlayEnabled = _tmp_11 != 0;
            final int _tmpVideoLoopCount;
            _tmpVideoLoopCount = _cursor.getInt(_cursorIndexOfVideoLoopCount);
            final int _tmpVideoVolume;
            _tmpVideoVolume = _cursor.getInt(_cursorIndexOfVideoVolume);
            final boolean _tmpIsLandscapeModeEnabled;
            final int _tmp_12;
            _tmp_12 = _cursor.getInt(_cursorIndexOfIsLandscapeModeEnabled);
            _tmpIsLandscapeModeEnabled = _tmp_12 != 0;
            _item = new WindowLayoutItemEntity(_tmpId,_tmpLayoutId,_tmpOrderIndex,_tmpDeviceName,_tmpDeviceId,_tmpIpAddress,_tmpPort,_tmpPositionX,_tmpPositionY,_tmpScaleFactor,_tmpRotationAngle,_tmpIsCropping,_tmpIsDragEnabled,_tmpIsScaleEnabled,_tmpIsRotationEnabled,_tmpIsVisible,_tmpIsMirrored,_tmpCornerRadius,_tmpAlpha,_tmpIsControlEnabled,_tmpIsBorderEnabled,_tmpBorderColor,_tmpBorderWidth,_tmpCropRect,_tmpBaseWindowWidth,_tmpBaseWindowHeight,_tmpNote,_tmpTextContent,_tmpTextIsBold,_tmpTextIsItalic,_tmpTextFontSize,_tmpTextFontName,_tmpTextFontFamily,_tmpTextLineSpacing,_tmpTextAlignment,_tmpRichTextData,_tmpWindowColorEnabled,_tmpWindowBackgroundColor,_tmpMediaFileUri,_tmpMediaFileName,_tmpMediaContentType,_tmpVideoPlayEnabled,_tmpVideoLoopCount,_tmpVideoVolume,_tmpIsLandscapeModeEnabled);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getLayoutWithItems(final long layoutId,
      final Continuation<? super WindowLayoutDao.LayoutWithItems> $completion) {
    final String _sql = "SELECT * FROM window_layouts WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, layoutId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, true, _cancellationSignal, new Callable<WindowLayoutDao.LayoutWithItems>() {
      @Override
      @Nullable
      public WindowLayoutDao.LayoutWithItems call() throws Exception {
        __db.beginTransaction();
        try {
          final Cursor _cursor = DBUtil.query(__db, _statement, true, null);
          try {
            final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
            final int _cursorIndexOfLayoutName = CursorUtil.getColumnIndexOrThrow(_cursor, "layoutName");
            final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
            final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
            final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
            final int _cursorIndexOfWindowCount = CursorUtil.getColumnIndexOrThrow(_cursor, "windowCount");
            final int _cursorIndexOfSortOrder = CursorUtil.getColumnIndexOrThrow(_cursor, "sortOrder");
            final int _cursorIndexOfIsApplied = CursorUtil.getColumnIndexOrThrow(_cursor, "isApplied");
            final LongSparseArray<ArrayList<WindowLayoutItemEntity>> _collectionItems = new LongSparseArray<ArrayList<WindowLayoutItemEntity>>();
            while (_cursor.moveToNext()) {
              final long _tmpKey;
              _tmpKey = _cursor.getLong(_cursorIndexOfId);
              if (!_collectionItems.containsKey(_tmpKey)) {
                _collectionItems.put(_tmpKey, new ArrayList<WindowLayoutItemEntity>());
              }
            }
            _cursor.moveToPosition(-1);
            __fetchRelationshipwindowLayoutItemsAscomExampleCastappDatabaseEntityWindowLayoutItemEntity(_collectionItems);
            final WindowLayoutDao.LayoutWithItems _result;
            if (_cursor.moveToFirst()) {
              final WindowLayoutEntity _tmpLayout;
              final long _tmpId;
              _tmpId = _cursor.getLong(_cursorIndexOfId);
              final String _tmpLayoutName;
              if (_cursor.isNull(_cursorIndexOfLayoutName)) {
                _tmpLayoutName = null;
              } else {
                _tmpLayoutName = _cursor.getString(_cursorIndexOfLayoutName);
              }
              final String _tmpDescription;
              if (_cursor.isNull(_cursorIndexOfDescription)) {
                _tmpDescription = null;
              } else {
                _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
              }
              final Date _tmpCreatedAt;
              final Long _tmp;
              if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
                _tmp = null;
              } else {
                _tmp = _cursor.getLong(_cursorIndexOfCreatedAt);
              }
              _tmpCreatedAt = __dateConverter.fromTimestamp(_tmp);
              final Date _tmpUpdatedAt;
              final Long _tmp_1;
              if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
                _tmp_1 = null;
              } else {
                _tmp_1 = _cursor.getLong(_cursorIndexOfUpdatedAt);
              }
              _tmpUpdatedAt = __dateConverter.fromTimestamp(_tmp_1);
              final int _tmpWindowCount;
              _tmpWindowCount = _cursor.getInt(_cursorIndexOfWindowCount);
              final int _tmpSortOrder;
              _tmpSortOrder = _cursor.getInt(_cursorIndexOfSortOrder);
              final boolean _tmpIsApplied;
              final int _tmp_2;
              _tmp_2 = _cursor.getInt(_cursorIndexOfIsApplied);
              _tmpIsApplied = _tmp_2 != 0;
              _tmpLayout = new WindowLayoutEntity(_tmpId,_tmpLayoutName,_tmpDescription,_tmpCreatedAt,_tmpUpdatedAt,_tmpWindowCount,_tmpSortOrder,_tmpIsApplied);
              final ArrayList<WindowLayoutItemEntity> _tmpItemsCollection;
              final long _tmpKey_1;
              _tmpKey_1 = _cursor.getLong(_cursorIndexOfId);
              _tmpItemsCollection = _collectionItems.get(_tmpKey_1);
              _result = new WindowLayoutDao.LayoutWithItems(_tmpLayout,_tmpItemsCollection);
            } else {
              _result = null;
            }
            __db.setTransactionSuccessful();
            return _result;
          } finally {
            _cursor.close();
            _statement.release();
          }
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object getLayoutsWithItemCount(
      final Continuation<? super List<WindowLayoutDao.LayoutWithItemCount>> $completion) {
    final String _sql = "\n"
            + "        SELECT l.*, COUNT(i.id) as actualWindowCount \n"
            + "        FROM window_layouts l \n"
            + "        LEFT JOIN window_layout_items i ON l.id = i.layoutId \n"
            + "        GROUP BY l.id \n"
            + "        ORDER BY l.createdAt DESC\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<WindowLayoutDao.LayoutWithItemCount>>() {
      @Override
      @NonNull
      public List<WindowLayoutDao.LayoutWithItemCount> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfLayoutName = CursorUtil.getColumnIndexOrThrow(_cursor, "layoutName");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfWindowCount = CursorUtil.getColumnIndexOrThrow(_cursor, "windowCount");
          final int _cursorIndexOfSortOrder = CursorUtil.getColumnIndexOrThrow(_cursor, "sortOrder");
          final int _cursorIndexOfIsApplied = CursorUtil.getColumnIndexOrThrow(_cursor, "isApplied");
          final int _cursorIndexOfActualWindowCount = CursorUtil.getColumnIndexOrThrow(_cursor, "actualWindowCount");
          final List<WindowLayoutDao.LayoutWithItemCount> _result = new ArrayList<WindowLayoutDao.LayoutWithItemCount>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final WindowLayoutDao.LayoutWithItemCount _item;
            final int _tmpActualWindowCount;
            _tmpActualWindowCount = _cursor.getInt(_cursorIndexOfActualWindowCount);
            final WindowLayoutEntity _tmpLayout;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpLayoutName;
            if (_cursor.isNull(_cursorIndexOfLayoutName)) {
              _tmpLayoutName = null;
            } else {
              _tmpLayoutName = _cursor.getString(_cursorIndexOfLayoutName);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final Date _tmpCreatedAt;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = __dateConverter.fromTimestamp(_tmp);
            final Date _tmpUpdatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            _tmpUpdatedAt = __dateConverter.fromTimestamp(_tmp_1);
            final int _tmpWindowCount;
            _tmpWindowCount = _cursor.getInt(_cursorIndexOfWindowCount);
            final int _tmpSortOrder;
            _tmpSortOrder = _cursor.getInt(_cursorIndexOfSortOrder);
            final boolean _tmpIsApplied;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsApplied);
            _tmpIsApplied = _tmp_2 != 0;
            _tmpLayout = new WindowLayoutEntity(_tmpId,_tmpLayoutName,_tmpDescription,_tmpCreatedAt,_tmpUpdatedAt,_tmpWindowCount,_tmpSortOrder,_tmpIsApplied);
            _item = new WindowLayoutDao.LayoutWithItemCount(_tmpLayout,_tmpActualWindowCount);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getLayoutCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM window_layouts";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getLayoutItemCount(final long layoutId,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM window_layout_items WHERE layoutId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, layoutId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }

  private void __fetchRelationshipwindowLayoutItemsAscomExampleCastappDatabaseEntityWindowLayoutItemEntity(
      @NonNull final LongSparseArray<ArrayList<WindowLayoutItemEntity>> _map) {
    if (_map.isEmpty()) {
      return;
    }
    if (_map.size() > RoomDatabase.MAX_BIND_PARAMETER_CNT) {
      RelationUtil.recursiveFetchLongSparseArray(_map, true, (map) -> {
        __fetchRelationshipwindowLayoutItemsAscomExampleCastappDatabaseEntityWindowLayoutItemEntity(map);
        return Unit.INSTANCE;
      });
      return;
    }
    final StringBuilder _stringBuilder = StringUtil.newStringBuilder();
    _stringBuilder.append("SELECT `id`,`layoutId`,`orderIndex`,`deviceName`,`deviceId`,`ipAddress`,`port`,`positionX`,`positionY`,`scaleFactor`,`rotationAngle`,`isCropping`,`isDragEnabled`,`isScaleEnabled`,`isRotationEnabled`,`isVisible`,`isMirrored`,`cornerRadius`,`alpha`,`isControlEnabled`,`isBorderEnabled`,`borderColor`,`borderWidth`,`cropRect`,`baseWindowWidth`,`baseWindowHeight`,`note`,`textContent`,`textIsBold`,`textIsItalic`,`textFontSize`,`textFontName`,`textFontFamily`,`textLineSpacing`,`textAlignment`,`richTextData`,`windowColorEnabled`,`windowBackgroundColor`,`mediaFileUri`,`mediaFileName`,`mediaContentType`,`videoPlayEnabled`,`videoLoopCount`,`videoVolume`,`isLandscapeModeEnabled` FROM `window_layout_items` WHERE `layoutId` IN (");
    final int _inputSize = _map.size();
    StringUtil.appendPlaceholders(_stringBuilder, _inputSize);
    _stringBuilder.append(")");
    final String _sql = _stringBuilder.toString();
    final int _argCount = 0 + _inputSize;
    final RoomSQLiteQuery _stmt = RoomSQLiteQuery.acquire(_sql, _argCount);
    int _argIndex = 1;
    for (int i = 0; i < _map.size(); i++) {
      final long _item = _map.keyAt(i);
      _stmt.bindLong(_argIndex, _item);
      _argIndex++;
    }
    final Cursor _cursor = DBUtil.query(__db, _stmt, false, null);
    try {
      final int _itemKeyIndex = CursorUtil.getColumnIndex(_cursor, "layoutId");
      if (_itemKeyIndex == -1) {
        return;
      }
      final int _cursorIndexOfId = 0;
      final int _cursorIndexOfLayoutId = 1;
      final int _cursorIndexOfOrderIndex = 2;
      final int _cursorIndexOfDeviceName = 3;
      final int _cursorIndexOfDeviceId = 4;
      final int _cursorIndexOfIpAddress = 5;
      final int _cursorIndexOfPort = 6;
      final int _cursorIndexOfPositionX = 7;
      final int _cursorIndexOfPositionY = 8;
      final int _cursorIndexOfScaleFactor = 9;
      final int _cursorIndexOfRotationAngle = 10;
      final int _cursorIndexOfIsCropping = 11;
      final int _cursorIndexOfIsDragEnabled = 12;
      final int _cursorIndexOfIsScaleEnabled = 13;
      final int _cursorIndexOfIsRotationEnabled = 14;
      final int _cursorIndexOfIsVisible = 15;
      final int _cursorIndexOfIsMirrored = 16;
      final int _cursorIndexOfCornerRadius = 17;
      final int _cursorIndexOfAlpha = 18;
      final int _cursorIndexOfIsControlEnabled = 19;
      final int _cursorIndexOfIsBorderEnabled = 20;
      final int _cursorIndexOfBorderColor = 21;
      final int _cursorIndexOfBorderWidth = 22;
      final int _cursorIndexOfCropRect = 23;
      final int _cursorIndexOfBaseWindowWidth = 24;
      final int _cursorIndexOfBaseWindowHeight = 25;
      final int _cursorIndexOfNote = 26;
      final int _cursorIndexOfTextContent = 27;
      final int _cursorIndexOfTextIsBold = 28;
      final int _cursorIndexOfTextIsItalic = 29;
      final int _cursorIndexOfTextFontSize = 30;
      final int _cursorIndexOfTextFontName = 31;
      final int _cursorIndexOfTextFontFamily = 32;
      final int _cursorIndexOfTextLineSpacing = 33;
      final int _cursorIndexOfTextAlignment = 34;
      final int _cursorIndexOfRichTextData = 35;
      final int _cursorIndexOfWindowColorEnabled = 36;
      final int _cursorIndexOfWindowBackgroundColor = 37;
      final int _cursorIndexOfMediaFileUri = 38;
      final int _cursorIndexOfMediaFileName = 39;
      final int _cursorIndexOfMediaContentType = 40;
      final int _cursorIndexOfVideoPlayEnabled = 41;
      final int _cursorIndexOfVideoLoopCount = 42;
      final int _cursorIndexOfVideoVolume = 43;
      final int _cursorIndexOfIsLandscapeModeEnabled = 44;
      while (_cursor.moveToNext()) {
        final long _tmpKey;
        _tmpKey = _cursor.getLong(_itemKeyIndex);
        final ArrayList<WindowLayoutItemEntity> _tmpRelation = _map.get(_tmpKey);
        if (_tmpRelation != null) {
          final WindowLayoutItemEntity _item_1;
          final long _tmpId;
          _tmpId = _cursor.getLong(_cursorIndexOfId);
          final long _tmpLayoutId;
          _tmpLayoutId = _cursor.getLong(_cursorIndexOfLayoutId);
          final int _tmpOrderIndex;
          _tmpOrderIndex = _cursor.getInt(_cursorIndexOfOrderIndex);
          final String _tmpDeviceName;
          if (_cursor.isNull(_cursorIndexOfDeviceName)) {
            _tmpDeviceName = null;
          } else {
            _tmpDeviceName = _cursor.getString(_cursorIndexOfDeviceName);
          }
          final String _tmpDeviceId;
          if (_cursor.isNull(_cursorIndexOfDeviceId)) {
            _tmpDeviceId = null;
          } else {
            _tmpDeviceId = _cursor.getString(_cursorIndexOfDeviceId);
          }
          final String _tmpIpAddress;
          if (_cursor.isNull(_cursorIndexOfIpAddress)) {
            _tmpIpAddress = null;
          } else {
            _tmpIpAddress = _cursor.getString(_cursorIndexOfIpAddress);
          }
          final int _tmpPort;
          _tmpPort = _cursor.getInt(_cursorIndexOfPort);
          final float _tmpPositionX;
          _tmpPositionX = _cursor.getFloat(_cursorIndexOfPositionX);
          final float _tmpPositionY;
          _tmpPositionY = _cursor.getFloat(_cursorIndexOfPositionY);
          final float _tmpScaleFactor;
          _tmpScaleFactor = _cursor.getFloat(_cursorIndexOfScaleFactor);
          final float _tmpRotationAngle;
          _tmpRotationAngle = _cursor.getFloat(_cursorIndexOfRotationAngle);
          final boolean _tmpIsCropping;
          final int _tmp;
          _tmp = _cursor.getInt(_cursorIndexOfIsCropping);
          _tmpIsCropping = _tmp != 0;
          final boolean _tmpIsDragEnabled;
          final int _tmp_1;
          _tmp_1 = _cursor.getInt(_cursorIndexOfIsDragEnabled);
          _tmpIsDragEnabled = _tmp_1 != 0;
          final boolean _tmpIsScaleEnabled;
          final int _tmp_2;
          _tmp_2 = _cursor.getInt(_cursorIndexOfIsScaleEnabled);
          _tmpIsScaleEnabled = _tmp_2 != 0;
          final boolean _tmpIsRotationEnabled;
          final int _tmp_3;
          _tmp_3 = _cursor.getInt(_cursorIndexOfIsRotationEnabled);
          _tmpIsRotationEnabled = _tmp_3 != 0;
          final boolean _tmpIsVisible;
          final int _tmp_4;
          _tmp_4 = _cursor.getInt(_cursorIndexOfIsVisible);
          _tmpIsVisible = _tmp_4 != 0;
          final boolean _tmpIsMirrored;
          final int _tmp_5;
          _tmp_5 = _cursor.getInt(_cursorIndexOfIsMirrored);
          _tmpIsMirrored = _tmp_5 != 0;
          final float _tmpCornerRadius;
          _tmpCornerRadius = _cursor.getFloat(_cursorIndexOfCornerRadius);
          final float _tmpAlpha;
          _tmpAlpha = _cursor.getFloat(_cursorIndexOfAlpha);
          final boolean _tmpIsControlEnabled;
          final int _tmp_6;
          _tmp_6 = _cursor.getInt(_cursorIndexOfIsControlEnabled);
          _tmpIsControlEnabled = _tmp_6 != 0;
          final boolean _tmpIsBorderEnabled;
          final int _tmp_7;
          _tmp_7 = _cursor.getInt(_cursorIndexOfIsBorderEnabled);
          _tmpIsBorderEnabled = _tmp_7 != 0;
          final int _tmpBorderColor;
          _tmpBorderColor = _cursor.getInt(_cursorIndexOfBorderColor);
          final float _tmpBorderWidth;
          _tmpBorderWidth = _cursor.getFloat(_cursorIndexOfBorderWidth);
          final String _tmpCropRect;
          if (_cursor.isNull(_cursorIndexOfCropRect)) {
            _tmpCropRect = null;
          } else {
            _tmpCropRect = _cursor.getString(_cursorIndexOfCropRect);
          }
          final int _tmpBaseWindowWidth;
          _tmpBaseWindowWidth = _cursor.getInt(_cursorIndexOfBaseWindowWidth);
          final int _tmpBaseWindowHeight;
          _tmpBaseWindowHeight = _cursor.getInt(_cursorIndexOfBaseWindowHeight);
          final String _tmpNote;
          if (_cursor.isNull(_cursorIndexOfNote)) {
            _tmpNote = null;
          } else {
            _tmpNote = _cursor.getString(_cursorIndexOfNote);
          }
          final String _tmpTextContent;
          if (_cursor.isNull(_cursorIndexOfTextContent)) {
            _tmpTextContent = null;
          } else {
            _tmpTextContent = _cursor.getString(_cursorIndexOfTextContent);
          }
          final boolean _tmpTextIsBold;
          final int _tmp_8;
          _tmp_8 = _cursor.getInt(_cursorIndexOfTextIsBold);
          _tmpTextIsBold = _tmp_8 != 0;
          final boolean _tmpTextIsItalic;
          final int _tmp_9;
          _tmp_9 = _cursor.getInt(_cursorIndexOfTextIsItalic);
          _tmpTextIsItalic = _tmp_9 != 0;
          final int _tmpTextFontSize;
          _tmpTextFontSize = _cursor.getInt(_cursorIndexOfTextFontSize);
          final String _tmpTextFontName;
          if (_cursor.isNull(_cursorIndexOfTextFontName)) {
            _tmpTextFontName = null;
          } else {
            _tmpTextFontName = _cursor.getString(_cursorIndexOfTextFontName);
          }
          final String _tmpTextFontFamily;
          if (_cursor.isNull(_cursorIndexOfTextFontFamily)) {
            _tmpTextFontFamily = null;
          } else {
            _tmpTextFontFamily = _cursor.getString(_cursorIndexOfTextFontFamily);
          }
          final float _tmpTextLineSpacing;
          _tmpTextLineSpacing = _cursor.getFloat(_cursorIndexOfTextLineSpacing);
          final int _tmpTextAlignment;
          _tmpTextAlignment = _cursor.getInt(_cursorIndexOfTextAlignment);
          final String _tmpRichTextData;
          if (_cursor.isNull(_cursorIndexOfRichTextData)) {
            _tmpRichTextData = null;
          } else {
            _tmpRichTextData = _cursor.getString(_cursorIndexOfRichTextData);
          }
          final boolean _tmpWindowColorEnabled;
          final int _tmp_10;
          _tmp_10 = _cursor.getInt(_cursorIndexOfWindowColorEnabled);
          _tmpWindowColorEnabled = _tmp_10 != 0;
          final int _tmpWindowBackgroundColor;
          _tmpWindowBackgroundColor = _cursor.getInt(_cursorIndexOfWindowBackgroundColor);
          final String _tmpMediaFileUri;
          if (_cursor.isNull(_cursorIndexOfMediaFileUri)) {
            _tmpMediaFileUri = null;
          } else {
            _tmpMediaFileUri = _cursor.getString(_cursorIndexOfMediaFileUri);
          }
          final String _tmpMediaFileName;
          if (_cursor.isNull(_cursorIndexOfMediaFileName)) {
            _tmpMediaFileName = null;
          } else {
            _tmpMediaFileName = _cursor.getString(_cursorIndexOfMediaFileName);
          }
          final String _tmpMediaContentType;
          if (_cursor.isNull(_cursorIndexOfMediaContentType)) {
            _tmpMediaContentType = null;
          } else {
            _tmpMediaContentType = _cursor.getString(_cursorIndexOfMediaContentType);
          }
          final boolean _tmpVideoPlayEnabled;
          final int _tmp_11;
          _tmp_11 = _cursor.getInt(_cursorIndexOfVideoPlayEnabled);
          _tmpVideoPlayEnabled = _tmp_11 != 0;
          final int _tmpVideoLoopCount;
          _tmpVideoLoopCount = _cursor.getInt(_cursorIndexOfVideoLoopCount);
          final int _tmpVideoVolume;
          _tmpVideoVolume = _cursor.getInt(_cursorIndexOfVideoVolume);
          final boolean _tmpIsLandscapeModeEnabled;
          final int _tmp_12;
          _tmp_12 = _cursor.getInt(_cursorIndexOfIsLandscapeModeEnabled);
          _tmpIsLandscapeModeEnabled = _tmp_12 != 0;
          _item_1 = new WindowLayoutItemEntity(_tmpId,_tmpLayoutId,_tmpOrderIndex,_tmpDeviceName,_tmpDeviceId,_tmpIpAddress,_tmpPort,_tmpPositionX,_tmpPositionY,_tmpScaleFactor,_tmpRotationAngle,_tmpIsCropping,_tmpIsDragEnabled,_tmpIsScaleEnabled,_tmpIsRotationEnabled,_tmpIsVisible,_tmpIsMirrored,_tmpCornerRadius,_tmpAlpha,_tmpIsControlEnabled,_tmpIsBorderEnabled,_tmpBorderColor,_tmpBorderWidth,_tmpCropRect,_tmpBaseWindowWidth,_tmpBaseWindowHeight,_tmpNote,_tmpTextContent,_tmpTextIsBold,_tmpTextIsItalic,_tmpTextFontSize,_tmpTextFontName,_tmpTextFontFamily,_tmpTextLineSpacing,_tmpTextAlignment,_tmpRichTextData,_tmpWindowColorEnabled,_tmpWindowBackgroundColor,_tmpMediaFileUri,_tmpMediaFileName,_tmpMediaContentType,_tmpVideoPlayEnabled,_tmpVideoLoopCount,_tmpVideoVolume,_tmpIsLandscapeModeEnabled);
          _tmpRelation.add(_item_1);
        }
      }
    } finally {
      _cursor.close();
    }
  }
}
