package com.example.castapp.ui.windowsettings;

/**
 * 截图管理器
 * 负责投屏窗口的截图捕获和图像处理
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J4\u0010\u0003\u001a\u0004\u0018\u00010\u00042\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\t2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u0002J\u001a\u0010\r\u001a\u0004\u0018\u00010\u00042\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0006\u001a\u00020\u0007H\u0002J>\u0010\u000e\u001a\u0004\u0018\u00010\u00042\b\u0010\u000f\u001a\u0004\u0018\u00010\u00102\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\t2\b\u0010\u000b\u001a\u0004\u0018\u00010\f2\b\b\u0002\u0010\u0011\u001a\u00020\tJ>\u0010\u0012\u001a\u0004\u0018\u00010\u00042\b\u0010\u0013\u001a\u0004\u0018\u00010\u00142\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\t2\b\u0010\u000b\u001a\u0004\u0018\u00010\f2\b\b\u0002\u0010\u0011\u001a\u00020\tJ\u0012\u0010\u0015\u001a\u0004\u0018\u00010\u00042\u0006\u0010\u0013\u001a\u00020\u0014H\u0002J\u001a\u0010\u0016\u001a\u0004\u0018\u00010\u00042\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0017\u001a\u00020\fH\u0002\u00a8\u0006\u0018"}, d2 = {"Lcom/example/castapp/ui/windowsettings/ScreenshotManager;", "", "()V", "applyContainerTransformsToBitmap", "Landroid/graphics/Bitmap;", "sourceBitmap", "connectionId", "", "isCroppedWindow", "", "isMirrored", "cropRectRatio", "Landroid/graphics/RectF;", "applyMirrorToBitmap", "captureScreenshot", "textureView", "Landroid/view/TextureView;", "applyTransforms", "captureViewScreenshot", "view", "Landroid/view/View;", "captureViewToBitmap", "cropBitmapWithRatio", "cropRatio", "app_debug"})
public final class ScreenshotManager {
    
    public ScreenshotManager() {
        super();
    }
    
    /**
     * 捕获投屏窗口的最终显示画面（TextureView专用）
     * @param textureView TextureView实例
     * @param connectionId 连接ID
     * @param isCroppedWindow 是否为裁剪窗口
     * @param isMirrored 是否镜像
     * @param cropRectRatio 裁剪区域比例
     * @param applyTransforms 🎯 是否应用变换（裁剪、镜像），false时返回原始截图
     * @return 截图Bitmap，失败时返回null
     */
    @org.jetbrains.annotations.Nullable()
    public final android.graphics.Bitmap captureScreenshot(@org.jetbrains.annotations.Nullable()
    android.view.TextureView textureView, @org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean isCroppedWindow, boolean isMirrored, @org.jetbrains.annotations.Nullable()
    android.graphics.RectF cropRectRatio, boolean applyTransforms) {
        return null;
    }
    
    /**
     * 🎯 新增：捕获任意View的截图（支持ImageView、TextView等）
     * @param view 要截图的View
     * @param connectionId 连接ID
     * @param isCroppedWindow 是否为裁剪窗口
     * @param isMirrored 是否镜像
     * @param cropRectRatio 裁剪区域比例
     * @param applyTransforms 🎯 是否应用变换（裁剪、镜像），false时返回原始截图
     * @return 截图Bitmap，失败时返回null
     */
    @org.jetbrains.annotations.Nullable()
    public final android.graphics.Bitmap captureViewScreenshot(@org.jetbrains.annotations.Nullable()
    android.view.View view, @org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean isCroppedWindow, boolean isMirrored, @org.jetbrains.annotations.Nullable()
    android.graphics.RectF cropRectRatio, boolean applyTransforms) {
        return null;
    }
    
    /**
     * 🎯 将View转换为Bitmap的辅助方法
     */
    private final android.graphics.Bitmap captureViewToBitmap(android.view.View view) {
        return null;
    }
    
    /**
     * 应用容器变换状态到Bitmap
     * 按照TextureView变换的相同顺序应用变换：裁剪 -> 镜像
     */
    private final android.graphics.Bitmap applyContainerTransformsToBitmap(android.graphics.Bitmap sourceBitmap, java.lang.String connectionId, boolean isCroppedWindow, boolean isMirrored, android.graphics.RectF cropRectRatio) {
        return null;
    }
    
    /**
     * 对Bitmap应用镜像变换
     * @param sourceBitmap 原始Bitmap
     * @param connectionId 连接ID
     * @return 镜像后的Bitmap，失败时返回null
     */
    private final android.graphics.Bitmap applyMirrorToBitmap(android.graphics.Bitmap sourceBitmap, java.lang.String connectionId) {
        return null;
    }
    
    /**
     * 根据裁剪比例从Bitmap中提取裁剪区域
     * @param sourceBitmap 原始Bitmap
     * @param cropRatio 裁剪区域比例 (left, top, right, bottom 都是0.0-1.0的相对值)
     * @return 裁剪后的Bitmap，失败时返回null
     */
    private final android.graphics.Bitmap cropBitmapWithRatio(android.graphics.Bitmap sourceBitmap, android.graphics.RectF cropRatio) {
        return null;
    }
}