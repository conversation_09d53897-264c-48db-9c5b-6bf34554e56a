package com.example.castapp.rtp;

/**
 * Payload视图类 - 零拷贝优化核心
 *
 * 通过视图方式访问payload数据，避免不必要的数据拷贝
 * 支持ByteBuffer和ByteArray两种数据源
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0005\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\t\n\u0002\u0010\u0012\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\u0018\u0000 #2\u00020\u0001:\u0001#B\u0005\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u0006J\u0006\u0010\u0003\u001a\u00020\u0004J\u0006\u0010\u000f\u001a\u00020\u0004J\u0006\u0010\u0010\u001a\u00020\u0011J\u001e\u0010\u0012\u001a\u00020\u00112\u0006\u0010\u0013\u001a\u00020\u000b2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0006J\u001e\u0010\u0014\u001a\u00020\u00112\u0006\u0010\u0015\u001a\u00020\t2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0006J\u0006\u0010\u0016\u001a\u00020\u0006J\u0016\u0010\u0017\u001a\u00020\u00002\u0006\u0010\u0018\u001a\u00020\u00062\u0006\u0010\u0019\u001a\u00020\u0006J\r\u0010\u001a\u001a\u00020\u001bH\u0000\u00a2\u0006\u0002\b\u001cJ\b\u0010\u001d\u001a\u00020\u001eH\u0016J\u000e\u0010\u001f\u001a\u00020\u00112\u0006\u0010 \u001a\u00020!J\u0018\u0010\"\u001a\u00020\u00112\u0006\u0010 \u001a\u00020!2\u0006\u0010\u0013\u001a\u00020\u000bH\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\b\u001a\u0004\u0018\u00010\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\n\u001a\u0004\u0018\u00010\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006$"}, d2 = {"Lcom/example/castapp/rtp/PayloadView;", "", "()V", "isEmpty", "", "length", "", "offset", "sourceByteArrayDataView", "Lcom/example/castapp/network/DataView;", "sourceSmartDataView", "Lcom/example/castapp/network/SmartDataView;", "getByte", "", "index", "isNotEmpty", "setEmpty", "", "setView", "smartDataView", "setViewFromByteArray", "byteArrayDataView", "size", "subView", "subOffset", "subLength", "toByteArray", "", "toByteArray$app_debug", "toString", "", "writeTo", "targetBuffer", "Ljava/nio/ByteBuffer;", "writeToDirectArray", "Companion", "app_debug"})
public final class PayloadView {
    @org.jetbrains.annotations.NotNull()
    private static final com.example.castapp.rtp.PayloadView EMPTY = null;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.network.SmartDataView sourceSmartDataView;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.network.DataView sourceByteArrayDataView;
    private int offset = 0;
    private int length = 0;
    private boolean isEmpty = true;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.rtp.PayloadView.Companion Companion = null;
    
    public PayloadView() {
        super();
    }
    
    /**
     * 从SmartDataView创建视图（智能零拷贝）
     */
    public final void setView(@org.jetbrains.annotations.NotNull()
    com.example.castapp.network.SmartDataView smartDataView, int offset, int length) {
    }
    
    /**
     * 🚀 零拷贝优化：直接从ByteArray设置视图
     * 避免SmartBuffer的中间拷贝步骤
     */
    public final void setViewFromByteArray(@org.jetbrains.annotations.NotNull()
    com.example.castapp.network.DataView byteArrayDataView, int offset, int length) {
    }
    
    /**
     * 设置为空视图
     */
    public final void setEmpty() {
    }
    
    /**
     * 获取视图长度
     */
    public final int size() {
        return 0;
    }
    
    /**
     * 检查是否为空
     */
    public final boolean isEmpty() {
        return false;
    }
    
    /**
     * 检查是否非空
     */
    public final boolean isNotEmpty() {
        return false;
    }
    
    /**
     * 🚀 零拷贝写入：统一优化，移除向后兼容分支
     * 完全消除临时数组创建，直接内存操作
     */
    public final void writeTo(@org.jetbrains.annotations.NotNull()
    java.nio.ByteBuffer targetBuffer) {
    }
    
    /**
     * 🚀 直接数组访问：移除临时数组创建
     */
    private final void writeToDirectArray(java.nio.ByteBuffer targetBuffer, com.example.castapp.network.SmartDataView smartDataView) {
    }
    
    /**
     * 获取指定位置的字节（用于解析协议头）
     * 在RtpPacket.parseFuAInfo()中使用，用于解析FU-A头部信息
     */
    public final byte getByte(int index) {
        return 0;
    }
    
    /**
     * 创建子视图（零拷贝）
     * 在RtpPacket.getFuANalDataView()中使用，用于创建FU-A NAL数据的子视图
     */
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.rtp.PayloadView subView(int subOffset, int subLength) {
        return null;
    }
    
    /**
     * 🚀 简化ByteArray创建：移除向后兼容，统一处理
     * 注意：此方法会产生数据拷贝，优先使用writeTo方法
     */
    @org.jetbrains.annotations.NotNull()
    public final byte[] toByteArray$app_debug() {
        return null;
    }
    
    /**
     * 调试用字符串表示
     */
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0011\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/example/castapp/rtp/PayloadView$Companion;", "", "()V", "EMPTY", "Lcom/example/castapp/rtp/PayloadView;", "getEMPTY", "()Lcom/example/castapp/rtp/PayloadView;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.rtp.PayloadView getEMPTY() {
            return null;
        }
    }
}