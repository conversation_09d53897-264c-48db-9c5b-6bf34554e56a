package com.example.castapp.ui.view

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.util.TypedValue
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.widget.FrameLayout
import com.example.castapp.R
import com.example.castapp.model.WindowVisualizationData
import com.example.castapp.ui.windowsettings.TransformManager
import com.example.castapp.utils.AppLog
import kotlin.math.abs
import kotlin.math.atan2
import kotlin.math.roundToInt
import kotlin.math.sqrt

/**
 * 🪟 投屏窗口容器可视化View
 * 在远程接收端控制窗口中绘制投屏窗口容器的可视化框架
 * 🎯 重构：使用FrameLayout + 子View架构，每个窗口使用独立的View.clipBounds裁剪
 */
class WindowContainerVisualizationView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    // 🎯 移除Paint对象，现在在WindowVisualizationContainerView中处理绘制
    
    // 数据
    private var visualizationDataList: List<WindowVisualizationData> = emptyList()
    private var isVisualizationEnabled = false

    // 🎯 新增：窗口容器View映射表，用于管理每个窗口的独立View
    private val windowContainerViews = mutableMapOf<String, WindowVisualizationContainerView>()

    // 🎯 新增：裁剪控制按钮组
    private var cropControlButtons: android.widget.LinearLayout? = null

    // 🎯 按钮组位置保存（参考接收端CropManager）
    private var buttonAbsoluteX = -1f
    private var buttonAbsoluteY = -1f

    // 🎯 原始裁剪状态保存（用于取消操作恢复）
    private var originalCropRatioForCancel: RectF? = null

    // 📸 截图数据
    private var screenshotDataMap: Map<String, Bitmap> = emptyMap()
    private var isScreenshotMode = false

    // 🎯 拖动相关属性
    private var isDragging = false
    private var draggedWindowData: WindowVisualizationData? = null
    private var lastTouchX = 0f
    private var lastTouchY = 0f
    private var dragOffsetX = 0f
    private var dragOffsetY = 0f
    private val dragThreshold = 15f

    // 🎯 缩放相关属性
    var isScaling = false
        private set
    private var scaledWindowData: WindowVisualizationData? = null
    private var initialScaleFactor = 1.0f
    private var currentScaleFactor = 1.0f
    private var originalWindowWidth = 0f
    private var originalWindowHeight = 0f
    private var baseAbsoluteScaleFactor = 1.0f // 接收端的基础绝对缩放因子
    private var scaleEndTime = 0L
    private val scaleEndDelay = 50L // 🎯 优化：减少缩放结束延迟，提高响应性
    private var scaleGestureDetector: ScaleGestureDetector

    // 🎯 旋转相关属性
    private var isRotating = false
    private var rotatedWindowData: WindowVisualizationData? = null
    private var currentRotationAngle = 0f
    private var baseAbsoluteRotationAngle = 0f // 接收端的基础绝对旋转角度
    private var rotationEndTime = 0L
    private val rotationEndDelay = 100L // 旋转结束后100ms内不处理拖动
    private var rotationGestureDetector: RotationGestureDetector

    // 🎯 裁剪相关属性
    private var isCropping = false
    private var croppedWindowData: WindowVisualizationData? = null
    private var cropOverlay: CropVisualizationOverlay? = null

    // 🎯 移除绘制相关常量，现在在WindowVisualizationContainerView中处理

    // 🎯 拖动和缩放回调接口
    interface OnWindowDragListener {
        /**
         * 拖动开始
         * @param windowData 被拖动的窗口数据
         */
        fun onDragStart(windowData: WindowVisualizationData)

        /**
         * 拖动中
         * @param windowData 被拖动的窗口数据
         * @param newX 新的X坐标（远程控制窗口坐标系）
         * @param newY 新的Y坐标（远程控制窗口坐标系）
         */
        fun onDragMove(windowData: WindowVisualizationData, newX: Float, newY: Float)

        /**
         * 拖动结束
         * @param windowData 被拖动的窗口数据
         * @param finalX 最终X坐标（远程控制窗口坐标系）
         * @param finalY 最终Y坐标（远程控制窗口坐标系）
         */
        fun onDragEnd(windowData: WindowVisualizationData, finalX: Float, finalY: Float)

        /**
         * 缩放开始
         * @param windowData 被缩放的窗口数据
         */
        fun onScaleStart(windowData: WindowVisualizationData)

        /**
         * 缩放中
         * @param windowData 被缩放的窗口数据
         * @param scaleFactor 当前缩放因子
         */
        fun onScaleMove(windowData: WindowVisualizationData, scaleFactor: Float)

        /**
         * 缩放结束
         * @param windowData 被缩放的窗口数据
         * @param finalScaleFactor 最终缩放因子（绝对值）
         * @param finalX 缩放后的最终X坐标（远程控制窗口坐标系）
         * @param finalY 缩放后的最终Y坐标（远程控制窗口坐标系）
         */
        fun onScaleEnd(windowData: WindowVisualizationData, finalScaleFactor: Float, finalX: Float, finalY: Float)

        /**
         * 旋转开始
         * @param windowData 被旋转的窗口数据
         */
        fun onRotationStart(windowData: WindowVisualizationData)

        /**
         * 旋转中
         * @param windowData 被旋转的窗口数据
         * @param rotationAngle 当前旋转角度
         */
        fun onRotationMove(windowData: WindowVisualizationData, rotationAngle: Float)

        /**
         * 旋转结束
         * @param windowData 被旋转的窗口数据
         * @param finalRotationAngle 最终旋转角度（绝对值）
         * @param finalX 旋转后的最终X坐标（远程控制窗口坐标系）
         * @param finalY 旋转后的最终Y坐标（远程控制窗口坐标系）
         */
        fun onRotationEnd(windowData: WindowVisualizationData, finalRotationAngle: Float, finalX: Float, finalY: Float)



        /**
         * 裁剪模式开始
         * @param windowData 进入裁剪模式的窗口数据
         */
        fun onCropModeStart(windowData: WindowVisualizationData)

        /**
         * 裁剪区域变化
         * @param windowData 被裁剪的窗口数据
         * @param cropRatio 当前裁剪区域比例
         */
        fun onCropAreaChange(windowData: WindowVisualizationData, cropRatio: RectF)

        /**
         * 裁剪模式结束
         * @param windowData 被裁剪的窗口数据
         * @param finalCropRatio 最终裁剪区域比例，null表示取消裁剪
         * @param isCancel 是否为取消操作
         */
        fun onCropModeEnd(windowData: WindowVisualizationData, finalCropRatio: RectF?, isCancel: Boolean)
    }

    // 拖动监听器
    private var onWindowDragListener: OnWindowDragListener? = null

    init {
        // 🎯 初始化缩放和旋转手势检测器
        scaleGestureDetector = ScaleGestureDetector(context, ScaleGestureListener())
        rotationGestureDetector = RotationGestureDetector(RotationGestureListener())
        AppLog.d("【窗口可视化】WindowContainerVisualizationView 初始化完成")
    }
    
    // 🎯 移除setupPaints方法，现在在WindowVisualizationContainerView中处理绘制
    
    /**
     * 更新可视化数据
     * @param dataList 窗口可视化数据列表
     * 🎯 重构：管理子View而不是重绘Canvas
     */
    fun updateVisualizationData(dataList: List<WindowVisualizationData>) {
        // 🎯 修复：保留现有文字格式信息，避免重置为默认值
        val preservedDataList = dataList.map { newData ->
            val existingData = visualizationDataList.find { it.connectionId == newData.connectionId }
            if (existingData != null && isTextWindow(newData.connectionId)) {
                // 对于文字窗口，保留现有的格式信息
                AppLog.d("【窗口可视化】🎯 保留文字格式信息: ${newData.connectionId}")
                AppLog.d("【窗口可视化】🎯 现有文字内容: '${existingData.textContent}', 行间距: ${existingData.lineSpacing}, 对齐: ${existingData.textAlignment}")
                AppLog.d("【窗口可视化】🎯 新数据文字内容: '${newData.textContent}', 行间距: ${newData.lineSpacing}, 对齐: ${newData.textAlignment}")

                val preservedData = newData.copy(
                    textContent = existingData.textContent,
                    richTextData = existingData.richTextData,
                    isBold = existingData.isBold,
                    isItalic = existingData.isItalic,
                    fontSize = existingData.fontSize,
                    fontName = existingData.fontName,
                    fontFamily = existingData.fontFamily,
                    lineSpacing = existingData.lineSpacing,
                    textAlignment = existingData.textAlignment,
                    isWindowColorEnabled = existingData.isWindowColorEnabled,
                    windowBackgroundColor = existingData.windowBackgroundColor
                )

                AppLog.d("【窗口可视化】🎯 保留后文字内容: '${preservedData.textContent}', 行间距: ${preservedData.lineSpacing}, 对齐: ${preservedData.textAlignment}")
                preservedData
            } else {
                if (isTextWindow(newData.connectionId)) {
                    AppLog.d("【窗口可视化】🎯 新文字窗口，无现有数据可保留: ${newData.connectionId}")
                }
                newData
            }
        }

        this.visualizationDataList = preservedDataList.filter { it.shouldDisplay() }
        this.isVisualizationEnabled = visualizationDataList.isNotEmpty()

        AppLog.d("【窗口可视化】更新可视化数据: ${dataList.size} 个窗口，${visualizationDataList.size} 个可显示")
        AppLog.d("【窗口可视化】🎯 已保留现有文字格式信息，避免重置")

        // 🎯 管理子View：创建、更新或移除窗口容器View
        updateWindowContainerViews()
    }

    /**
     * 🎯 层级修复：管理窗口容器子View（优化层级排序逻辑）
     */
    private fun updateWindowContainerViews() {
        // 获取当前需要显示的窗口ID集合
        val currentWindowIds = visualizationDataList.map { it.connectionId }.toSet()

        // 移除不再需要的窗口View
        val viewsToRemove = windowContainerViews.keys - currentWindowIds
        viewsToRemove.forEach { windowId ->
            windowContainerViews[windowId]?.let { view ->
                removeView(view)
                windowContainerViews.remove(windowId)
                AppLog.d("【层级修复】移除窗口View: $windowId")
            }
        }

        // 🎯 层级修复：按层级排序（接收端实际逻辑：zOrder=1是最上层，zOrder越大越在下层）
        // 所以zOrder大的先添加（在底层），zOrder小的后添加（在上层）
        val sortedData = visualizationDataList.sortedByDescending { it.zOrder }

        // 🎯 层级修复：分别处理新建和更新，确保层级正确
        val newViews = mutableListOf<Pair<WindowVisualizationContainerView, WindowVisualizationData>>()
        val existingViews = mutableListOf<Pair<WindowVisualizationContainerView, WindowVisualizationData>>()

        // 分类处理窗口View
        sortedData.forEach { data ->
            val windowId = data.connectionId
            val existingView = windowContainerViews[windowId]

            if (existingView != null) {
                // 更新现有View
                existingView.setWindowData(data)

                // 📝 根据窗口类型设置内容
                if (isTextWindow(data.connectionId)) {
                    // 🎯 修复：不调用setTextContent，避免覆盖富文本格式
                    // 文字内容和格式通过setWindowData中的updateTextView设置
                    AppLog.d("【层级修复】更新文字窗口View: $windowId, zOrder=${data.zOrder}, 文字内容: ${data.textContent}")
                } else {
                    AppLog.d("【层级修复】更新截图窗口View: $windowId, zOrder=${data.zOrder}, 截图: ${data.screenshotBitmap != null}")
                }

                // 🎯 统一缩放处理：所有窗口都使用左上角作为缩放中心点
                existingView.scaleX = data.scaleFactor
                existingView.scaleY = data.scaleFactor
                existingView.pivotX = 0f  // 统一使用左上角作为缩放中心点
                existingView.pivotY = 0f

                // 🎯 关键修复：容器变换属性更新后，立即同步边框变换属性
                syncBorderTransformWithContainer(existingView)
                AppLog.d("【缩放同步修复】应用接收端缩放状态: scaleFactor=${data.scaleFactor}")
                existingViews.add(Pair(existingView, data))
            } else {
                // 创建新View
                val bounds = data.getVisualizationBounds()
                val newView = WindowVisualizationContainerView(context)

                // 🎯 修复：先设置layoutParams再设置数据
                val layoutParams = FrameLayout.LayoutParams(
                    bounds.width().roundToInt(),
                    bounds.height().roundToInt()
                )
                newView.layoutParams = layoutParams

                // 然后设置窗口数据
                newView.setWindowData(data)

                // 📝 根据窗口类型设置内容
                if (isTextWindow(data.connectionId)) {
                    // 🎯 修复：不调用setTextContent，避免覆盖富文本格式
                    // 文字内容和格式通过setWindowData中的updateTextView设置
                    AppLog.d("【层级修复】准备创建文字窗口View: $windowId, zOrder=${data.zOrder}, 文字内容: ${data.textContent}")
                } else {
                    AppLog.d("【层级修复】准备创建截图窗口View: $windowId, zOrder=${data.zOrder}, 截图: ${data.screenshotBitmap != null}")
                }

                // 🎯 统一缩放处理：所有窗口都使用左上角作为缩放中心点
                newView.scaleX = data.scaleFactor
                newView.scaleY = data.scaleFactor
                newView.pivotX = 0f  // 统一使用左上角作为缩放中心点
                newView.pivotY = 0f

                // 🎯 关键修复：新窗口变换属性设置后，需要延迟同步边框（等边框创建完成）
                newView.post {
                    syncBorderTransformWithContainer(newView)
                }
                AppLog.d("【缩放同步修复】新窗口应用接收端缩放状态: scaleFactor=${data.scaleFactor}")

                newViews.add(Pair(newView, data))
                windowContainerViews[windowId] = newView
            }
        }

        // 🎯 层级修复：先添加新View，再调整现有View的层级
        newViews.forEach { (view, data) ->
            addView(view)
            AppLog.d("【层级修复】新建窗口View已添加: ${data.connectionId}")
        }

        // 🎯 层级修复：调整现有View的层级顺序
        adjustExistingViewsLayerOrder(existingViews)

        AppLog.d("【层级修复】窗口管理完成，当前子View数量: ${childCount}")
        AppLog.d("【层级修复】添加顺序（先添加的在底层）: ${sortedData.map { "${it.getShortConnectionId()}(z=${it.zOrder})" }}")
        AppLog.d("【层级修复】显示层级（zOrder=1是最上层）: ${sortedData.reversed().map { "${it.getShortConnectionId()}(z=${it.zOrder})" }}")
    }

    /**
     * 🎯 层级修复：调整现有View的层级顺序
     */
    private fun adjustExistingViewsLayerOrder(existingViews: List<Pair<WindowVisualizationContainerView, WindowVisualizationData>>) {
        if (existingViews.isEmpty()) return

        // 接收端实际逻辑：zOrder=1是最上层，zOrder越大越在下层
        // 所以zOrder大的先调用bringToFront（移到底层），zOrder小的后调用bringToFront（移到上层）
        val sortedViews = existingViews.sortedByDescending { it.second.zOrder }

        // 使用bringToFront调整层级，zOrder大的先调用，zOrder小的后调用，确保zOrder小的最终在上层
        sortedViews.forEach { (view, data) ->
            view.bringToFront()
            AppLog.d("【层级修复】调整窗口层级: ${data.getShortConnectionId()}, zOrder=${data.zOrder}")
        }
    }

    /**
     * 清除可视化数据
     * 🎯 重构：清除所有子View
     */
    fun clearVisualization() {
        visualizationDataList = emptyList()
        isVisualizationEnabled = false

        // 🎯 移除所有窗口容器View
        windowContainerViews.values.forEach { view ->
            removeView(view)
        }
        windowContainerViews.clear()

        AppLog.d("【窗口可视化】清除可视化数据，移除所有子View")
    }

    /**
     * 🎯 设置窗口拖动监听器
     */
    fun setOnWindowDragListener(listener: OnWindowDragListener?) {
        this.onWindowDragListener = listener
        AppLog.d("【窗口可视化】设置拖动监听器: ${listener != null}")
    }

    // 🎯 移除onDraw方法，现在使用子View架构，不需要Canvas绘制
    
    // 🎯 移除drawWindowContainer方法，现在使用WindowVisualizationContainerView子View
    
    // 🎯 移除drawDeviceInfoText方法，现在在WindowVisualizationContainerView中处理

    // ========== 📸 截图功能 ==========

    /**
     * 📸 更新截图数据
     * 🎯 重构：更新子View中的截图数据
     */
    fun updateScreenshots(screenshotsData: List<Map<String, Any>>) {
        try {
            AppLog.d("📸 [WindowVisualization] 开始更新截图数据，输入数据数量: ${screenshotsData.size}")

            val newScreenshotMap = mutableMapOf<String, Bitmap>()

            screenshotsData.forEach { screenshotData ->
                val connectionId = screenshotData["connectionId"] as? String
                val screenshotBase64 = screenshotData["imageData"] as? String

                if (connectionId != null && screenshotBase64 != null) {
                    val bitmap = base64ToBitmap(screenshotBase64)
                    if (bitmap != null) {
                        newScreenshotMap[connectionId] = bitmap
                        AppLog.d("📸 [WindowVisualization] 解析截图成功: $connectionId, 尺寸: ${bitmap.width}x${bitmap.height}")
                    }
                }
            }

            screenshotDataMap = newScreenshotMap
            isScreenshotMode = screenshotDataMap.isNotEmpty()

            // 🎯 更新可视化数据，将截图信息添加到WindowVisualizationData中
            updateVisualizationDataWithScreenshots()

            AppLog.d("📸 [WindowVisualization] 截图数据更新完成: ${screenshotDataMap.size} 个截图")

        } catch (e: Exception) {
            AppLog.e("📸 [WindowVisualization] 更新截图数据失败", e)
        }
    }

    /**
     * 📝 更新文字内容数据
     * 🎯 重构：更新子View中的文字内容数据（包含富文本格式）
     */
    fun updateTextContents(textContentsData: List<Map<String, Any>>) {
        try {
            AppLog.d("📝 [WindowVisualization] 开始更新文字内容数据，输入数据数量: ${textContentsData.size}")

            val newTextFormatMap = mutableMapOf<String, Map<String, Any>>()

            textContentsData.forEach { textContentData ->
                val connectionId = textContentData["connectionId"] as? String
                val textContent = textContentData["textContent"] as? String

                if (connectionId != null && textContent != null) {
                    newTextFormatMap[connectionId] = textContentData

                    val hasRichText = textContentData.containsKey("richTextData")
                    AppLog.d("📝 [WindowVisualization] 解析文字格式成功: $connectionId, 内容: $textContent, 富文本: $hasRichText")
                }
            }

            // 🎯 更新可视化数据，将完整的文字格式信息添加到WindowVisualizationData中
            updateVisualizationDataWithTextFormats(newTextFormatMap)

            AppLog.d("📝 [WindowVisualization] 文字格式数据更新完成: ${newTextFormatMap.size} 个文字窗口")

        } catch (e: Exception) {
            AppLog.e("📝 [WindowVisualization] 更新文字内容数据失败", e)
        }
    }

    /**
     * 🎯 新增：更新可视化数据中的截图信息
     */
    private fun updateVisualizationDataWithScreenshots() {
        // 更新可视化数据，添加截图信息
        visualizationDataList = visualizationDataList.map { data ->
            val screenshot = screenshotDataMap[data.connectionId]
            data.copy(screenshotBitmap = screenshot)
        }

        // 重新更新子View
        updateWindowContainerViews()
    }

    /**
     * 🎯 更新可视化数据中的文字格式信息（包含富文本格式）
     */
    private fun updateVisualizationDataWithTextFormats(textFormatMap: Map<String, Map<String, Any>>) {
        // 更新可视化数据，添加完整的文字格式信息
        visualizationDataList = visualizationDataList.map { data ->
            val formatInfo = textFormatMap[data.connectionId]
            if (formatInfo != null) {
                AppLog.d("【窗口可视化】格式数据传递调试: connectionId=${data.connectionId}")
                AppLog.d("【窗口可视化】原始行间距: ${formatInfo["lineSpacing"]}, 类型: ${formatInfo["lineSpacing"]?.javaClass?.simpleName}")
                AppLog.d("【窗口可视化】原始对齐: ${formatInfo["textAlignment"]}, 类型: ${formatInfo["textAlignment"]?.javaClass?.simpleName}")

                // 🎯 修复：确保正确解析和传递格式数据
                val updatedData = data.copy(
                    textContent = formatInfo["textContent"] as? String ?: data.textContent,
                    richTextData = formatInfo["richTextData"] as? String ?: data.richTextData,
                    isBold = formatInfo["isBold"] as? Boolean ?: data.isBold,
                    isItalic = formatInfo["isItalic"] as? Boolean ?: data.isItalic,
                    fontSize = (formatInfo["fontSize"] as? Number)?.toInt() ?: data.fontSize,
                    fontName = formatInfo["fontName"] as? String ?: data.fontName,
                    fontFamily = formatInfo["fontFamily"] as? String ?: data.fontFamily,
                    lineSpacing = when (val spacing = formatInfo["lineSpacing"]) {
                        is Float -> spacing
                        is Double -> spacing.toFloat()
                        is Number -> spacing.toFloat()
                        else -> data.lineSpacing // 🎯 保留原有值而不是默认值
                    },
                    textAlignment = when (val alignment = formatInfo["textAlignment"]) {
                        is Int -> alignment
                        is Float -> alignment.toInt()
                        is Double -> alignment.toInt()
                        is Number -> alignment.toInt()
                        else -> data.textAlignment // 🎯 保留原有值而不是默认值
                    },
                    isWindowColorEnabled = formatInfo["isWindowColorEnabled"] as? Boolean ?: data.isWindowColorEnabled,
                    windowBackgroundColor = when (val color = formatInfo["windowBackgroundColor"]) {
                        is Int -> color
                        is Long -> color.toInt()
                        is Float -> color.toInt()
                        is Double -> color.toInt()
                        is Number -> color.toInt()
                        else -> data.windowBackgroundColor // 🎯 保留原有值而不是默认值
                    }
                )

                AppLog.d("【窗口可视化】🎯 格式更新完成: connectionId=${data.connectionId}")
                AppLog.d("【窗口可视化】🎯 更新后行间距: ${updatedData.lineSpacing}, 对齐: ${updatedData.textAlignment}")
                updatedData
            } else {
                data
            }
        }

        // 重新更新子View
        updateWindowContainerViews()
    }

    // 🎯 移除calculateScreenshotDestRect方法，现在在WindowVisualizationContainerView中处理

    /**
     * 📸 将Base64字符串转换为Bitmap
     * 🎯 优化：使用高质量解码选项
     */
    private fun base64ToBitmap(base64String: String): Bitmap? {
        return try {
            val decodedBytes = android.util.Base64.decode(base64String, android.util.Base64.NO_WRAP)

            // 🎯 使用BitmapFactory.Options优化解码质量
            val options = BitmapFactory.Options().apply {
                inPreferredConfig = Bitmap.Config.ARGB_8888  // 使用最高质量配置
                // inDither 已弃用，现代Android系统会自动处理抖动
                inScaled = false  // 禁用自动缩放
                inPremultiplied = true  // 启用预乘alpha，提升绘制性能
            }

            val bitmap = BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.size, options)

            if (bitmap != null) {
                AppLog.v("📸 [Base64解码] 成功，尺寸: ${bitmap.width}×${bitmap.height}, 配置: ${bitmap.config}")
            } else {
                AppLog.w("📸 [Base64解码] 失败，返回null")
            }

            bitmap
        } catch (e: Exception) {
            AppLog.e("📸 Base64转Bitmap失败", e)
            null
        }
    }

    /**
     * 🎯 dp转px工具方法（与接收端TransformHandler保持一致）
     */
    private fun dpToPx(dp: Float): Float {
        return TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            dp,
            context.resources.displayMetrics
        )
    }

    // ========== 🎯 触摸事件处理 ==========

    override fun onTouchEvent(event: MotionEvent): Boolean {
        if (!isVisualizationEnabled || visualizationDataList.isEmpty()) {
            return super.onTouchEvent(event)
        }

        // 🎯 如果正在裁剪模式，不处理其他手势
        if (isCropping) {
            return cropOverlay?.onTouchEvent(event) ?: false
        }



        // 🎯 处理缩放和旋转手势
        val scaleHandled = scaleGestureDetector.onTouchEvent(event)
        val rotationHandled = rotationGestureDetector.onTouchEvent(event)

        // 🎯 优化：更智能的手势冲突检测
        val currentTime = System.currentTimeMillis()
        val isRecentlyScaled = (currentTime - scaleEndTime < scaleEndDelay)
        val isRecentlyRotated = (currentTime - rotationEndTime < rotationEndDelay)

        // 如果正在进行手势操作，或者刚结束且检测到手势处理，则不处理拖动
        if (isScaling || isRotating ||
            ((isRecentlyScaled || isRecentlyRotated) && (scaleHandled || rotationHandled))) {
            return scaleHandled || rotationHandled
        }

        // 🎯 重构：处理拖动手势，现在基于子View架构
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                return handleTouchDownWithSubViews(event.x, event.y)
            }

            MotionEvent.ACTION_MOVE -> {
                return handleTouchMove(event.x, event.y)
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                return handleTouchUp(event.x, event.y)
            }
        }

        return scaleHandled || rotationHandled || super.onTouchEvent(event)
    }

    /**
     * 🎯 新增：基于子View架构的触摸按下事件处理
     */
    private fun handleTouchDownWithSubViews(x: Float, y: Float): Boolean {
        lastTouchX = x
        lastTouchY = y

        // 🎯 通过子View查找被点击的窗口
        val clickedView = findClickedWindowView(x, y)
        if (clickedView != null) {
            // 从子View获取对应的窗口数据
            val clickedWindow = visualizationDataList.find { data ->
                windowContainerViews[data.connectionId] == clickedView
            }

            if (clickedWindow != null) {
                draggedWindowData = clickedWindow

                // 计算拖动偏移（点击位置相对于窗口左上角的偏移）
                dragOffsetX = x - clickedWindow.visualizedX
                dragOffsetY = y - clickedWindow.visualizedY

                AppLog.d("【窗口拖动】检测到点击: ${clickedWindow.getShortConnectionId()}, 偏移: ($dragOffsetX, $dragOffsetY)")
                return true
            }
        }

        return false
    }

    /**
     * 🎯 新增：查找被点击的窗口子View
     */
    private fun findClickedWindowView(x: Float, y: Float): WindowVisualizationContainerView? {
        // 🎯 层级修复：按层级从上到下检测（接收端实际逻辑：zOrder=1是最上层，所以从小到大检测）
        val sortedViews = windowContainerViews.values.sortedBy { view ->
            visualizationDataList.find { data ->
                windowContainerViews[data.connectionId] == view
            }?.zOrder ?: Int.MAX_VALUE
        }

        for (view in sortedViews) {
            // 🎯 关键修复：考虑View层缩放对边界检测的影响
            val windowData = visualizationDataList.find { data ->
                windowContainerViews[data.connectionId] == view
            }

            // 🎯 统一缩放处理：所有窗口都考虑View层缩放，且缩放锚点为左上角
            val actualWidth = view.width * view.scaleX
            val actualHeight = view.height * view.scaleY

            val viewBounds = RectF(view.x, view.y, view.x + actualWidth, view.y + actualHeight)

            if (x >= viewBounds.left && x <= viewBounds.right &&
                y >= viewBounds.top && y <= viewBounds.bottom) {

                // 🎯 关键修复：如果View有clipBounds，检查是否在裁剪区域内
                val clipBounds = view.clipBounds
                if (clipBounds != null) {
                    val localX = x - view.x
                    val localY = y - view.y
                    if (localX < clipBounds.left || localX > clipBounds.right ||
                        localY < clipBounds.top || localY > clipBounds.bottom) {
                        // 🎯 修复：触摸点在被裁剪区域内，跳过该窗口继续检测下层窗口
                        AppLog.d("🎯 触摸点在被裁剪区域内，跳过窗口: 触摸点($localX, $localY), 裁剪区域: $clipBounds")
                        continue // 跳过该窗口，继续检测下一个窗口
                    }
                }

                // 🎯 触摸点在可见区域内（未裁剪或在裁剪区域内），返回该窗口
                AppLog.d("🎯 触摸点在可见区域内，选中窗口: ${windowData?.getShortConnectionId()}")
                AppLog.d("  实际边界: $viewBounds (原始: ${view.width}×${view.height}, 缩放: ${view.scaleX}×${view.scaleY})")
                return view
            }
        }

        return null
    }

    /**
     * 处理触摸移动事件
     */
    private fun handleTouchMove(x: Float, y: Float): Boolean {
        val draggedWindow = draggedWindowData ?: return false

        if (!isDragging) {
            // 检查是否达到拖动阈值
            val deltaX = x - lastTouchX
            val deltaY = y - lastTouchY
            val distance = sqrt(deltaX * deltaX + deltaY * deltaY)

            if (distance > dragThreshold) {
                isDragging = true
                onWindowDragListener?.onDragStart(draggedWindow)
                AppLog.d("【窗口拖动】开始拖动: ${draggedWindow.getShortConnectionId()}")
            }
        }

        if (isDragging) {
            // 计算新位置（减去拖动偏移，确保窗口左上角位置正确）
            val newX = x - dragOffsetX
            val newY = y - dragOffsetY

            // 更新可视化数据中的位置（创建新的数据对象）
            updateDraggedWindowPosition(draggedWindow, newX, newY)

            // 通知拖动监听器
            onWindowDragListener?.onDragMove(draggedWindow, newX, newY)

            // 重绘视图
            invalidate()

            AppLog.v("【窗口拖动】移动到: ($newX, $newY)")
        }

        return isDragging
    }

    /**
     * 处理触摸抬起事件
     */
    private fun handleTouchUp(x: Float, y: Float): Boolean {
        val draggedWindow = draggedWindowData
        val wasDragging = isDragging

        if (wasDragging && draggedWindow != null) {
            // 计算最终位置
            val finalX = x - dragOffsetX
            val finalY = y - dragOffsetY

            // 通知拖动结束
            onWindowDragListener?.onDragEnd(draggedWindow, finalX, finalY)

            AppLog.d("【窗口拖动】结束拖动: ${draggedWindow.getShortConnectionId()}, 最终位置: ($finalX, $finalY)")
        }

        // 重置拖动状态
        isDragging = false
        draggedWindowData = null
        dragOffsetX = 0f
        dragOffsetY = 0f

        return wasDragging
    }

    // 🎯 移除findClickedWindow方法，现在使用findClickedWindowView

    // 🎯 移除isPointInWindow方法，现在使用子View的点击检测

    /**
     * 判断是否为文字窗口
     */
    private fun isTextWindow(connectionId: String): Boolean {
        return connectionId.startsWith("text_")
    }

    /**
     * 更新被拖动窗口的位置
     * 🎯 关键修复：使用实时同步机制，避免拖动过程中边框消失
     */
    private fun updateDraggedWindowPosition(originalData: WindowVisualizationData, newX: Float, newY: Float) {
        // 创建新的可视化数据列表，更新被拖动窗口的位置
        visualizationDataList = visualizationDataList.map { data ->
            if (data.connectionId == originalData.connectionId) {
                // 创建新的数据对象，更新位置
                data.copy(
                    visualizedX = newX,
                    visualizedY = newY
                )
            } else {
                data
            }
        }

        // 更新拖动窗口引用
        draggedWindowData = visualizationDataList.find { it.connectionId == originalData.connectionId }

        // 🎯 关键修复：直接更新容器位置，然后同步边框，避免重新创建边框
        windowContainerViews[originalData.connectionId]?.let { containerView ->
            // 直接更新容器位置
            containerView.x = newX
            containerView.y = newY

            // 🎯 关键修复：实时同步边框位置，避免边框消失
            syncBorderTransformWithContainer(containerView)

            AppLog.v("【窗口拖动】容器和边框位置已实时同步: ${originalData.getShortConnectionId()}, 位置: ($newX, $newY)")
        }
    }

    // ========== 🎯 缩放手势监听器 ==========

    /**
     * 缩放手势监听器
     */
    private inner class ScaleGestureListener : ScaleGestureDetector.OnScaleGestureListener {

        override fun onScaleBegin(detector: ScaleGestureDetector): Boolean {
            if (!isVisualizationEnabled || visualizationDataList.isEmpty()) {
                return false
            }

            // 查找被缩放的窗口（基于缩放中心点）
            val focusX = detector.focusX
            val focusY = detector.focusY
            val scaledView = findClickedWindowView(focusX, focusY)
            val scaledWindow = scaledView?.let { view ->
                visualizationDataList.find { data ->
                    windowContainerViews[data.connectionId] == view
                }
            }

            if (scaledWindow != null) {
                isScaling = true
                scaledWindowData = scaledWindow
                initialScaleFactor = 1.0f
                currentScaleFactor = 1.0f

                // 保存原始尺寸
                originalWindowWidth = scaledWindow.visualizedWidth
                originalWindowHeight = scaledWindow.visualizedHeight

                // 🎯 修复缩放同步：统一使用接收端当前的实际缩放因子作为基础
                baseAbsoluteScaleFactor = scaledWindow.scaleFactor
                AppLog.d("【窗口缩放】使用接收端当前缩放因子作为基础: $baseAbsoluteScaleFactor")
                AppLog.d("【窗口缩放】基础缩放因子设置完成，将作为遥控端手势缩放的基础值")

                // 通知缩放开始
                onWindowDragListener?.onScaleStart(scaledWindow)

                // 🎯 统一缩放模式日志
                AppLog.d("【窗口缩放】窗口缩放开始: ${scaledWindow.getShortConnectionId()}")
                AppLog.d("【窗口缩放】统一使用左上角锚点缩放模式")

                AppLog.d("【窗口缩放】焦点: ($focusX, $focusY)")
                AppLog.d("【窗口缩放】基础绝对缩放因子: $baseAbsoluteScaleFactor")
                return true
            }

            return false
        }

        override fun onScale(detector: ScaleGestureDetector): Boolean {
            val scaledWindow = scaledWindowData ?: return false

            if (isScaling) {
                // 计算累积缩放因子
                currentScaleFactor *= detector.scaleFactor

                // 🎯 修复：使用与TransformManager相同的缩放范围限制
                currentScaleFactor = currentScaleFactor.coerceIn(
                    TransformManager.MIN_SCALE_FACTOR,
                    TransformManager.MAX_SCALE_FACTOR
                )

                // 更新可视化数据中的缩放（这里只是视觉效果，不发送消息）
                updateScaledWindowVisualization(scaledWindow, currentScaleFactor)

                // 通知缩放中
                onWindowDragListener?.onScaleMove(scaledWindow, currentScaleFactor)

                // 重绘视图
                invalidate()

                AppLog.v("【窗口缩放】缩放中: ${scaledWindow.getShortConnectionId()}, 因子: $currentScaleFactor")
            }

            return true
        }

        override fun onScaleEnd(detector: ScaleGestureDetector) {
            val scaledWindow = scaledWindowData
            val wasScaling = isScaling

            if (wasScaling && scaledWindow != null) {
                // 🎯 修复缩放同步：统一使用基础 × 相对的计算方式
                // 现在所有窗口类型都能正确传递缩放因子了
                val finalAbsoluteScaleFactor = baseAbsoluteScaleFactor * currentScaleFactor

                // 🎯 获取缩放后的最终位置（从更新后的可视化数据中获取）
                val updatedWindowData = visualizationDataList.find { it.connectionId == scaledWindow.connectionId }
                val finalX = updatedWindowData?.visualizedX ?: scaledWindow.visualizedX
                val finalY = updatedWindowData?.visualizedY ?: scaledWindow.visualizedY

                // 通知缩放结束，传递绝对缩放因子和最终位置
                onWindowDragListener?.onScaleEnd(scaledWindow, finalAbsoluteScaleFactor, finalX, finalY)

                AppLog.d("【窗口缩放】结束缩放: ${scaledWindow.getShortConnectionId()}")
                AppLog.d("【窗口缩放】统一左上角锚点缩放模式")
                AppLog.d("【窗口缩放】相对缩放因子: $currentScaleFactor")
                AppLog.d("【窗口缩放】基础绝对缩放因子: $baseAbsoluteScaleFactor")
                AppLog.d("【窗口缩放】最终绝对缩放因子: $finalAbsoluteScaleFactor")
                AppLog.d("【窗口缩放】最终位置: ($finalX, $finalY)")
            }

            // 🎯 优化：立即重置缩放状态，提高响应性
            resetScaleState()

            AppLog.d("【窗口缩放】缩放状态已重置，可以立即响应其他手势")
        }
    }

    /**
     * 更新被缩放窗口的可视化效果
     */
    private fun updateScaledWindowVisualization(originalData: WindowVisualizationData, scaleFactor: Float) {
        // 🎯 关键修复：使用容器级别的缩放变换，而不是改变View尺寸
        // 这样可以实现与接收端一致的"放大镜效果"

        // 更新缩放窗口引用
        scaledWindowData = originalData

        // 直接更新对应的View，使用容器级别的缩放
        windowContainerViews[originalData.connectionId]?.let { containerView ->
            // 🎯 关键修复：基于接收端基础缩放因子应用手势缩放
            // 最终缩放 = 基础缩放（接收端） × 手势缩放（遥控端）
            val finalScaleFactor = baseAbsoluteScaleFactor * scaleFactor
            containerView.scaleX = finalScaleFactor
            containerView.scaleY = finalScaleFactor

            // 🎯 修改：所有窗口都使用左上角作为缩放中心点，与接收端保持一致
            containerView.pivotX = 0f
            containerView.pivotY = 0f
            AppLog.v("【窗口缩放】所有窗口使用左上角缩放: pivot=(0, 0)")

            // 🎯 修改：所有窗口都使用左上角缩放，位置保持不变
            val newX = originalData.visualizedX
            val newY = originalData.visualizedY
            AppLog.v("【窗口缩放】所有窗口位置保持不变: ($newX, $newY)")

            // 设置位置
            containerView.x = newX
            containerView.y = newY

            // 🎯 关键修复：同步更新边框View的变换属性
            syncBorderTransformWithContainer(containerView)

            // 🎯 额外修复：强制刷新容器View，确保缩放过程中边框实时更新
            containerView.invalidate()

            AppLog.v("【窗口缩放】容器级别缩放已应用: ${originalData.getShortConnectionId()}")
            AppLog.v("  基础缩放因子: $baseAbsoluteScaleFactor")
            AppLog.v("  手势缩放因子: $scaleFactor")
            AppLog.v("  最终缩放因子: $finalScaleFactor")
            AppLog.v("  原始尺寸: ${originalWindowWidth}x${originalWindowHeight}")
            AppLog.v("  缩放后位置: ($newX, $newY)")
            AppLog.v("  缩放中心点: (${containerView.pivotX}, ${containerView.pivotY})")
        }

        AppLog.d("【窗口缩放】容器级别缩放效果已应用: ${originalData.getShortConnectionId()}")
        AppLog.d("  基础缩放: $baseAbsoluteScaleFactor × 手势缩放: $scaleFactor = 最终缩放: ${baseAbsoluteScaleFactor * scaleFactor}")
    }

    /**
     * 🎯 新增：重置缩放状态的统一方法
     */
    private fun resetScaleState() {
        isScaling = false
        scaledWindowData = null
        initialScaleFactor = 1.0f
        currentScaleFactor = 1.0f
        originalWindowWidth = 0f
        originalWindowHeight = 0f
        baseAbsoluteScaleFactor = 1.0f
        scaleEndTime = System.currentTimeMillis() // 记录缩放结束时间
    }

    // ========== 🎯 旋转手势监听器 ==========

    /**
     * 旋转手势监听器
     */
    private inner class RotationGestureListener : RotationGestureDetector.OnRotationGestureListener {

        override fun onRotationBegin(detector: RotationGestureDetector): Boolean {
            if (!isVisualizationEnabled || visualizationDataList.isEmpty()) {
                return false
            }

            // 查找被旋转的窗口（基于旋转中心点）
            val focusX = detector.focusX
            val focusY = detector.focusY
            val rotatedView = findClickedWindowView(focusX, focusY)
            val rotatedWindow = rotatedView?.let { view ->
                visualizationDataList.find { data ->
                    windowContainerViews[data.connectionId] == view
                }
            }

            if (rotatedWindow != null) {
                isRotating = true
                rotatedWindowData = rotatedWindow
                currentRotationAngle = 0f

                // 🎯 记录接收端的基础绝对旋转角度
                baseAbsoluteRotationAngle = rotatedWindow.rotationAngle

                // 通知旋转开始
                onWindowDragListener?.onRotationStart(rotatedWindow)

                AppLog.d("【窗口旋转】开始旋转: ${rotatedWindow.getShortConnectionId()}, 焦点: ($focusX, $focusY)")
                AppLog.d("【窗口旋转】基础绝对旋转角度: $baseAbsoluteRotationAngle")
                return true
            }

            return false
        }

        override fun onRotate(detector: RotationGestureDetector, rotationDelta: Float): Boolean {
            val rotatedWindow = rotatedWindowData ?: return false

            if (isRotating) {
                // 累积旋转角度
                currentRotationAngle += rotationDelta

                // 更新可视化数据中的旋转（这里只是视觉效果，不发送消息）
                updateRotatedWindowVisualization(rotatedWindow, currentRotationAngle)

                // 通知旋转中
                onWindowDragListener?.onRotationMove(rotatedWindow, currentRotationAngle)

                // 重绘视图
                invalidate()

                AppLog.v("【窗口旋转】旋转中: ${rotatedWindow.getShortConnectionId()}, 角度: $currentRotationAngle")
            }

            return true
        }

        override fun onRotationEnd(detector: RotationGestureDetector) {
            val rotatedWindow = rotatedWindowData
            val wasRotating = isRotating

            if (wasRotating && rotatedWindow != null) {
                // 🎯 计算最终的绝对旋转角度
                val finalAbsoluteRotationAngle = baseAbsoluteRotationAngle + currentRotationAngle

                // 🎯 获取旋转后的最终位置（从更新后的可视化数据中获取）
                val updatedWindowData = visualizationDataList.find { it.connectionId == rotatedWindow.connectionId }
                val finalX = updatedWindowData?.visualizedX ?: rotatedWindow.visualizedX
                val finalY = updatedWindowData?.visualizedY ?: rotatedWindow.visualizedY

                // 通知旋转结束，传递绝对旋转角度和最终位置
                onWindowDragListener?.onRotationEnd(rotatedWindow, finalAbsoluteRotationAngle, finalX, finalY)

                AppLog.d("【窗口旋转】结束旋转: ${rotatedWindow.getShortConnectionId()}")
                AppLog.d("【窗口旋转】相对旋转角度: $currentRotationAngle")
                AppLog.d("【窗口旋转】基础绝对旋转角度: $baseAbsoluteRotationAngle")
                AppLog.d("【窗口旋转】最终绝对旋转角度: $finalAbsoluteRotationAngle")
                AppLog.d("【窗口旋转】最终位置: ($finalX, $finalY)")
            }

            // 重置旋转状态
            isRotating = false
            rotatedWindowData = null
            currentRotationAngle = 0f
            baseAbsoluteRotationAngle = 0f
            rotationEndTime = System.currentTimeMillis() // 记录旋转结束时间
        }
    }

    /**
     * 更新被旋转窗口的可视化效果
     */
    private fun updateRotatedWindowVisualization(originalData: WindowVisualizationData, rotationDelta: Float) {
        // 创建新的可视化数据列表，更新被旋转窗口的角度
        visualizationDataList = visualizationDataList.map { data ->
            if (data.connectionId == originalData.connectionId) {
                // 计算新的旋转角度（基于基础角度）
                val newRotationAngle = baseAbsoluteRotationAngle + rotationDelta

                // 创建新的数据对象，更新旋转角度
                data.copy(rotationAngle = newRotationAngle)
            } else {
                data
            }
        }

        // 更新旋转窗口引用
        rotatedWindowData = visualizationDataList.find { it.connectionId == originalData.connectionId }

        // 🎯 关键修复：使用实时同步机制，避免旋转过程中边框重建
        windowContainerViews[originalData.connectionId]?.let { containerView ->
            // 🎯 设置旋转中心点为左上角，与接收端保持一致
            containerView.pivotX = 0f
            containerView.pivotY = 0f

            // 直接更新容器旋转角度
            containerView.rotation = baseAbsoluteRotationAngle + rotationDelta

            // 🎯 关键修复：实时同步边框旋转，避免边框重建
            syncBorderTransformWithContainer(containerView)

            // 强制刷新容器View
            containerView.invalidate()

            AppLog.v("【窗口旋转】容器和边框旋转已实时同步: ${originalData.getShortConnectionId()}, 旋转角度: ${baseAbsoluteRotationAngle + rotationDelta}")
        }

        AppLog.d("【窗口旋转】可视化数据已更新: ${originalData.getShortConnectionId()}, 旋转角度: $rotationDelta")
        AppLog.d("【窗口旋转】子View已更新，旋转效果已应用")
    }

    // ========== 🎯 自定义旋转手势检测器 ==========

    /**
     * 自定义旋转手势检测器（复用TransformHandler的实现）
     */
    private class RotationGestureDetector(private val listener: OnRotationGestureListener) {
        private var prevAngle = 0f
        private var isInProgress = false
        var focusX = 0f
            private set
        var focusY = 0f
            private set

        interface OnRotationGestureListener {
            fun onRotationBegin(detector: RotationGestureDetector): Boolean
            fun onRotate(detector: RotationGestureDetector, rotationDelta: Float): Boolean
            fun onRotationEnd(detector: RotationGestureDetector)
        }

        fun onTouchEvent(event: MotionEvent): Boolean {
            var handled = false
            when (event.action and MotionEvent.ACTION_MASK) {
                MotionEvent.ACTION_POINTER_DOWN -> {
                    if (event.pointerCount == 2) {
                        prevAngle = getAngle(event)
                        focusX = (event.getX(0) + event.getX(1)) / 2f
                        focusY = (event.getY(0) + event.getY(1)) / 2f
                        isInProgress = listener.onRotationBegin(this)
                        handled = true
                    }
                }
                MotionEvent.ACTION_MOVE -> {
                    if (isInProgress && event.pointerCount == 2) {
                        val newAngle = getAngle(event)
                        var angleDelta = newAngle - prevAngle

                        if (angleDelta > 180) angleDelta -= 360
                        if (angleDelta < -180) angleDelta += 360

                        if (abs(angleDelta) > 1f) {
                            listener.onRotate(this, angleDelta)
                            prevAngle = newAngle
                        }

                        focusX = (event.getX(0) + event.getX(1)) / 2f
                        focusY = (event.getY(0) + event.getY(1)) / 2f
                        handled = true
                    }
                }
                MotionEvent.ACTION_POINTER_UP -> {
                    if (isInProgress) {
                        listener.onRotationEnd(this)
                        isInProgress = false
                    }
                    handled = true
                }
            }
            return handled
        }

        private fun getAngle(event: MotionEvent): Float {
            val deltaX = event.getX(0) - event.getX(1)
            val deltaY = event.getY(0) - event.getY(1)
            return Math.toDegrees(atan2(deltaY.toDouble(), deltaX.toDouble())).toFloat()
        }
    }



    // ========== 🎯 裁剪模式管理 ==========

    /**
     * 🎯 新增：公共方法 - 根据连接ID进入裁剪模式
     * @param connectionId 窗口连接ID
     * @return 是否成功进入裁剪模式
     */
    fun enterCropModeByConnectionId(connectionId: String): Boolean {
        val windowData = visualizationDataList.find { it.connectionId == connectionId }
        return if (windowData != null) {
            enterCropMode(windowData)
            true
        } else {
            AppLog.w("【窗口裁剪】未找到连接ID对应的窗口: $connectionId")
            false
        }
    }

    /**
     * 🎯 新增：公共方法 - 退出当前裁剪模式
     * @param isCancel 是否为取消操作
     * @return 是否成功退出裁剪模式
     */
    fun exitCurrentCropMode(isCancel: Boolean = false): Boolean {
        return if (isCropping && croppedWindowData != null) {
            val currentCropRatio = if (!isCancel) {
                // 获取当前裁剪区域
                val windowView = windowContainerViews[croppedWindowData!!.connectionId]
                windowView?.cropOverlay?.getCropRectRatio()
            } else {
                // 取消时恢复原始裁剪状态
                croppedWindowData!!.cropRectRatio
            }
            exitCropMode(isCancel, currentCropRatio)
            true
        } else {
            AppLog.w("【窗口裁剪】当前没有活跃的裁剪模式")
            false
        }
    }

    /**
     * 进入裁剪模式
     * 🎯 重构：使用窗口容器内部的裁剪覆盖层，与接收端保持一致
     */
    private fun enterCropMode(windowData: WindowVisualizationData) {
        if (isCropping) {
            AppLog.d("【窗口裁剪】已经在裁剪模式中")
            return
        }

        isCropping = true
        croppedWindowData = windowData

        // 🎯 关键修复：像接收端一样，临时清除裁剪效果显示完整画面
        // 🎯 修复：保存进入裁剪模式时的原始状态（参考接收端CropManager）
        originalCropRatioForCancel = windowData.cropRectRatio?.let { RectF(it) } // 深拷贝
        val originalCropRatio = originalCropRatioForCancel

        // 临时更新可视化数据，清除裁剪状态以显示完整画面
        visualizationDataList = visualizationDataList.map { data ->
            if (data.connectionId == windowData.connectionId) {
                data.copy(
                    isCropping = false,
                    cropRectRatio = null
                )
            } else {
                data
            }
        }

        // 更新裁剪窗口引用和对应的子View
        croppedWindowData = visualizationDataList.find { it.connectionId == windowData.connectionId }
        val windowView = windowContainerViews[windowData.connectionId]



        if (windowView != null && croppedWindowData != null) {
            // 🎯 修复：在窗口容器内部创建裁剪覆盖层，与接收端保持一致
            windowView.startCropMode(originalCropRatio) { cropRatio, isCancel ->
                if (!isCancel && cropRatio != null) {
                    // 应用裁剪
                    exitCropMode(false, cropRatio)
                } else {
                    // 取消裁剪，恢复原始状态
                    exitCropMode(true, originalCropRatio)
                }
            }

            // 🎯 修复：在父容器中创建控制按钮组，与接收端保持一致
            createCropControlButtonsInParent(croppedWindowData!!)

            // 通知裁剪模式开始
            onWindowDragListener?.onCropModeStart(windowData)

            AppLog.d("【窗口裁剪】进入裁剪模式: ${windowData.getShortConnectionId()}")
        } else {
            AppLog.w("【窗口裁剪】未找到对应的窗口容器View: ${windowData.getShortConnectionId()}")
            isCropping = false
            croppedWindowData = null
        }
    }

    /**
     * 退出裁剪模式
     */
    private fun exitCropMode(isCancel: Boolean, finalCropRatio: RectF?) {
        if (!isCropping) {
            return
        }

        val windowData = croppedWindowData
        if (windowData != null) {
            // 🎯 结束窗口容器的裁剪模式
            val windowView = windowContainerViews[windowData.connectionId]
            windowView?.endCropMode(isCancel)

            // 🎯 修复：正确处理裁剪状态恢复
            if (!isCancel && finalCropRatio != null) {
                // 应用新的裁剪
                updateCroppedWindowVisualization(windowData, finalCropRatio)
                AppLog.d("【窗口裁剪】应用新裁剪: ${windowData.getShortConnectionId()}, 裁剪比例: $finalCropRatio")
            } else {
                // 🎯 关键修复：取消时恢复到进入裁剪模式前的原始状态
                // finalCropRatio 在取消时包含原始裁剪状态
                updateCroppedWindowVisualization(windowData, finalCropRatio)
                AppLog.d("【窗口裁剪】取消裁剪，恢复原始状态: ${windowData.getShortConnectionId()}, 原始裁剪: $finalCropRatio")
            }

            // 通知裁剪模式结束
            onWindowDragListener?.onCropModeEnd(windowData, finalCropRatio, isCancel)
        }

        // 🎯 移除裁剪控制按钮
        removeCropControlButtons()

        // 重置状态
        isCropping = false
        croppedWindowData = null

        AppLog.d("【窗口裁剪】退出裁剪模式: ${windowData?.getShortConnectionId()}, 取消: $isCancel")
    }

    /**
     * 🎯 新增：检查指定窗口是否处于裁剪模式
     */
    fun isWindowInCropMode(connectionId: String): Boolean {
        return isCropping && croppedWindowData?.connectionId == connectionId
    }

    /**
     * 🎯 新增：在父容器中创建裁剪控制按钮组（与接收端CropManager保持一致）
     */
    private fun createCropControlButtonsInParent(windowData: WindowVisualizationData) {
        // 获取父容器（远程控制窗口的根容器）
        val parentContainer = parent as? FrameLayout ?: return

        // 🎯 加载保存的按钮位置（参考接收端CropManager）
        loadButtonPosition(windowData.connectionId)

        // 使用XML布局创建裁剪控制按钮组
        val layoutInflater = android.view.LayoutInflater.from(context)
        cropControlButtons = layoutInflater.inflate(
            R.layout.crop_control_buttons,
            parentContainer,
            false
        ) as android.widget.LinearLayout

        // 🎯 计算按钮组位置（参考接收端CropManager的逻辑）
        val screenWidth = context.resources.displayMetrics.widthPixels
        val screenHeight = context.resources.displayMetrics.heightPixels
        val estimatedButtonWidth = dpToPx(120f).toInt()
        val estimatedButtonHeight = dpToPx(60f).toInt()

        // 如果有保存的位置，使用保存的位置；否则使用屏幕底部中央默认位置
        val buttonX = if (buttonAbsoluteX >= 0) {
            buttonAbsoluteX.coerceIn(0f, (screenWidth.toFloat() - estimatedButtonWidth.toFloat()))
        } else {
            (screenWidth.toFloat() - estimatedButtonWidth.toFloat()) / 2f  // 屏幕水平居中
        }

        val buttonY = if (buttonAbsoluteY >= 0) {
            buttonAbsoluteY.coerceIn(0f, (screenHeight.toFloat() - estimatedButtonHeight.toFloat()))
        } else {
            (screenHeight.toFloat() - estimatedButtonHeight.toFloat() - dpToPx(50f))  // 屏幕底部向上50dp
        }

        // 设置按钮组位置和基本属性
        cropControlButtons?.apply {
            layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.WRAP_CONTENT,
                FrameLayout.LayoutParams.WRAP_CONTENT
            )

            // 使用计算好的绝对位置
            x = buttonX
            y = buttonY
            elevation = 1000f // 确保显示在最上层
        }

        // 🎯 设置拖动功能（参考接收端CropManager）
        setupCropControlButtonsDrag(windowData.connectionId)

        // 设置设备信息显示
        val deviceInfoTextView = cropControlButtons?.findViewById<android.widget.TextView>(R.id.tv_device_info)
        deviceInfoTextView?.text = windowData.getShortConnectionId()

        // 设置按钮点击事件
        setupCropControlButtonsEvents()

        // 添加按钮容器到父容器
        cropControlButtons?.let { buttons ->
            parentContainer.addView(buttons)
            buttons.bringToFront()
            AppLog.d("【窗口裁剪】裁剪控制按钮组已添加到父容器")
        }
    }

    /**
     * 🎯 新增：设置裁剪控制按钮事件
     */
    private fun setupCropControlButtonsEvents() {
        cropControlButtons?.let { buttonsLayout ->
            // 重置按钮
            val resetButton = buttonsLayout.findViewById<android.widget.Button>(R.id.btn_reset)
            resetButton?.setOnClickListener {
                // 重置裁剪区域为全屏
                val windowView = croppedWindowData?.let { windowContainerViews[it.connectionId] }
                windowView?.let { view ->
                    view.cropOverlay?.setCropRectRatio(RectF(0f, 0f, 1f, 1f))
                }
                AppLog.d("【窗口裁剪】点击重置按钮")
            }

            // 取消按钮
            val cancelButton = buttonsLayout.findViewById<android.widget.Button>(R.id.btn_cancel)
            cancelButton?.setOnClickListener {
                // 🎯 修复：取消裁剪，恢复到进入裁剪模式前的原始状态（参考接收端行为）
                exitCropMode(true, originalCropRatioForCancel)
                AppLog.d("【窗口裁剪】点击取消按钮，恢复原始状态: $originalCropRatioForCancel")
            }

            // 应用按钮
            val applyButton = buttonsLayout.findViewById<android.widget.Button>(R.id.btn_apply)
            applyButton?.setOnClickListener {
                // 应用裁剪设置
                val windowView = croppedWindowData?.let { windowContainerViews[it.connectionId] }
                val finalCropRatio = windowView?.cropOverlay?.getCropRectRatio()

                AppLog.d("【窗口裁剪】点击应用按钮")
                AppLog.d("【窗口裁剪】窗口数据: ${croppedWindowData?.getShortConnectionId()}")
                AppLog.d("【窗口裁剪】窗口View: $windowView")
                AppLog.d("【窗口裁剪】裁剪覆盖层: ${windowView?.cropOverlay}")
                AppLog.d("【窗口裁剪】获取的裁剪比例: $finalCropRatio")

                exitCropMode(false, finalCropRatio)
            }
        }
    }

    /**
     * 🎯 新增：设置裁剪控制按钮拖动功能（完全参考接收端CropManager）
     */
    private fun setupCropControlButtonsDrag(connectionId: String) {
        cropControlButtons?.let { buttonsLayout ->
            var isDragging = false
            var lastTouchX = 0f
            var lastTouchY = 0f
            var initialTouchX = 0f
            var initialTouchY = 0f
            val touchSlop = android.view.ViewConfiguration.get(context).scaledTouchSlop

            buttonsLayout.setOnTouchListener { _, event ->
                when (event.action) {
                    android.view.MotionEvent.ACTION_DOWN -> {
                        initialTouchX = event.rawX
                        initialTouchY = event.rawY
                        lastTouchX = event.rawX
                        lastTouchY = event.rawY
                        isDragging = false
                        true
                    }
                    android.view.MotionEvent.ACTION_MOVE -> {
                        val deltaX = kotlin.math.abs(event.rawX - initialTouchX)
                        val deltaY = kotlin.math.abs(event.rawY - initialTouchY)

                        // 如果移动距离超过触摸阈值，开始拖动
                        if (!isDragging && (deltaX > touchSlop || deltaY > touchSlop)) {
                            isDragging = true
                        }

                        if (isDragging) {
                            val moveX = event.rawX - lastTouchX
                            val moveY = event.rawY - lastTouchY

                            buttonsLayout.x += moveX
                            buttonsLayout.y += moveY

                            lastTouchX = event.rawX
                            lastTouchY = event.rawY
                        }
                        true
                    }
                    android.view.MotionEvent.ACTION_UP, android.view.MotionEvent.ACTION_CANCEL -> {
                        if (isDragging) {
                            // 拖动结束，保存当前绝对位置
                            buttonAbsoluteX = buttonsLayout.x
                            buttonAbsoluteY = buttonsLayout.y
                            saveButtonPosition(connectionId)
                            AppLog.d("【窗口裁剪】按钮组拖动结束，保存位置: (${buttonAbsoluteX}, ${buttonAbsoluteY})")
                            isDragging = false
                        }
                        true
                    }
                    else -> false
                }
            }
        }
    }

    /**
     * 🎯 新增：保存按钮组位置到SharedPreferences（参考接收端CropManager）
     */
    private fun saveButtonPosition(connectionId: String) {
        if (connectionId.isEmpty()) return

        val sharedPrefs = context.getSharedPreferences("crop_button_positions", android.content.Context.MODE_PRIVATE)
        sharedPrefs.edit().apply {
            putFloat("${connectionId}_absolute_x", buttonAbsoluteX)
            putFloat("${connectionId}_absolute_y", buttonAbsoluteY)
            apply()
        }
        AppLog.d("【窗口裁剪】保存按钮组位置: $connectionId, 绝对坐标=(${buttonAbsoluteX}, ${buttonAbsoluteY})")
    }

    /**
     * 🎯 新增：从SharedPreferences加载按钮组位置（参考接收端CropManager）
     */
    private fun loadButtonPosition(connectionId: String) {
        if (connectionId.isEmpty()) return

        val sharedPrefs = context.getSharedPreferences("crop_button_positions", android.content.Context.MODE_PRIVATE)
        buttonAbsoluteX = sharedPrefs.getFloat("${connectionId}_absolute_x", -1f)
        buttonAbsoluteY = sharedPrefs.getFloat("${connectionId}_absolute_y", -1f)
        AppLog.d("【窗口裁剪】加载按钮组位置: $connectionId, 绝对坐标=(${buttonAbsoluteX}, ${buttonAbsoluteY})")
    }

    /**
     * 🎯 新增：移除裁剪控制按钮
     */
    private fun removeCropControlButtons() {
        cropControlButtons?.let { buttons ->
            // 从父容器中移除按钮
            val parentContainer = parent as? FrameLayout
            parentContainer?.removeView(buttons)
        }
        cropControlButtons = null
    }

    /**
     * 🎯 更新被裁剪窗口的可视化效果
     */
    private fun updateCroppedWindowVisualization(originalData: WindowVisualizationData, cropRatio: RectF?) {
        // 创建新的可视化数据列表，更新被裁剪窗口的裁剪状态
        visualizationDataList = visualizationDataList.map { data ->
            if (data.connectionId == originalData.connectionId) {
                // 创建新的数据对象，更新裁剪状态
                data.copy(
                    isCropping = cropRatio != null,
                    cropRectRatio = cropRatio
                )
            } else {
                data
            }
        }

        // 更新裁剪窗口引用
        croppedWindowData = visualizationDataList.find { it.connectionId == originalData.connectionId }

        // 🎯 关键修复：更新子View以应用裁剪效果
        updateWindowContainerViews()

        AppLog.d("【窗口裁剪】可视化数据已更新: ${originalData.getShortConnectionId()}, 裁剪状态: ${cropRatio != null}")
        AppLog.d("【窗口裁剪】子View已更新，裁剪效果已应用")
    }

    /**
     * 🎯 新增：获取当前可视化数据列表
     * 用于外部更新可视化数据
     */
    fun getVisualizationDataList(): List<WindowVisualizationData> = visualizationDataList.toList()

    /**
     * 🎯 关键修复：同步边框View与容器View的变换属性
     * 确保边框在缩放、旋转、移动时与窗口容器保持完全同步
     */
    private fun syncBorderTransformWithContainer(containerView: WindowVisualizationContainerView) {
        try {
            // 获取容器的边框View引用
            val borderViewRef = containerView.javaClass.getDeclaredField("borderViewRef").apply {
                isAccessible = true
            }.get(containerView) as? java.lang.ref.WeakReference<*>

            val borderView = borderViewRef?.get() as? android.view.View
            if (borderView != null) {
                // 🎯 关键修复：确保边框View可见
                if (borderView.visibility != android.view.View.VISIBLE) {
                    borderView.visibility = android.view.View.VISIBLE
                }

                // 同步所有变换属性
                borderView.x = containerView.x
                borderView.y = containerView.y
                borderView.translationX = containerView.translationX
                borderView.translationY = containerView.translationY
                borderView.scaleX = containerView.scaleX
                borderView.scaleY = containerView.scaleY
                borderView.rotation = containerView.rotation

                val leftMargin = (borderView.layoutParams as? FrameLayout.LayoutParams)?.leftMargin ?: 0
                val topMargin = (borderView.layoutParams as? FrameLayout.LayoutParams)?.topMargin ?: 0
                borderView.pivotX = containerView.pivotX - leftMargin.toFloat()
                borderView.pivotY = containerView.pivotY - topMargin.toFloat()

                // 🎯 关键修复：强制刷新边框View，确保缩放过程中实时更新
                borderView.invalidate()
                borderView.requestLayout()

                // 🎯 额外修复：强制父容器重新布局，确保边框变换立即生效
                (borderView.parent as? android.view.ViewGroup)?.invalidate()

                // 获取窗口数据用于日志
                val windowData = containerView.javaClass.getDeclaredField("windowData").apply {
                    isAccessible = true
                }.get(containerView) as? WindowVisualizationData

                AppLog.v("【边框同步】边框变换属性已同步并强制刷新: ${windowData?.getShortConnectionId()}, 缩放: ${borderView.scaleX}x${borderView.scaleY}")
            } else {
                AppLog.w("【边框同步】边框View为null，可能已被回收")
            }
        } catch (e: Exception) {
            AppLog.w("【边框同步】同步边框变换属性失败", e)
        }
    }

}
