package com.example.castapp.model;

/**
 * 远程接收端数据模型
 * 用于管理远程接收端设备信息
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\t\n\u0002\b\u001c\n\u0002\u0010\u0000\n\u0002\b\r\b\u0086\b\u0018\u0000 42\u00020\u0001:\u00014BY\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\u0003\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u0012\b\b\u0002\u0010\n\u001a\u00020\u000b\u0012\b\b\u0002\u0010\f\u001a\u00020\u000b\u0012\b\b\u0002\u0010\r\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u0006\u00a2\u0006\u0002\u0010\u000fJ\t\u0010\u001c\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010 \u001a\u00020\tH\u00c6\u0003J\t\u0010!\u001a\u00020\u000bH\u00c6\u0003J\t\u0010\"\u001a\u00020\u000bH\u00c6\u0003J\t\u0010#\u001a\u00020\u0006H\u00c6\u0003J\t\u0010$\u001a\u00020\u0006H\u00c6\u0003Jc\u0010%\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u00032\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\u000b2\b\b\u0002\u0010\r\u001a\u00020\u00062\b\b\u0002\u0010\u000e\u001a\u00020\u0006H\u00c6\u0001J\u0013\u0010&\u001a\u00020\t2\b\u0010\'\u001a\u0004\u0018\u00010(H\u00d6\u0003J\u0006\u0010)\u001a\u00020\u0003J\u0006\u0010*\u001a\u00020\u0003J\u0006\u0010+\u001a\u00020\u0003J\u0006\u0010,\u001a\u00020\tJ\t\u0010-\u001a\u00020\u0006H\u00d6\u0001J\t\u0010.\u001a\u00020\u0003H\u00d6\u0001J\u000e\u0010/\u001a\u00020\u00002\u0006\u00100\u001a\u00020\tJ\u0016\u00101\u001a\u00020\u00002\u0006\u00102\u001a\u00020\u00062\u0006\u00103\u001a\u00020\u0006R\u0011\u0010\f\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\u0007\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0013R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0013R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0016R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0011R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0011\u0010\u000e\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0019R\u0011\u0010\r\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0019\u00a8\u00065"}, d2 = {"Lcom/example/castapp/model/RemoteReceiverConnection;", "Ljava/io/Serializable;", "id", "", "ipAddress", "port", "", "deviceName", "isConnected", "", "lastConnectedTime", "", "createdTime", "screenWidth", "screenHeight", "(Ljava/lang/String;Ljava/lang/String;ILjava/lang/String;ZJJII)V", "getCreatedTime", "()J", "getDeviceName", "()Ljava/lang/String;", "getId", "getIpAddress", "()Z", "getLastConnectedTime", "getPort", "()I", "getScreenHeight", "getScreenWidth", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "", "getDisplayText", "getScreenResolutionText", "getStatusText", "hasValidScreenResolution", "hashCode", "toString", "withConnectionState", "connected", "withScreenResolution", "width", "height", "Companion", "app_debug"})
public final class RemoteReceiverConnection implements java.io.Serializable {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String id = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String ipAddress = null;
    private final int port = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String deviceName = null;
    private final boolean isConnected = false;
    private final long lastConnectedTime = 0L;
    private final long createdTime = 0L;
    private final int screenWidth = 0;
    private final int screenHeight = 0;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.model.RemoteReceiverConnection.Companion Companion = null;
    
    public RemoteReceiverConnection(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String ipAddress, int port, @org.jetbrains.annotations.NotNull()
    java.lang.String deviceName, boolean isConnected, long lastConnectedTime, long createdTime, int screenWidth, int screenHeight) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getIpAddress() {
        return null;
    }
    
    public final int getPort() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDeviceName() {
        return null;
    }
    
    public final boolean isConnected() {
        return false;
    }
    
    public final long getLastConnectedTime() {
        return 0L;
    }
    
    public final long getCreatedTime() {
        return 0L;
    }
    
    public final int getScreenWidth() {
        return 0;
    }
    
    public final int getScreenHeight() {
        return 0;
    }
    
    /**
     * 获取显示文本
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDisplayText() {
        return null;
    }
    
    /**
     * 获取连接状态文本
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getStatusText() {
        return null;
    }
    
    /**
     * 更新连接状态
     */
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.model.RemoteReceiverConnection withConnectionState(boolean connected) {
        return null;
    }
    
    /**
     * 更新屏幕分辨率
     */
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.model.RemoteReceiverConnection withScreenResolution(int width, int height) {
        return null;
    }
    
    /**
     * 获取屏幕分辨率文本
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getScreenResolutionText() {
        return null;
    }
    
    /**
     * 检查是否有有效的屏幕分辨率信息
     */
    public final boolean hasValidScreenResolution() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    public final int component3() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component4() {
        return null;
    }
    
    public final boolean component5() {
        return false;
    }
    
    public final long component6() {
        return 0L;
    }
    
    public final long component7() {
        return 0L;
    }
    
    public final int component8() {
        return 0;
    }
    
    public final int component9() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.model.RemoteReceiverConnection copy(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String ipAddress, int port, @org.jetbrains.annotations.NotNull()
    java.lang.String deviceName, boolean isConnected, long lastConnectedTime, long createdTime, int screenWidth, int screenHeight) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0016\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u0006J\u0006\u0010\b\u001a\u00020\u0006J\u000e\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\u0006J\u000e\u0010\f\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u0006\u00a8\u0006\u000e"}, d2 = {"Lcom/example/castapp/model/RemoteReceiverConnection$Companion;", "", "()V", "create", "Lcom/example/castapp/model/RemoteReceiverConnection;", "ipAddress", "", "deviceName", "generateId", "isValidDeviceName", "", "name", "isValidIpAddress", "ip", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * 生成接收端ID
         */
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String generateId() {
            return null;
        }
        
        /**
         * 创建新的远程接收端
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.model.RemoteReceiverConnection create(@org.jetbrains.annotations.NotNull()
        java.lang.String ipAddress, @org.jetbrains.annotations.NotNull()
        java.lang.String deviceName) {
            return null;
        }
        
        /**
         * 验证IP地址格式
         */
        public final boolean isValidIpAddress(@org.jetbrains.annotations.NotNull()
        java.lang.String ip) {
            return false;
        }
        
        /**
         * 验证设备名称
         */
        public final boolean isValidDeviceName(@org.jetbrains.annotations.NotNull()
        java.lang.String name) {
            return false;
        }
    }
}