package com.example.castapp.ui.windowsettings.interfaces;

/**
 * 变换状态监听接口
 * 用于监听投屏窗口的变换状态变化
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0004\bf\u0018\u00002\u00020\u0001J0\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\u00072\u0006\u0010\t\u001a\u00020\u00072\u0006\u0010\n\u001a\u00020\u0007H&\u00a8\u0006\u000b"}, d2 = {"Lcom/example/castapp/ui/windowsettings/interfaces/TransformStateListener;", "", "onTransformChanged", "", "connectionId", "", "x", "", "y", "scale", "rotation", "app_debug"})
public abstract interface TransformStateListener {
    
    /**
     * 变换状态发生变化时的回调
     * @param connectionId 连接ID
     * @param x 实际显示的X坐标（左上角）
     * @param y 实际显示的Y坐标（左上角）
     * @param scale 缩放倍数
     * @param rotation 旋转角度
     */
    public abstract void onTransformChanged(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, float x, float y, float scale, float rotation);
}