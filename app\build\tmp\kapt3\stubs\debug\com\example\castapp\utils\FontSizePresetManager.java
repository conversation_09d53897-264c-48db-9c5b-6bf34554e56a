package com.example.castapp.utils;

/**
 * 全局字号预设管理器
 * 负责管理所有文本窗口共享的字号预设列表
 * 提供持久化存储和实时同步功能
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010#\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\r\u0018\u0000 #2\u00020\u0001:\u0002#$B\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000fJ\u000e\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\tJ\u000e\u0010\u0013\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000fJ\u000e\u0010\u0014\u001a\u00020\u000f2\u0006\u0010\u0015\u001a\u00020\u000fJ\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00180\u0017J\f\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0017J\u000e\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0017H\u0002J\u000e\u0010\u001b\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000fJ\u0010\u0010\u001c\u001a\u00020\u00112\u0006\u0010\u000e\u001a\u00020\u000fH\u0002J\u0010\u0010\u001d\u001a\u00020\u00112\u0006\u0010\u000e\u001a\u00020\u000fH\u0002J\b\u0010\u001e\u001a\u00020\u0011H\u0002J\u000e\u0010\u001f\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\tJ\u0006\u0010 \u001a\u00020\u0011J\u0016\u0010!\u001a\u00020\u00112\f\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0017H\u0002R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006%"}, d2 = {"Lcom/example/castapp/utils/FontSizePresetManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "gson", "Lcom/google/gson/Gson;", "listeners", "", "Lcom/example/castapp/utils/FontSizePresetManager$FontSizePresetListener;", "sharedPreferences", "Landroid/content/SharedPreferences;", "addCustomFontSize", "", "fontSize", "", "addListener", "", "listener", "deleteCustomFontSize", "findClosestPresetFontSize", "targetFontSize", "getAllFontSizeOptions", "", "", "getAllFontSizes", "getCustomFontSizes", "isPresetFontSize", "notifyFontSizeAdded", "notifyFontSizeDeleted", "notifyFontSizeListReset", "removeListener", "resetToDefault", "saveCustomFontSizes", "customFontSizes", "Companion", "FontSizePresetListener", "app_debug"})
public final class FontSizePresetManager {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String PREF_NAME = "font_size_preset_preferences";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_CUSTOM_FONT_SIZES = "custom_font_sizes";
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<java.lang.Integer> PRESET_FONT_SIZES = null;
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.example.castapp.utils.FontSizePresetManager INSTANCE;
    @org.jetbrains.annotations.NotNull()
    private final android.content.SharedPreferences sharedPreferences = null;
    @org.jetbrains.annotations.NotNull()
    private final com.google.gson.Gson gson = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Set<com.example.castapp.utils.FontSizePresetManager.FontSizePresetListener> listeners = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.utils.FontSizePresetManager.Companion Companion = null;
    
    private FontSizePresetManager(android.content.Context context) {
        super();
    }
    
    /**
     * 获取完整的字号列表（预设 + 自定义）
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.Integer> getAllFontSizes() {
        return null;
    }
    
    /**
     * 获取字号选项字符串列表（用于Spinner显示）
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getAllFontSizeOptions() {
        return null;
    }
    
    /**
     * 获取自定义字号列表
     */
    private final java.util.List<java.lang.Integer> getCustomFontSizes() {
        return null;
    }
    
    /**
     * 保存自定义字号列表
     */
    private final void saveCustomFontSizes(java.util.List<java.lang.Integer> customFontSizes) {
    }
    
    /**
     * 添加自定义字号
     */
    public final boolean addCustomFontSize(int fontSize) {
        return false;
    }
    
    /**
     * 删除自定义字号
     */
    public final boolean deleteCustomFontSize(int fontSize) {
        return false;
    }
    
    /**
     * 重置为默认字号设置（清除所有自定义字号）
     */
    public final void resetToDefault() {
    }
    
    /**
     * 检查字号是否为预设字号
     */
    public final boolean isPresetFontSize(int fontSize) {
        return false;
    }
    
    /**
     * 添加字号列表变化监听器
     */
    public final void addListener(@org.jetbrains.annotations.NotNull()
    com.example.castapp.utils.FontSizePresetManager.FontSizePresetListener listener) {
    }
    
    /**
     * 移除字号列表变化监听器
     */
    public final void removeListener(@org.jetbrains.annotations.NotNull()
    com.example.castapp.utils.FontSizePresetManager.FontSizePresetListener listener) {
    }
    
    /**
     * 通知字号被添加
     */
    private final void notifyFontSizeAdded(int fontSize) {
    }
    
    /**
     * 通知字号被删除
     */
    private final void notifyFontSizeDeleted(int fontSize) {
    }
    
    /**
     * 通知字号列表被重置
     */
    private final void notifyFontSizeListReset() {
    }
    
    /**
     * 查找最接近的预设字号
     */
    public final int findClosestPresetFontSize(int targetFontSize) {
        return 0;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\r\u001a\u00020\u00042\u0006\u0010\u000e\u001a\u00020\u000fR\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\f\u00a8\u0006\u0010"}, d2 = {"Lcom/example/castapp/utils/FontSizePresetManager$Companion;", "", "()V", "INSTANCE", "Lcom/example/castapp/utils/FontSizePresetManager;", "KEY_CUSTOM_FONT_SIZES", "", "PREF_NAME", "PRESET_FONT_SIZES", "", "", "getPRESET_FONT_SIZES", "()Ljava/util/List;", "getInstance", "context", "Landroid/content/Context;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.lang.Integer> getPRESET_FONT_SIZES() {
            return null;
        }
        
        /**
         * 获取单例实例
         * 使用 applicationContext 避免内存泄漏
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.utils.FontSizePresetManager getInstance(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
            return null;
        }
    }
    
    /**
     * 字号预设列表变化监听器接口
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\u0010\u0010\u0006\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\b\u0010\u0007\u001a\u00020\u0003H&\u00a8\u0006\b"}, d2 = {"Lcom/example/castapp/utils/FontSizePresetManager$FontSizePresetListener;", "", "onFontSizeAdded", "", "fontSize", "", "onFontSizeDeleted", "onFontSizeListReset", "app_debug"})
    public static abstract interface FontSizePresetListener {
        
        /**
         * 字号被添加时调用
         */
        public abstract void onFontSizeAdded(int fontSize);
        
        /**
         * 字号被删除时调用
         */
        public abstract void onFontSizeDeleted(int fontSize);
        
        /**
         * 字号列表被重置时调用
         */
        public abstract void onFontSizeListReset();
    }
}