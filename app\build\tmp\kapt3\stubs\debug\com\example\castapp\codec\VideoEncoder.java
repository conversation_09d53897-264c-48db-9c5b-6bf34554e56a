package com.example.castapp.codec;

/**
 * H.264视频编码器
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000~\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u0012\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0015\u0018\u0000 G2\u00020\u0001:\u0003FGHB\u00c7\u0001\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0003\u0012\u0018\u0010\u0007\u001a\u0014\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\n0\b\u0012 \b\u0002\u0010\u000b\u001a\u001a\u0012\u0006\u0012\u0004\u0018\u00010\f\u0012\u0006\u0012\u0004\u0018\u00010\f\u0012\u0004\u0012\u00020\n\u0018\u00010\b\u0012,\b\u0002\u0010\r\u001a&\u0012\u0006\u0012\u0004\u0018\u00010\f\u0012\u0006\u0012\u0004\u0018\u00010\f\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\n\u0018\u00010\u000e\u00122\b\u0002\u0010\u000f\u001a,\u0012\u0006\u0012\u0004\u0018\u00010\f\u0012\u0006\u0012\u0004\u0018\u00010\f\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\n\u0018\u00010\u0010\u00a2\u0006\u0002\u0010\u0011J\u001a\u0010(\u001a\u0004\u0018\u00010\u001a2\u0006\u0010)\u001a\u00020*2\u0006\u0010+\u001a\u00020,H\u0002J\b\u0010-\u001a\u00020*H\u0002J\u000e\u0010.\u001a\u00020\n2\u0006\u0010/\u001a\u00020\u0003J\u000e\u00100\u001a\b\u0012\u0004\u0012\u00020,01H\u0002J\u0006\u00102\u001a\u00020\u0003J\u0016\u00103\u001a\u0012\u0012\u0006\u0012\u0004\u0018\u00010%\u0012\u0006\u0012\u0004\u0018\u00010%04J\u0010\u00105\u001a\u00020%2\u0006\u00106\u001a\u00020\u0003H\u0002J\u0010\u00107\u001a\u00020\u00152\u0006\u0010+\u001a\u00020,H\u0002J\u0006\u00108\u001a\u00020\u0015J\u0010\u00109\u001a\u00020\n2\u0006\u0010:\u001a\u00020 H\u0002J\u0018\u0010;\u001a\u0004\u0018\u00010,2\f\u0010<\u001a\b\u0012\u0004\u0012\u00020,01H\u0002J0\u0010=\u001a\u00020\n2(\u0010>\u001a$\u0012\u0006\u0012\u0004\u0018\u00010\f\u0012\u0006\u0012\u0004\u0018\u00010\f\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\n0\u000eJ\u0010\u0010?\u001a\u00020\n2\u0006\u0010)\u001a\u00020*H\u0002J\u000e\u0010@\u001a\u00020\n2\u0006\u00106\u001a\u00020\u0003J\b\u0010A\u001a\u0004\u0018\u00010\u001aJ\u0006\u0010B\u001a\u00020\nJ\u0016\u0010C\u001a\u0012\u0012\u0006\u0012\u0004\u0018\u00010 \u0012\u0006\u0012\u0004\u0018\u00010\u001a04J\u000e\u0010D\u001a\u00020\n2\u0006\u0010/\u001a\u00020\u0003J\u0018\u0010E\u001a\u00020\n2\u0006\u0010+\u001a\u00020,2\u0006\u0010)\u001a\u00020*H\u0002R\u000e\u0010\u0005\u001a\u00020\u0003X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0012\u001a\u0004\u0018\u00010\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0013\u001a\u0004\u0018\u00010\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0015X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0003X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0019\u001a\u0004\u0018\u00010\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u0015X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001e\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001f\u001a\u0004\u0018\u00010 X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010!\u001a\n #*\u0004\u0018\u00010\"0\"X\u0082\u0004\u00a2\u0006\u0002\n\u0000R&\u0010\u000b\u001a\u001a\u0012\u0006\u0012\u0004\u0018\u00010\f\u0012\u0006\u0012\u0004\u0018\u00010\f\u0012\u0004\u0012\u00020\n\u0018\u00010\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R8\u0010\u000f\u001a,\u0012\u0006\u0012\u0004\u0018\u00010\f\u0012\u0006\u0012\u0004\u0018\u00010\f\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\n\u0018\u00010\u0010X\u0082\u0004\u00a2\u0006\u0002\n\u0000R2\u0010\r\u001a&\u0012\u0006\u0012\u0004\u0018\u00010\f\u0012\u0006\u0012\u0004\u0018\u00010\f\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\n\u0018\u00010\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R \u0010\u0007\u001a\u0014\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\n0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010$\u001a\u0004\u0018\u00010%X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010&\u001a\u0004\u0018\u00010%X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\'\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006I"}, d2 = {"Lcom/example/castapp/codec/VideoEncoder;", "", "width", "", "height", "bitRate", "frameRate", "onEncodedData", "Lkotlin/Function2;", "Ljava/nio/ByteBuffer;", "", "onConfigurationData", "", "onConfigurationDataWithResolution", "Lkotlin/Function4;", "onConfigurationDataWithOrientation", "Lkotlin/Function5;", "(IIIILkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function4;Lkotlin/jvm/functions/Function5;)V", "cachedPpsData", "cachedSpsData", "configurationSent", "", "currentOrientation", "frameCount", "", "inputSurface", "Landroid/view/Surface;", "isRunning", "lastBitrateCheckTime", "lastDataSent", "lastFrameTime", "mediaCodec", "Landroid/media/MediaCodec;", "networkExecutor", "Ljava/util/concurrent/ExecutorService;", "kotlin.jvm.PlatformType", "selectedEncoderName", "", "selectedEncoderType", "totalDataSent", "createEncoderWithConfig", "format", "Landroid/media/MediaFormat;", "codecInfo", "Landroid/media/MediaCodecInfo;", "createStandardFormat", "forceUpdateBitRate", "newBitRate", "getAvailableHardwareEncoders", "", "getCurrentBitRate", "getEncoderInfo", "Lkotlin/Pair;", "getOrientationName", "orientation", "isHardwareEncoder", "isUsingHardwareAcceleration", "safeReleaseCodec", "codec", "selectBestHardwareEncoder", "hardwareEncoders", "sendCachedConfigurationDataViaWebSocketWithResolution", "onConfigData", "sendConfigurationData", "setOrientation", "start", "stop", "ultraFastStop", "updateBitRate", "validateEncoderCapabilities", "BufferReference", "Companion", "MediaCodecCallback", "app_debug"})
public final class VideoEncoder {
    private final int width = 0;
    private final int height = 0;
    private int bitRate;
    private final int frameRate = 0;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function2<java.nio.ByteBuffer, java.lang.Integer, kotlin.Unit> onEncodedData = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlin.jvm.functions.Function2<byte[], byte[], kotlin.Unit> onConfigurationData = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlin.jvm.functions.Function4<byte[], byte[], java.lang.Integer, java.lang.Integer, kotlin.Unit> onConfigurationDataWithResolution = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlin.jvm.functions.Function5<byte[], byte[], java.lang.Integer, java.lang.Integer, java.lang.Integer, kotlin.Unit> onConfigurationDataWithOrientation = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String MIME_TYPE = "video/avc";
    private static final int I_FRAME_INTERVAL = 1;
    @org.jetbrains.annotations.Nullable()
    private android.media.MediaCodec mediaCodec;
    @org.jetbrains.annotations.Nullable()
    private android.view.Surface inputSurface;
    private boolean isRunning = false;
    private boolean configurationSent = false;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String selectedEncoderName;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String selectedEncoderType;
    @org.jetbrains.annotations.Nullable()
    private byte[] cachedSpsData;
    @org.jetbrains.annotations.Nullable()
    private byte[] cachedPpsData;
    private int currentOrientation = android.content.res.Configuration.ORIENTATION_PORTRAIT;
    private long lastFrameTime = 0L;
    private long frameCount = 0L;
    private long totalDataSent = 0L;
    private long lastBitrateCheckTime = 0L;
    private long lastDataSent = 0L;
    private final java.util.concurrent.ExecutorService networkExecutor = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.codec.VideoEncoder.Companion Companion = null;
    
    public VideoEncoder(int width, int height, int bitRate, int frameRate, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.nio.ByteBuffer, ? super java.lang.Integer, kotlin.Unit> onEncodedData, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function2<? super byte[], ? super byte[], kotlin.Unit> onConfigurationData, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function4<? super byte[], ? super byte[], ? super java.lang.Integer, ? super java.lang.Integer, kotlin.Unit> onConfigurationDataWithResolution, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function5<? super byte[], ? super byte[], ? super java.lang.Integer, ? super java.lang.Integer, ? super java.lang.Integer, kotlin.Unit> onConfigurationDataWithOrientation) {
        super();
    }
    
    /**
     * 检测并获取可用的硬件H.264编码器列表
     */
    private final java.util.List<android.media.MediaCodecInfo> getAvailableHardwareEncoders() {
        return null;
    }
    
    /**
     * 判断编码器是否为硬件编码器
     */
    private final boolean isHardwareEncoder(android.media.MediaCodecInfo codecInfo) {
        return false;
    }
    
    /**
     * 选择最佳的硬件编码器
     */
    private final android.media.MediaCodecInfo selectBestHardwareEncoder(java.util.List<android.media.MediaCodecInfo> hardwareEncoders) {
        return null;
    }
    
    /**
     * 启动编码器
     */
    @org.jetbrains.annotations.Nullable()
    public final android.view.Surface start() {
        return null;
    }
    
    /**
     * 创建标准格式 - 优化发热问题
     */
    private final android.media.MediaFormat createStandardFormat() {
        return null;
    }
    
    /**
     * 使用指定格式和编码器创建编码器 - 异步回调模式
     */
    private final android.view.Surface createEncoderWithConfig(android.media.MediaFormat format, android.media.MediaCodecInfo codecInfo) {
        return null;
    }
    
    /**
     * 安全释放MediaCodec资源
     */
    private final void safeReleaseCodec(android.media.MediaCodec codec) {
    }
    
    /**
     * 验证编码器能力
     */
    private final void validateEncoderCapabilities(android.media.MediaCodecInfo codecInfo, android.media.MediaFormat format) {
    }
    
    /**
     * 停止异步编码器
     */
    public final void stop() {
    }
    
    /**
     * 超快速停止异步编码器 - 立即停止但在后台释放资源
     * 用于分辨率调整时最小化延迟
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlin.Pair<android.media.MediaCodec, android.view.Surface> ultraFastStop() {
        return null;
    }
    
    /**
     * 发送配置数据（SPS/PPS）
     * 🚀 增强版：包含分辨率信息传递
     */
    private final void sendConfigurationData(android.media.MediaFormat format) {
    }
    
    /**
     * 🚀 新增：通过WebSocket为新连接发送包含分辨率信息的缓存配置数据
     */
    public final void sendCachedConfigurationDataViaWebSocketWithResolution(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function4<? super byte[], ? super byte[], ? super java.lang.Integer, ? super java.lang.Integer, kotlin.Unit> onConfigData) {
    }
    
    /**
     * 动态调整码率
     * @param newBitRate 新的码率值（bps）
     */
    public final void updateBitRate(int newBitRate) {
    }
    
    /**
     * 获取当前码率
     */
    public final int getCurrentBitRate() {
        return 0;
    }
    
    /**
     * 强制重新配置码率（更激进的方法）
     */
    public final void forceUpdateBitRate(int newBitRate) {
    }
    
    /**
     * 获取当前使用的编码器信息
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlin.Pair<java.lang.String, java.lang.String> getEncoderInfo() {
        return null;
    }
    
    /**
     * 检查编码器是否正在使用硬件加速
     */
    public final boolean isUsingHardwareAcceleration() {
        return false;
    }
    
    /**
     * 设置当前屏幕方向
     */
    public final void setOrientation(int orientation) {
    }
    
    /**
     * 获取方向名称（用于日志）
     */
    private final java.lang.String getOrientationName(int orientation) {
        return null;
    }
    
    /**
     * 缓冲区引用管理器 - 解决ByteBuffer生命周期问题
     * 🚀 零拷贝核心：通过引用计数延迟释放MediaCodec缓冲区
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\b\u0002\u0018\u00002\u00020\u0001B%\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\tJ\u0006\u0010\u0016\u001a\u00020\u0017R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0011\u0010\b\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u001b\u0010\f\u001a\u00020\u00038FX\u0086\u0084\u0002\u00a2\u0006\f\n\u0004\b\u000f\u0010\u0010\u001a\u0004\b\r\u0010\u000eR\u000e\u0010\u0011\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u000eR\u000e\u0010\u0014\u001a\u00020\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0018"}, d2 = {"Lcom/example/castapp/codec/VideoEncoder$BufferReference;", "", "originalBuffer", "Ljava/nio/ByteBuffer;", "codec", "Landroid/media/MediaCodec;", "bufferIndex", "", "dataSize", "(Ljava/nio/ByteBuffer;Landroid/media/MediaCodec;II)V", "getDataSize", "()I", "dataView", "getDataView", "()Ljava/nio/ByteBuffer;", "dataView$delegate", "Lkotlin/Lazy;", "isReleased", "", "getOriginalBuffer", "refCount", "Ljava/util/concurrent/atomic/AtomicInteger;", "release", "", "app_debug"})
    static final class BufferReference {
        @org.jetbrains.annotations.NotNull()
        private final java.nio.ByteBuffer originalBuffer = null;
        @org.jetbrains.annotations.NotNull()
        private final android.media.MediaCodec codec = null;
        private final int bufferIndex = 0;
        private final int dataSize = 0;
        @org.jetbrains.annotations.NotNull()
        private final java.util.concurrent.atomic.AtomicInteger refCount = null;
        @kotlin.jvm.Volatile()
        private volatile boolean isReleased = false;
        @org.jetbrains.annotations.NotNull()
        private final kotlin.Lazy dataView$delegate = null;
        
        public BufferReference(@org.jetbrains.annotations.NotNull()
        java.nio.ByteBuffer originalBuffer, @org.jetbrains.annotations.NotNull()
        android.media.MediaCodec codec, int bufferIndex, int dataSize) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.nio.ByteBuffer getOriginalBuffer() {
            return null;
        }
        
        public final int getDataSize() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.nio.ByteBuffer getDataView() {
            return null;
        }
        
        /**
         * 减少引用计数，当计数为0时释放MediaCodec缓冲区
         */
        public final void release() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0007"}, d2 = {"Lcom/example/castapp/codec/VideoEncoder$Companion;", "", "()V", "I_FRAME_INTERVAL", "", "MIME_TYPE", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
    
    /**
     * MediaCodec异步回调处理器
     * 🚀 异步模式：系统驱动的高效编码处理
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0082\u0004\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0018\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0016J\u0018\u0010\t\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u000bH\u0016J \u0010\f\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\r\u001a\u00020\u000eH\u0016J\u0018\u0010\u000f\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0010\u001a\u00020\u0011H\u0016\u00a8\u0006\u0012"}, d2 = {"Lcom/example/castapp/codec/VideoEncoder$MediaCodecCallback;", "Landroid/media/MediaCodec$Callback;", "(Lcom/example/castapp/codec/VideoEncoder;)V", "onError", "", "codec", "Landroid/media/MediaCodec;", "e", "Landroid/media/MediaCodec$CodecException;", "onInputBufferAvailable", "index", "", "onOutputBufferAvailable", "info", "Landroid/media/MediaCodec$BufferInfo;", "onOutputFormatChanged", "format", "Landroid/media/MediaFormat;", "app_debug"})
    final class MediaCodecCallback extends android.media.MediaCodec.Callback {
        
        public MediaCodecCallback() {
            super();
        }
        
        @java.lang.Override()
        public void onInputBufferAvailable(@org.jetbrains.annotations.NotNull()
        android.media.MediaCodec codec, int index) {
        }
        
        @java.lang.Override()
        public void onOutputBufferAvailable(@org.jetbrains.annotations.NotNull()
        android.media.MediaCodec codec, int index, @org.jetbrains.annotations.NotNull()
        android.media.MediaCodec.BufferInfo info) {
        }
        
        @java.lang.Override()
        public void onError(@org.jetbrains.annotations.NotNull()
        android.media.MediaCodec codec, @org.jetbrains.annotations.NotNull()
        android.media.MediaCodec.CodecException e) {
        }
        
        @java.lang.Override()
        public void onOutputFormatChanged(@org.jetbrains.annotations.NotNull()
        android.media.MediaCodec codec, @org.jetbrains.annotations.NotNull()
        android.media.MediaFormat format) {
        }
    }
}