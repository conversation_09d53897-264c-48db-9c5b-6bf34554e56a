package com.example.castapp.audio;

/**
 * PCM音频播放器 - 优化版本，支持低延迟播放
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000j\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u000f\u0018\u0000 52\u00020\u0001:\u000245B?\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0007\u001a\u00020\u0005\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u0012\b\b\u0002\u0010\n\u001a\u00020\t\u00a2\u0006\u0002\u0010\u000bJ\b\u0010\"\u001a\u00020#H\u0002J\u0006\u0010$\u001a\u00020#J\u0018\u0010%\u001a\u00020\u001e2\u0006\u0010&\u001a\u00020\'2\u0006\u0010(\u001a\u00020\u0005H\u0002J\b\u0010)\u001a\u00020\tH\u0002J\u0006\u0010\u0018\u001a\u00020\tJ\b\u0010*\u001a\u00020#H\u0002J\b\u0010+\u001a\u00020#H\u0002J\u0016\u0010,\u001a\u00020#2\u0006\u0010&\u001a\u00020\'2\u0006\u0010(\u001a\u00020\u0005J\b\u0010-\u001a\u00020#H\u0002J\b\u0010.\u001a\u00020#H\u0002J\u000e\u0010/\u001a\u00020#2\u0006\u0010\n\u001a\u00020\tJ\u0006\u00100\u001a\u00020\tJ\b\u00101\u001a\u00020#H\u0002J\u0006\u00102\u001a\u00020#J\b\u00103\u001a\u00020#H\u0002R\u0010\u0010\f\u001a\u0004\u0018\u00010\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0010\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0013X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0015X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0005X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0005X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0019X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u0015X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u001e0\u001dX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001f\u001a\u0004\u0018\u00010 X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010!\u001a\u00020\u0015X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00066"}, d2 = {"Lcom/example/castapp/audio/AudioPlayer;", "", "context", "Landroid/content/Context;", "sampleRate", "", "channelCount", "audioFormat", "lowLatencyMode", "", "isSpeakerMode", "(Landroid/content/Context;IIIZZ)V", "audioFocusRequest", "Landroid/media/AudioFocusRequest;", "audioManager", "Landroid/media/AudioManager;", "audioTrack", "Landroid/media/AudioTrack;", "bufferPool", "Lcom/example/castapp/audio/AudioBufferPool;", "bytesPlayed", "", "droppedFrames", "framesPlayed", "isPlaying", "Ljava/util/concurrent/atomic/AtomicBoolean;", "lastLogTime", "maxQueueSize", "playQueue", "Ljava/util/concurrent/BlockingQueue;", "Lcom/example/castapp/audio/AudioPlayer$AudioBuffer;", "playThread", "Ljava/lang/Thread;", "pooledBuffersUsed", "abandonAudioFocus", "", "clearQueue", "createAudioBufferFromDataView", "dataView", "Lcom/example/castapp/network/DataView;", "size", "createAudioTrack", "logStatisticsIfNeeded", "playAudio", "playDataView", "requestAudioFocus", "setAudioRouting", "setPlaybackMode", "start", "startPlaybackThread", "stop", "stopAudioTrackSafely", "AudioBuffer", "Companion", "app_debug"})
public final class AudioPlayer {
    private final int sampleRate = 0;
    private final int channelCount = 0;
    private final int audioFormat = 0;
    private final boolean lowLatencyMode = false;
    private boolean isSpeakerMode;
    private static final long LOG_INTERVAL_MS = 60000L;
    private static final int LOW_LATENCY_QUEUE_SIZE = 8;
    private static final int NORMAL_QUEUE_SIZE = 16;
    @org.jetbrains.annotations.Nullable()
    private android.media.AudioTrack audioTrack;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.atomic.AtomicBoolean isPlaying = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.BlockingQueue<com.example.castapp.audio.AudioPlayer.AudioBuffer> playQueue = null;
    private final int maxQueueSize = 0;
    @org.jetbrains.annotations.Nullable()
    private java.lang.Thread playThread;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.audio.AudioBufferPool bufferPool = null;
    @org.jetbrains.annotations.NotNull()
    private final android.media.AudioManager audioManager = null;
    @org.jetbrains.annotations.Nullable()
    private android.media.AudioFocusRequest audioFocusRequest;
    private long lastLogTime = 0L;
    private int framesPlayed = 0;
    private long bytesPlayed = 0L;
    private int droppedFrames = 0;
    private long pooledBuffersUsed = 0L;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.audio.AudioPlayer.Companion Companion = null;
    
    public AudioPlayer(@org.jetbrains.annotations.NotNull()
    android.content.Context context, int sampleRate, int channelCount, int audioFormat, boolean lowLatencyMode, boolean isSpeakerMode) {
        super();
    }
    
    /**
     * 启动音频播放器
     */
    public final boolean start() {
        return false;
    }
    
    /**
     * 停止音频播放器
     */
    public final void stop() {
    }
    
    /**
     * 检查播放器是否正在运行
     */
    public final boolean isPlaying() {
        return false;
    }
    
    /**
     * 🚀 零拷贝优化：播放PCM音频数据视图
     */
    public final void playDataView(@org.jetbrains.annotations.NotNull()
    com.example.castapp.network.DataView dataView, int size) {
    }
    
    /**
     * 🚀 零拷贝优化：从DataView创建音频缓冲区 - 延迟拷贝策略
     */
    private final com.example.castapp.audio.AudioPlayer.AudioBuffer createAudioBufferFromDataView(com.example.castapp.network.DataView dataView, int size) {
        return null;
    }
    
    /**
     * 清空播放队列
     */
    public final void clearQueue() {
    }
    
    /**
     * 音频播放线程
     */
    private final void playAudio() {
    }
    
    /**
     * 定期输出统计信息 - 包含延迟优化信息
     */
    private final void logStatisticsIfNeeded() {
    }
    
    /**
     * 设置音频路由 - 🔥 根本性修复：简化路由设置，主要依赖AudioAttributes自动路由
     */
    private final void setAudioRouting() {
    }
    
    /**
     * 切换播放模式 - 线程安全版：确保播放线程安全停止后再重新创建
     */
    public final void setPlaybackMode(boolean isSpeakerMode) {
    }
    
    /**
     * 安全停止AudioTrack和播放线程
     */
    private final void stopAudioTrackSafely() {
    }
    
    /**
     * 启动播放线程
     */
    private final void startPlaybackThread() {
    }
    
    /**
     * 创建AudioTrack（提取为独立方法）
     */
    private final boolean createAudioTrack() {
        return false;
    }
    
    /**
     * 请求音频焦点 - 确保音量键控制系统音量
     */
    private final void requestAudioFocus() {
    }
    
    /**
     * 释放音频焦点
     */
    private final void abandonAudioFocus() {
    }
    
    /**
     * 🚀 零拷贝优化：音频缓冲区包装类 - 支持DataView延迟拷贝
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u0012\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b2\u0018\u00002\u00020\u0001:\u0002\u000b\fB\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0007\u001a\u00020\bH&J\b\u0010\t\u001a\u00020\nH&R\u0012\u0010\u0003\u001a\u00020\u0004X\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b\u0005\u0010\u0006\u0082\u0001\u0002\r\u000e\u00a8\u0006\u000f"}, d2 = {"Lcom/example/castapp/audio/AudioPlayer$AudioBuffer;", "", "()V", "size", "", "getSize", "()I", "getByteArray", "", "release", "", "ByteArrayBuffer", "DataViewBuffer", "Lcom/example/castapp/audio/AudioPlayer$AudioBuffer$ByteArrayBuffer;", "Lcom/example/castapp/audio/AudioPlayer$AudioBuffer$DataViewBuffer;", "app_debug"})
    static abstract class AudioBuffer {
        
        private AudioBuffer() {
            super();
        }
        
        public abstract int getSize();
        
        @org.jetbrains.annotations.NotNull()
        public abstract byte[] getByteArray();
        
        public abstract void release();
        
        /**
         * 基于ByteArray的音频缓冲区（池化）
         */
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0012\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0002\b\f\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\t\u0010\u000e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000f\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0007H\u00c6\u0003J\'\u0010\u0011\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u00c6\u0001J\u0013\u0010\u0012\u001a\u00020\u00072\b\u0010\u0013\u001a\u0004\u0018\u00010\u0014H\u0096\u0002J\b\u0010\u0015\u001a\u00020\u0003H\u0016J\b\u0010\u0016\u001a\u00020\u0005H\u0016J\b\u0010\u0017\u001a\u00020\u0018H\u0016J\t\u0010\u0019\u001a\u00020\u001aH\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u000bR\u0014\u0010\u0004\u001a\u00020\u0005X\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\r\u00a8\u0006\u001b"}, d2 = {"Lcom/example/castapp/audio/AudioPlayer$AudioBuffer$ByteArrayBuffer;", "Lcom/example/castapp/audio/AudioPlayer$AudioBuffer;", "data", "", "size", "", "isPooled", "", "([BIZ)V", "getData", "()[B", "()Z", "getSize", "()I", "component1", "component2", "component3", "copy", "equals", "other", "", "getByteArray", "hashCode", "release", "", "toString", "", "app_debug"})
        public static final class ByteArrayBuffer extends com.example.castapp.audio.AudioPlayer.AudioBuffer {
            @org.jetbrains.annotations.NotNull()
            private final byte[] data = null;
            private final int size = 0;
            private final boolean isPooled = false;
            
            public ByteArrayBuffer(@org.jetbrains.annotations.NotNull()
            byte[] data, int size, boolean isPooled) {
            }
            
            @org.jetbrains.annotations.NotNull()
            public final byte[] getData() {
                return null;
            }
            
            @java.lang.Override()
            public int getSize() {
                return 0;
            }
            
            public final boolean isPooled() {
                return false;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public byte[] getByteArray() {
                return null;
            }
            
            @java.lang.Override()
            public void release() {
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final byte[] component1() {
                return null;
            }
            
            public final int component2() {
                return 0;
            }
            
            public final boolean component3() {
                return false;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.example.castapp.audio.AudioPlayer.AudioBuffer.ByteArrayBuffer copy(@org.jetbrains.annotations.NotNull()
            byte[] data, int size, boolean isPooled) {
                return null;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
        
        /**
         * 🚀 零拷贝优化：基于DataView的音频缓冲区（延迟拷贝）
         */
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\t\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0012\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\t\u0010\u000b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\f\u001a\u00020\u0005H\u00c6\u0003J\u001d\u0010\r\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u000e\u001a\u00020\u000f2\b\u0010\u0010\u001a\u0004\u0018\u00010\u0011H\u00d6\u0003J\b\u0010\u0012\u001a\u00020\u0013H\u0016J\t\u0010\u0014\u001a\u00020\u0005H\u00d6\u0001J\b\u0010\u0015\u001a\u00020\u0016H\u0016J\t\u0010\u0017\u001a\u00020\u0018H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0014\u0010\u0004\u001a\u00020\u0005X\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\n\u00a8\u0006\u0019"}, d2 = {"Lcom/example/castapp/audio/AudioPlayer$AudioBuffer$DataViewBuffer;", "Lcom/example/castapp/audio/AudioPlayer$AudioBuffer;", "dataView", "Lcom/example/castapp/network/DataView;", "size", "", "(Lcom/example/castapp/network/DataView;I)V", "getDataView", "()Lcom/example/castapp/network/DataView;", "getSize", "()I", "component1", "component2", "copy", "equals", "", "other", "", "getByteArray", "", "hashCode", "release", "", "toString", "", "app_debug"})
        public static final class DataViewBuffer extends com.example.castapp.audio.AudioPlayer.AudioBuffer {
            @org.jetbrains.annotations.NotNull()
            private final com.example.castapp.network.DataView dataView = null;
            private final int size = 0;
            
            public DataViewBuffer(@org.jetbrains.annotations.NotNull()
            com.example.castapp.network.DataView dataView, int size) {
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.example.castapp.network.DataView getDataView() {
                return null;
            }
            
            @java.lang.Override()
            public int getSize() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public byte[] getByteArray() {
                return null;
            }
            
            @java.lang.Override()
            public void release() {
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.example.castapp.network.DataView component1() {
                return null;
            }
            
            public final int component2() {
                return 0;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.example.castapp.audio.AudioPlayer.AudioBuffer.DataViewBuffer copy(@org.jetbrains.annotations.NotNull()
            com.example.castapp.network.DataView dataView, int size) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\b"}, d2 = {"Lcom/example/castapp/audio/AudioPlayer$Companion;", "", "()V", "LOG_INTERVAL_MS", "", "LOW_LATENCY_QUEUE_SIZE", "", "NORMAL_QUEUE_SIZE", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}