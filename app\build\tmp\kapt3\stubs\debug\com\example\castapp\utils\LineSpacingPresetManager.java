package com.example.castapp.utils;

/**
 * 行间距预设管理器
 * 管理预设和自定义行间距列表，提供持久化存储功能
 * 完全复用字间距架构的实现模式
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0010\u0007\n\u0002\b\u0003\n\u0002\u0010#\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\n\b\u00c6\u0002\u0018\u00002\u00020\u0001:\u0001%B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\bJ\u000e\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\rJ\u000e\u0010\u0016\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\bJ\f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00040\u0007J\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\b0\u0007J\u000e\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\b0\u0007H\u0002J\u000e\u0010\u001a\u001a\u00020\u00142\u0006\u0010\u001b\u001a\u00020\u001cJ\u000e\u0010\u001d\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\bJ\u0010\u0010\u001e\u001a\u00020\u00142\u0006\u0010\u0012\u001a\u00020\bH\u0002J\u0010\u0010\u001f\u001a\u00020\u00142\u0006\u0010\u0012\u001a\u00020\bH\u0002J\b\u0010 \u001a\u00020\u0014H\u0002J\u000e\u0010!\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\rJ\u0006\u0010\"\u001a\u00020\u0014J\u0016\u0010#\u001a\u00020\u00142\f\u0010$\u001a\b\u0012\u0004\u0012\u00020\b0\u0007H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006&"}, d2 = {"Lcom/example/castapp/utils/LineSpacingPresetManager;", "", "()V", "KEY_CUSTOM_LINE_SPACINGS", "", "PREFS_NAME", "PRESET_LINE_SPACINGS", "", "", "getPRESET_LINE_SPACINGS", "()Ljava/util/List;", "listeners", "", "Lcom/example/castapp/utils/LineSpacingPresetManager$LineSpacingPresetListener;", "sharedPreferences", "Landroid/content/SharedPreferences;", "addCustomLineSpacing", "", "lineSpacing", "addListener", "", "listener", "deleteCustomLineSpacing", "getAllLineSpacingOptions", "getAllLineSpacings", "getCustomLineSpacings", "initialize", "context", "Landroid/content/Context;", "isPresetLineSpacing", "notifyLineSpacingAdded", "notifyLineSpacingDeleted", "notifyLineSpacingListReset", "removeListener", "resetToDefault", "saveCustomLineSpacings", "lineSpacings", "LineSpacingPresetListener", "app_debug"})
public final class LineSpacingPresetManager {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String PREFS_NAME = "line_spacing_presets";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_CUSTOM_LINE_SPACINGS = "custom_line_spacings";
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<java.lang.Float> PRESET_LINE_SPACINGS = null;
    private static android.content.SharedPreferences sharedPreferences;
    @org.jetbrains.annotations.NotNull()
    private static final java.util.Set<com.example.castapp.utils.LineSpacingPresetManager.LineSpacingPresetListener> listeners = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.utils.LineSpacingPresetManager INSTANCE = null;
    
    private LineSpacingPresetManager() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.Float> getPRESET_LINE_SPACINGS() {
        return null;
    }
    
    /**
     * 初始化管理器
     */
    public final void initialize(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    /**
     * 获取自定义行间距列表
     */
    private final java.util.List<java.lang.Float> getCustomLineSpacings() {
        return null;
    }
    
    /**
     * 保存自定义行间距列表
     */
    private final void saveCustomLineSpacings(java.util.List<java.lang.Float> lineSpacings) {
    }
    
    /**
     * 获取完整的行间距列表（预设 + 自定义）
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.Float> getAllLineSpacings() {
        return null;
    }
    
    /**
     * 获取行间距选项字符串列表（用于Spinner显示）
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getAllLineSpacingOptions() {
        return null;
    }
    
    /**
     * 添加自定义行间距
     */
    public final boolean addCustomLineSpacing(float lineSpacing) {
        return false;
    }
    
    /**
     * 删除自定义行间距
     */
    public final boolean deleteCustomLineSpacing(float lineSpacing) {
        return false;
    }
    
    /**
     * 重置为默认行间距设置（清除所有自定义行间距）
     */
    public final void resetToDefault() {
    }
    
    /**
     * 检查行间距是否为预设行间距
     */
    public final boolean isPresetLineSpacing(float lineSpacing) {
        return false;
    }
    
    /**
     * 添加行间距列表变化监听器
     */
    public final void addListener(@org.jetbrains.annotations.NotNull()
    com.example.castapp.utils.LineSpacingPresetManager.LineSpacingPresetListener listener) {
    }
    
    /**
     * 移除行间距列表变化监听器
     */
    public final void removeListener(@org.jetbrains.annotations.NotNull()
    com.example.castapp.utils.LineSpacingPresetManager.LineSpacingPresetListener listener) {
    }
    
    private final void notifyLineSpacingAdded(float lineSpacing) {
    }
    
    private final void notifyLineSpacingDeleted(float lineSpacing) {
    }
    
    private final void notifyLineSpacingListReset() {
    }
    
    /**
     * 行间距预设监听器接口
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0003\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0016J\u0010\u0010\u0006\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0016J\b\u0010\u0007\u001a\u00020\u0003H\u0016\u00a8\u0006\b"}, d2 = {"Lcom/example/castapp/utils/LineSpacingPresetManager$LineSpacingPresetListener;", "", "onLineSpacingAdded", "", "lineSpacing", "", "onLineSpacingDeleted", "onLineSpacingListReset", "app_debug"})
    public static abstract interface LineSpacingPresetListener {
        
        public abstract void onLineSpacingAdded(float lineSpacing);
        
        public abstract void onLineSpacingDeleted(float lineSpacing);
        
        public abstract void onLineSpacingListReset();
        
        /**
         * 行间距预设监听器接口
         */
        @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
        public static final class DefaultImpls {
            
            public static void onLineSpacingAdded(@org.jetbrains.annotations.NotNull()
            com.example.castapp.utils.LineSpacingPresetManager.LineSpacingPresetListener $this, float lineSpacing) {
            }
            
            public static void onLineSpacingDeleted(@org.jetbrains.annotations.NotNull()
            com.example.castapp.utils.LineSpacingPresetManager.LineSpacingPresetListener $this, float lineSpacing) {
            }
            
            public static void onLineSpacingListReset(@org.jetbrains.annotations.NotNull()
            com.example.castapp.utils.LineSpacingPresetManager.LineSpacingPresetListener $this) {
            }
        }
    }
}