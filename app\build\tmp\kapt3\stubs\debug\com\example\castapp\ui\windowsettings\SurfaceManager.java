package com.example.castapp.ui.windowsettings;

/**
 * Surface管理器
 * 负责TextureView和Surface的生命周期管理
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\u0018\u00002\u00020\u0001B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0006\u0010\r\u001a\u00020\u000eJ\u0010\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\bH\u0002J\b\u0010\u0012\u001a\u00020\u0010H\u0002J\b\u0010\u0013\u001a\u0004\u0018\u00010\fJ\u0010\u0010\u0014\u001a\u00020\u000e2\b\u0010\u0015\u001a\u0004\u0018\u00010\nJ\u0016\u0010\u0016\u001a\u00020\u000e2\u0006\u0010\u0017\u001a\u00020\b2\u0006\u0010\u0011\u001a\u00020\bJ\u000e\u0010\u0018\u001a\u00020\u000e2\u0006\u0010\u0007\u001a\u00020\bR\u000e\u0010\u0007\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\t\u001a\u0004\u0018\u00010\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000b\u001a\u0004\u0018\u00010\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0019"}, d2 = {"Lcom/example/castapp/ui/windowsettings/SurfaceManager;", "", "context", "Landroid/content/Context;", "container", "Landroid/widget/FrameLayout;", "(Landroid/content/Context;Landroid/widget/FrameLayout;)V", "connectionId", "", "surfaceStateListener", "Lcom/example/castapp/ui/windowsettings/interfaces/SurfaceStateListener;", "textureView", "Landroid/view/TextureView;", "cleanup", "", "createCameraSurfaceTextureListener", "Landroid/view/TextureView$SurfaceTextureListener;", "cameraName", "createSurfaceTextureListener", "getTextureView", "setSurfaceStateListener", "listener", "setupCameraTextureView", "cameraId", "setupTextureView", "app_debug"})
public final class SurfaceManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final android.widget.FrameLayout container = null;
    @org.jetbrains.annotations.Nullable()
    private android.view.TextureView textureView;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String connectionId = "";
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.windowsettings.interfaces.SurfaceStateListener surfaceStateListener;
    
    public SurfaceManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    android.widget.FrameLayout container) {
        super();
    }
    
    /**
     * 设置Surface状态监听器
     */
    public final void setSurfaceStateListener(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.windowsettings.interfaces.SurfaceStateListener listener) {
    }
    
    /**
     * 为指定连接创建TextureView和Surface
     * @param connectionId 连接ID
     */
    public final void setupTextureView(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 创建SurfaceTextureListener
     */
    private final android.view.TextureView.SurfaceTextureListener createSurfaceTextureListener() {
        return null;
    }
    
    /**
     * 为摄像头创建TextureView和Surface
     */
    public final void setupCameraTextureView(@org.jetbrains.annotations.NotNull()
    java.lang.String cameraId, @org.jetbrains.annotations.NotNull()
    java.lang.String cameraName) {
    }
    
    /**
     * 创建摄像头SurfaceTextureListener
     */
    private final android.view.TextureView.SurfaceTextureListener createCameraSurfaceTextureListener(java.lang.String cameraName) {
        return null;
    }
    
    /**
     * 获取当前TextureView
     */
    @org.jetbrains.annotations.Nullable()
    public final android.view.TextureView getTextureView() {
        return null;
    }
    
    /**
     * 清理资源
     */
    public final void cleanup() {
    }
}