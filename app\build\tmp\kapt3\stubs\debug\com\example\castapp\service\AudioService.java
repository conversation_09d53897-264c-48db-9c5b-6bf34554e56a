package com.example.castapp.service;

/**
 * 独立的音频流服务
 * 完全独立于投屏服务，专门处理音频流功能
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0090\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u000b\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0010\t\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\b\f\u0018\u0000 _2\u00020\u0001:\u0001_B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0013\u001a\u00020\u0014H\u0002J\b\u0010\u0015\u001a\u00020\u0014H\u0002J\u001a\u0010\u0016\u001a\u00020\u00142\u0006\u0010\u0017\u001a\u00020\t2\b\u0010\u0018\u001a\u0004\u0018\u00010\u0006H\u0002J\u001c\u0010\u0019\u001a\u00020\u00142\u0012\u0010\u001a\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n0\bH\u0002J\u001a\u0010\u001b\u001a\u00020\u00142\b\u0010\u001c\u001a\u0004\u0018\u00010\n2\u0006\u0010\u001d\u001a\u00020\tH\u0002J.\u0010\u001e\u001a\u0004\u0018\u00010\u00062\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010!\u001a\u00020 2\u0012\u0010\u001a\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n0\bH\u0002J*\u0010\"\u001a\u0004\u0018\u00010\n2\u0006\u0010#\u001a\u00020$2\u0006\u0010%\u001a\u00020\t2\u0006\u0010&\u001a\u00020 2\u0006\u0010\'\u001a\u00020 H\u0002J2\u0010(\u001a\u00020\u00142\u0006\u0010)\u001a\u00020*2\u0018\u0010+\u001a\u0014\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00140,2\u0006\u0010-\u001a\u00020\tH\u0002J\u0010\u0010.\u001a\u00020\u00142\u0006\u0010\u001d\u001a\u00020\tH\u0002J\u0018\u0010/\u001a\u00020\u00142\u0006\u00100\u001a\u00020 2\u0006\u00101\u001a\u00020*H\u0002J.\u00102\u001a\u00020\u00142\u0006\u0010\u001d\u001a\u00020\t2\u0012\u0010\u001a\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n0\b2\b\u0010\u0018\u001a\u0004\u0018\u00010\u0006H\u0002J\u0010\u00103\u001a\u00020\u00142\u0006\u0010)\u001a\u00020*H\u0002J$\u00104\u001a\u00020\u00142\u0006\u0010)\u001a\u00020*2\u0012\u0010+\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u001405H\u0002J\u0018\u00106\u001a\u00020\u00142\u0006\u0010\u001d\u001a\u00020\t2\u0006\u00107\u001a\u000208H\u0002J\b\u00109\u001a\u00020\u0014H\u0002J\u0010\u0010:\u001a\u00020\u00142\u0006\u0010;\u001a\u00020<H\u0002J\b\u0010=\u001a\u00020\u0014H\u0002J\u0014\u0010>\u001a\u0004\u0018\u00010?2\b\u0010)\u001a\u0004\u0018\u00010*H\u0016J\b\u0010@\u001a\u00020\u0014H\u0016J\b\u0010A\u001a\u00020\u0014H\u0016J\"\u0010B\u001a\u00020 2\b\u0010)\u001a\u0004\u0018\u00010*2\u0006\u0010C\u001a\u00020 2\u0006\u0010D\u001a\u00020 H\u0016J\b\u0010E\u001a\u00020\u0014H\u0002J(\u0010F\u001a\u00020\u00142\u0006\u0010\u001d\u001a\u00020\t2\u0006\u0010G\u001a\u00020H2\u0006\u0010;\u001a\u00020<2\u0006\u0010I\u001a\u00020<H\u0002J\b\u0010J\u001a\u00020\u0014H\u0002J\u0018\u0010K\u001a\u00020\u00142\u0006\u0010\u001d\u001a\u00020\t2\u0006\u0010%\u001a\u00020\tH\u0002J\b\u0010L\u001a\u00020\u0014H\u0002J\u0018\u0010M\u001a\u00020\u00142\u0006\u0010\u001d\u001a\u00020\t2\u0006\u0010%\u001a\u00020\tH\u0002J\b\u0010N\u001a\u00020\u0014H\u0002J \u0010O\u001a\u00020<2\u0006\u0010#\u001a\u00020$2\u0006\u0010P\u001a\u00020\t2\u0006\u0010-\u001a\u00020\tH\u0002J\b\u0010Q\u001a\u00020\u0014H\u0002JB\u0010R\u001a\u00020\u00142\u0006\u0010\u001d\u001a\u00020\t2\u0006\u0010;\u001a\u00020<2\u0012\u0010\u001a\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n0\b2\u0006\u0010P\u001a\u00020\t2\f\u0010S\u001a\b\u0012\u0004\u0012\u00020\u00140TH\u0002J\u0010\u0010U\u001a\u00020\u00142\u0006\u0010\u001d\u001a\u00020\tH\u0002J\u0010\u0010V\u001a\u00020\u00142\u0006\u0010\u001d\u001a\u00020\tH\u0002J\u0018\u0010W\u001a\u00020\u00142\u0006\u0010)\u001a\u00020*2\u0006\u0010;\u001a\u00020<H\u0002J\"\u0010X\u001a\u00020\u00142\b\u0010\u0018\u001a\u0004\u0018\u00010\u00062\u0006\u0010Y\u001a\u00020 2\u0006\u0010-\u001a\u00020\tH\u0002J\u0010\u0010Z\u001a\u00020\u00142\u0006\u0010Y\u001a\u00020 H\u0002J\u0010\u0010[\u001a\u00020\u00142\u0006\u0010Y\u001a\u00020 H\u0002J\u0010\u0010\\\u001a\u00020\u00142\u0006\u0010]\u001a\u00020\tH\u0002J\b\u0010^\u001a\u00020\u0014H\u0002R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000b\u001a\u0004\u0018\u00010\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\r\u001a\u0004\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u000e\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006`"}, d2 = {"Lcom/example/castapp/service/AudioService;", "Landroid/app/Service;", "()V", "audioCaptureManager", "Lcom/example/castapp/audio/AudioCaptureManager;", "mediaAudioEncoder", "Lcom/example/castapp/audio/AudioEncoder;", "mediaAudioRtpSenders", "Ljava/util/concurrent/ConcurrentHashMap;", "", "Lcom/example/castapp/audio/AudioRtpSender;", "mediaProjectionManager", "Lcom/example/castapp/manager/MediaProjectionManager;", "micAudioEncoder", "micAudioRtpSenders", "stateManager", "Lcom/example/castapp/manager/StateManager;", "webSocketManager", "Lcom/example/castapp/manager/WebSocketManager;", "applySavedMediaAudioVolume", "", "applySavedMicAudioVolume", "applySavedVolume", "key", "encoder", "cleanupAllRtpSenders", "senders", "cleanupRtpSender", "sender", "connectionId", "createAudioEncoder", "channelCount", "", "bitRate", "createAudioRtpSender", "connection", "Lcom/example/castapp/model/Connection;", "targetIp", "targetPort", "payloadType", "handleAudioAction", "intent", "Landroid/content/Intent;", "action", "Lkotlin/Function2;", "audioType", "handleDisconnectMessage", "handleMediaAudioPermissionResult", "resultCode", "resultData", "handleNewConnectionConfig", "handlePermissionResult", "handleStopAction", "Lkotlin/Function1;", "handleWebSocketMessage", "controlMessage", "Lcom/example/castapp/websocket/ControlMessage;", "initializeAudioCaptureManager", "initializeMediaEncoder", "isMedia", "", "initializeMediaProjectionManager", "onBind", "Landroid/os/IBinder;", "onCreate", "onDestroy", "onStartCommand", "flags", "startId", "retryPendingMediaAudioConnections", "sendAudioControlMessages", "ssrc", "", "enable", "startForegroundService", "startMediaAudio", "startMediaCapture", "startMicAudio", "startMicCapture", "startWebSocketFunction", "functionType", "stopAllAudioStreams", "stopAudioStream", "stopCapture", "Lkotlin/Function0;", "stopMediaAudio", "stopMicAudio", "updateAudioVolume", "updateEncoderVolume", "volume", "updateMediaAudioVolume", "updateMicAudioVolume", "updateNotification", "title", "updateNotificationOrStopService", "Companion", "app_debug"})
public final class AudioService extends android.app.Service {
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_START_MEDIA_AUDIO = "action_start_media_audio";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_STOP_MEDIA_AUDIO = "action_stop_media_audio";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_START_MIC_AUDIO = "action_start_mic_audio";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_STOP_MIC_AUDIO = "action_stop_mic_audio";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_HANDLE_MEDIA_AUDIO_PERMISSION = "action_handle_media_audio_permission";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_UPDATE_MEDIA_VOLUME = "action_update_media_volume";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_UPDATE_MIC_VOLUME = "action_update_mic_volume";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_CONNECTION_ID = "connection_id";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_RESULT_CODE = "result_code";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_RESULT_DATA = "result_data";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_VOLUME = "volume";
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.manager.MediaProjectionManager mediaProjectionManager;
    private com.example.castapp.manager.StateManager stateManager;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.WebSocketManager webSocketManager = null;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.audio.AudioCaptureManager audioCaptureManager;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.audio.AudioEncoder mediaAudioEncoder;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.audio.AudioEncoder micAudioEncoder;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, com.example.castapp.audio.AudioRtpSender> mediaAudioRtpSenders = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, com.example.castapp.audio.AudioRtpSender> micAudioRtpSenders = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.service.AudioService.Companion Companion = null;
    
    public AudioService() {
        super();
    }
    
    @java.lang.Override()
    public void onCreate() {
    }
    
    @java.lang.Override()
    public int onStartCommand(@org.jetbrains.annotations.Nullable()
    android.content.Intent intent, int flags, int startId) {
        return 0;
    }
    
    private final void handleAudioAction(android.content.Intent intent, kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.String, kotlin.Unit> action, java.lang.String audioType) {
    }
    
    private final void handleStopAction(android.content.Intent intent, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> action) {
    }
    
    private final void updateAudioVolume(android.content.Intent intent, boolean isMedia) {
    }
    
    private final void handlePermissionResult(android.content.Intent intent) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.os.IBinder onBind(@org.jetbrains.annotations.Nullable()
    android.content.Intent intent) {
        return null;
    }
    
    private final void initializeMediaProjectionManager() {
    }
    
    private final void initializeAudioCaptureManager() {
    }
    
    private final void startForegroundService() {
    }
    
    private final boolean startWebSocketFunction(com.example.castapp.model.Connection connection, java.lang.String functionType, java.lang.String audioType) {
        return false;
    }
    
    private final com.example.castapp.audio.AudioRtpSender createAudioRtpSender(com.example.castapp.model.Connection connection, java.lang.String targetIp, int targetPort, int payloadType) {
        return null;
    }
    
    private final void sendAudioControlMessages(java.lang.String connectionId, long ssrc, boolean isMedia, boolean enable) {
    }
    
    private final void startMediaAudio(java.lang.String connectionId, java.lang.String targetIp) {
    }
    
    private final void initializeMediaEncoder(boolean isMedia) {
    }
    
    private final com.example.castapp.audio.AudioEncoder createAudioEncoder(int channelCount, int bitRate, java.util.concurrent.ConcurrentHashMap<java.lang.String, com.example.castapp.audio.AudioRtpSender> senders) {
        return null;
    }
    
    private final void startMediaCapture() {
    }
    
    private final void handleNewConnectionConfig(java.lang.String connectionId, java.util.concurrent.ConcurrentHashMap<java.lang.String, com.example.castapp.audio.AudioRtpSender> senders, com.example.castapp.audio.AudioEncoder encoder) {
    }
    
    private final void startMicAudio(java.lang.String connectionId, java.lang.String targetIp) {
    }
    
    private final void startMicCapture() {
    }
    
    private final void stopMediaAudio(java.lang.String connectionId) {
    }
    
    private final void stopMicAudio(java.lang.String connectionId) {
    }
    
    private final void stopAudioStream(java.lang.String connectionId, boolean isMedia, java.util.concurrent.ConcurrentHashMap<java.lang.String, com.example.castapp.audio.AudioRtpSender> senders, java.lang.String functionType, kotlin.jvm.functions.Function0<kotlin.Unit> stopCapture) {
    }
    
    /**
     * 🚀 新增：处理WebSocket消息，特别是断开连接消息
     */
    private final void handleWebSocketMessage(java.lang.String connectionId, com.example.castapp.websocket.ControlMessage controlMessage) {
    }
    
    /**
     * 🚀 新增：处理断开连接消息，停止所有音频服务并更新状态
     */
    private final void handleDisconnectMessage(java.lang.String connectionId) {
    }
    
    private final void cleanupRtpSender(com.example.castapp.audio.AudioRtpSender sender, java.lang.String connectionId) {
    }
    
    /**
     * 处理媒体音频权限结果
     */
    private final void handleMediaAudioPermissionResult(int resultCode, android.content.Intent resultData) {
    }
    
    /**
     * 重新尝试启动等待中的媒体音频连接
     */
    private final void retryPendingMediaAudioConnections() {
    }
    
    private final void stopAllAudioStreams() {
    }
    
    private final void cleanupAllRtpSenders(java.util.concurrent.ConcurrentHashMap<java.lang.String, com.example.castapp.audio.AudioRtpSender> senders) {
    }
    
    private final void updateNotificationOrStopService() {
    }
    
    private final void updateNotification(java.lang.String title) {
    }
    
    private final void updateMediaAudioVolume(int volume) {
    }
    
    private final void updateMicAudioVolume(int volume) {
    }
    
    private final void updateEncoderVolume(com.example.castapp.audio.AudioEncoder encoder, int volume, java.lang.String audioType) {
    }
    
    private final void applySavedMediaAudioVolume() {
    }
    
    private final void applySavedMicAudioVolume() {
    }
    
    private final void applySavedVolume(java.lang.String key, com.example.castapp.audio.AudioEncoder encoder) {
    }
    
    @java.lang.Override()
    public void onDestroy() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u000b\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000f"}, d2 = {"Lcom/example/castapp/service/AudioService$Companion;", "", "()V", "ACTION_HANDLE_MEDIA_AUDIO_PERMISSION", "", "ACTION_START_MEDIA_AUDIO", "ACTION_START_MIC_AUDIO", "ACTION_STOP_MEDIA_AUDIO", "ACTION_STOP_MIC_AUDIO", "ACTION_UPDATE_MEDIA_VOLUME", "ACTION_UPDATE_MIC_VOLUME", "EXTRA_CONNECTION_ID", "EXTRA_RESULT_CODE", "EXTRA_RESULT_DATA", "EXTRA_VOLUME", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}