package com.example.castapp.manager;

/**
 * 分辨率管理器
 * 负责管理动态分辨率调整功能
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0002\u0018\u0000 \u00172\u00020\u0001:\u0001\u0017B\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u001a\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\u000b\u001a\u00020\u0007J\u001a\u0010\f\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\u000b\u001a\u00020\u0007J\u001c\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\u000b\u001a\u00020\u0007H\u0002J\u0012\u0010\u000e\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00070\u0006J\u0006\u0010\u000f\u001a\u00020\u0007J\u0012\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00070\u0006J\b\u0010\u0011\u001a\u00020\u0007H\u0003J\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00070\u0006J\u0010\u0010\u0013\u001a\u00020\u00072\u0006\u0010\u0014\u001a\u00020\u0007H\u0002J\u000e\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u000b\u001a\u00020\u0007R\u001c\u0010\u0005\u001a\u0010\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0018"}, d2 = {"Lcom/example/castapp/manager/ResolutionManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "cachedOriginalResolution", "Lkotlin/Pair;", "", "sharedPreferences", "Landroid/content/SharedPreferences;", "calculateCaptureResolution", "scalePercent", "calculateResolution", "calculateResolutionInternal", "getCurrentResolution", "getCurrentResolutionScale", "getOriginalScreenResolution", "getStatusBarHeight", "getUsableScreenResolution", "makeEven", "value", "setResolutionScale", "", "Companion", "app_debug"})
public final class ResolutionManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String PREFS_NAME = "resolution_settings";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_RESOLUTION_SCALE = "resolution_scale_percent";
    private static final int DEFAULT_RESOLUTION_SCALE = 100;
    private static final int MIN_RESOLUTION_SCALE = 1;
    private static final int MAX_RESOLUTION_SCALE = 150;
    @kotlin.jvm.Volatile()
    @android.annotation.SuppressLint(value = {"StaticFieldLeak"})
    @org.jetbrains.annotations.Nullable()
    private static volatile com.example.castapp.manager.ResolutionManager INSTANCE;
    @org.jetbrains.annotations.NotNull()
    private final android.content.SharedPreferences sharedPreferences = null;
    @org.jetbrains.annotations.Nullable()
    private kotlin.Pair<java.lang.Integer, java.lang.Integer> cachedOriginalResolution;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.manager.ResolutionManager.Companion Companion = null;
    
    private ResolutionManager(android.content.Context context) {
        super();
    }
    
    /**
     * 获取设备原始屏幕分辨率
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlin.Pair<java.lang.Integer, java.lang.Integer> getOriginalScreenResolution() {
        return null;
    }
    
    /**
     * 获取状态栏高度
     * 使用WindowInsets API获取状态栏高度，避免使用内部资源
     */
    @android.annotation.SuppressLint(value = {"InternalInsetResource", "DiscouragedApi"})
    private final int getStatusBarHeight() {
        return 0;
    }
    
    /**
     * 获取可用屏幕分辨率（不包括状态栏）
     * 用于遥控端计算远程控制窗口尺寸
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlin.Pair<java.lang.Integer, java.lang.Integer> getUsableScreenResolution() {
        return null;
    }
    
    /**
     * 获取当前分辨率缩放百分比
     */
    public final int getCurrentResolutionScale() {
        return 0;
    }
    
    /**
     * 设置分辨率缩放百分比
     */
    public final boolean setResolutionScale(int scalePercent) {
        return false;
    }
    
    /**
     * 根据当前缩放百分比计算实际使用的分辨率
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlin.Pair<java.lang.Integer, java.lang.Integer> getCurrentResolution() {
        return null;
    }
    
    /**
     * 根据指定缩放百分比计算分辨率，保持等比缩放
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlin.Pair<java.lang.Integer, java.lang.Integer> calculateResolution(int scalePercent) {
        return null;
    }
    
    /**
     * 内部分辨率计算方法，保持等比缩放
     */
    private final kotlin.Pair<java.lang.Integer, java.lang.Integer> calculateResolutionInternal(int scalePercent) {
        return null;
    }
    
    /**
     * 根据指定缩放百分比计算屏幕捕获分辨率
     * 专门用于VirtualDisplay的动态分辨率调整，保持等比缩放
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlin.Pair<java.lang.Integer, java.lang.Integer> calculateCaptureResolution(int scalePercent) {
        return null;
    }
    
    /**
     * 确保数值为偶数（H.264编码要求）
     */
    private final int makeEven(int value) {
        return 0;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\f\u001a\u00020\u00062\u0006\u0010\r\u001a\u00020\u000eR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0005\u001a\u0004\u0018\u00010\u00068\u0002@\u0002X\u0083\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\bX\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000f"}, d2 = {"Lcom/example/castapp/manager/ResolutionManager$Companion;", "", "()V", "DEFAULT_RESOLUTION_SCALE", "", "INSTANCE", "Lcom/example/castapp/manager/ResolutionManager;", "KEY_RESOLUTION_SCALE", "", "MAX_RESOLUTION_SCALE", "MIN_RESOLUTION_SCALE", "PREFS_NAME", "getInstance", "context", "Landroid/content/Context;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.manager.ResolutionManager getInstance(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
            return null;
        }
    }
}