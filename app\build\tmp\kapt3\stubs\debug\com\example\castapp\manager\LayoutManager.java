package com.example.castapp.manager;

/**
 * 布局管理器
 * 负责窗口布局的保存、加载和恢复功能
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u008e\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000b\u0018\u0000 D2\u00020\u0001:\u0001DB\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J*\u0010\t\u001a\u00020\n2\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f2\u0012\u0010\u000e\u001a\u000e\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u00110\u000fH\u0002J\u0006\u0010\u0012\u001a\u00020\u0013JF\u0010\u0014\u001a\u00020\u00132\u0006\u0010\u0015\u001a\u00020\u001626\u0010\u0017\u001a2\u0012\u0013\u0012\u00110\n\u00a2\u0006\f\b\u0019\u0012\b\b\u001a\u0012\u0004\b\b(\u001b\u0012\u0013\u0012\u00110\u0010\u00a2\u0006\f\b\u0019\u0012\b\b\u001a\u0012\u0004\b\b(\u001c\u0012\u0004\u0012\u00020\u00130\u0018JL\u0010\u001d\u001a\u00020\u00132\f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00160\f26\u0010\u0017\u001a2\u0012\u0013\u0012\u00110\n\u00a2\u0006\f\b\u0019\u0012\b\b\u001a\u0012\u0004\b\b(\u001b\u0012\u0013\u0012\u00110\u0010\u00a2\u0006\f\b\u0019\u0012\b\b\u001a\u0012\u0004\b\b(\u001c\u0012\u0004\u0012\u00020\u00130\u0018J\u0014\u0010\u001f\u001a\u0010\u0012\n\u0012\b\u0012\u0004\u0012\u00020!0\f\u0018\u00010 J+\u0010\"\u001a\u00020\u00132#\u0010\u0017\u001a\u001f\u0012\u0015\u0012\u0013\u0018\u00010!\u00a2\u0006\f\b\u0019\u0012\b\b\u001a\u0012\u0004\b\b($\u0012\u0004\u0012\u00020\u00130#Jc\u0010%\u001a\u00020\u00132\u0006\u0010\u0015\u001a\u00020\u00162S\u0010\u0017\u001aO\u0012\u0013\u0012\u00110\n\u00a2\u0006\f\b\u0019\u0012\b\b\u001a\u0012\u0004\b\b(\u001b\u0012\u001b\u0012\u0019\u0012\u0004\u0012\u00020\u0011\u0018\u00010\f\u00a2\u0006\f\b\u0019\u0012\b\b\u001a\u0012\u0004\b\b(\'\u0012\u0013\u0012\u00110\u0010\u00a2\u0006\f\b\u0019\u0012\b\b\u001a\u0012\u0004\b\b(\u001c\u0012\u0004\u0012\u00020\u00130&J\u000e\u0010(\u001a\u00020\u00132\u0006\u0010)\u001a\u00020*JV\u0010+\u001a\u00020\u00132\u0006\u0010,\u001a\u00020-2\u0006\u0010\u0015\u001a\u00020\u00162\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f2\f\u0010.\u001a\b\u0012\u0004\u0012\u00020\u00110\f2\u0012\u0010\u000e\u001a\u000e\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u00110\u000f2\u0006\u0010)\u001a\u00020*H\u0082@\u00a2\u0006\u0002\u0010/J\\\u00100\u001a\u00020\u00132\u0006\u0010\u0015\u001a\u00020\u00162\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f2\u0006\u0010)\u001a\u00020*26\u0010\u0017\u001a2\u0012\u0013\u0012\u00110\n\u00a2\u0006\f\b\u0019\u0012\b\b\u001a\u0012\u0004\b\b(\u001b\u0012\u0013\u0012\u00110\u0010\u00a2\u0006\f\b\u0019\u0012\b\b\u001a\u0012\u0004\b\b(\u001c\u0012\u0004\u0012\u00020\u00130\u0018J\\\u00101\u001a\u00020\u00132\u0006\u00102\u001a\u00020\u00102\f\u00103\u001a\b\u0012\u0004\u0012\u00020\r0\f2\u0006\u0010)\u001a\u00020*26\u0010\u0017\u001a2\u0012\u0013\u0012\u00110\n\u00a2\u0006\f\b\u0019\u0012\b\b\u001a\u0012\u0004\b\b(\u001b\u0012\u0013\u0012\u00110\u0010\u00a2\u0006\f\b\u0019\u0012\b\b\u001a\u0012\u0004\b\b(\u001c\u0012\u0004\u0012\u00020\u00130\u0018JN\u00104\u001a\u00020\u00132\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u00105\u001a\u00020\n26\u0010\u0017\u001a2\u0012\u0013\u0012\u00110\n\u00a2\u0006\f\b\u0019\u0012\b\b\u001a\u0012\u0004\b\b(\u001b\u0012\u0013\u0012\u00110\u0010\u00a2\u0006\f\b\u0019\u0012\b\b\u001a\u0012\u0004\b\b(\u001c\u0012\u0004\u0012\u00020\u00130\u0018J0\u00106\u001a\u00020\u00132\u0006\u00107\u001a\u0002082\u0006\u00109\u001a\u00020:2\u0018\u0010;\u001a\u0014\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00110\f\u0012\u0004\u0012\u00020\u00130#J\u001c\u0010<\u001a\u00020\u00132\u0006\u00107\u001a\u0002082\f\u00103\u001a\b\u0012\u0004\u0012\u00020\r0\fJN\u0010=\u001a\u00020\u00132\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010>\u001a\u00020\u001026\u0010\u0017\u001a2\u0012\u0013\u0012\u00110\n\u00a2\u0006\f\b\u0019\u0012\b\b\u001a\u0012\u0004\b\b(\u001b\u0012\u0013\u0012\u00110\u0010\u00a2\u0006\f\b\u0019\u0012\b\b\u001a\u0012\u0004\b\b(\u001c\u0012\u0004\u0012\u00020\u00130\u0018J\\\u0010?\u001a\u00020\u00132\u0006\u0010\u0015\u001a\u00020\u00162\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f2\u0006\u0010)\u001a\u00020*26\u0010\u0017\u001a2\u0012\u0013\u0012\u00110\n\u00a2\u0006\f\b\u0019\u0012\b\b\u001a\u0012\u0004\b\b(\u001b\u0012\u0013\u0012\u00110\u0010\u00a2\u0006\f\b\u0019\u0012\b\b\u001a\u0012\u0004\b\b(\u001c\u0012\u0004\u0012\u00020\u00130\u0018JB\u0010@\u001a\u00020\u00132\u0006\u0010,\u001a\u00020-2\u0006\u0010\u0015\u001a\u00020\u00162\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f2\f\u0010.\u001a\b\u0012\u0004\u0012\u00020\u00110\f2\u0006\u0010)\u001a\u00020*H\u0082@\u00a2\u0006\u0002\u0010AJL\u0010B\u001a\u00020\u00132\f\u0010C\u001a\b\u0012\u0004\u0012\u00020!0\f26\u0010\u0017\u001a2\u0012\u0013\u0012\u00110\n\u00a2\u0006\f\b\u0019\u0012\b\b\u001a\u0012\u0004\b\b(\u001b\u0012\u0013\u0012\u00110\u0010\u00a2\u0006\f\b\u0019\u0012\b\b\u001a\u0012\u0004\b\b(\u001c\u0012\u0004\u0012\u00020\u00130\u0018R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0007\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006E"}, d2 = {"Lcom/example/castapp/manager/LayoutManager;", "", "()V", "database", "Lcom/example/castapp/database/CastAppDatabase;", "directorDialog", "Lcom/example/castapp/ui/dialog/DirectorDialog;", "saveLayoutDialog", "Lcom/example/castapp/ui/dialog/SaveLayoutDialog;", "checkIfLayerOrderChanged", "", "currentWindowInfoList", "", "Lcom/example/castapp/model/CastWindowInfo;", "existingItemsMap", "", "", "Lcom/example/castapp/database/entity/WindowLayoutItemEntity;", "cleanup", "", "deleteLayout", "layoutId", "", "callback", "Lkotlin/Function2;", "Lkotlin/ParameterName;", "name", "success", "message", "deleteLayouts", "layoutIds", "getAllLayouts", "Landroidx/lifecycle/LiveData;", "Lcom/example/castapp/database/entity/WindowLayoutEntity;", "getCurrentAppliedLayout", "Lkotlin/Function1;", "layout", "getLayoutDetails", "Lkotlin/Function3;", "items", "initialize", "context", "Landroid/content/Context;", "reassignAllDeviceLayers", "dao", "Lcom/example/castapp/database/dao/WindowLayoutDao;", "existingItems", "(Lcom/example/castapp/database/dao/WindowLayoutDao;JLjava/util/List;Ljava/util/List;Ljava/util/Map;Landroid/content/Context;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "replaceLayoutParameters", "saveLayout", "layoutName", "windowInfoList", "setLayoutAppliedStatus", "isApplied", "showDirectorDialog", "activity", "Landroidx/fragment/app/FragmentActivity;", "windowSettingsManager", "Lcom/example/castapp/manager/WindowSettingsManager;", "onRestoreLayout", "showSaveLayoutDialog", "updateLayoutName", "newLayoutName", "updateLayoutParameters", "updateLayoutWithMergedDevices", "(Lcom/example/castapp/database/dao/WindowLayoutDao;JLjava/util/List;Ljava/util/List;Landroid/content/Context;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateLayoutsOrder", "layouts", "Companion", "app_debug"})
public final class LayoutManager {
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.example.castapp.manager.LayoutManager INSTANCE;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.database.CastAppDatabase database;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.dialog.SaveLayoutDialog saveLayoutDialog;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.dialog.DirectorDialog directorDialog;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.manager.LayoutManager.Companion Companion = null;
    
    private LayoutManager() {
        super();
    }
    
    /**
     * 初始化管理器
     */
    public final void initialize(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    /**
     * 保存当前窗口布局
     * @param layoutName 布局名称
     * @param windowInfoList 当前窗口信息列表
     * @param callback 保存结果回调
     */
    public final void saveLayout(@org.jetbrains.annotations.NotNull()
    java.lang.String layoutName, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.castapp.model.CastWindowInfo> windowInfoList, @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Boolean, ? super java.lang.String, kotlin.Unit> callback) {
    }
    
    /**
     * 获取所有保存的布局列表
     */
    @org.jetbrains.annotations.Nullable()
    public final androidx.lifecycle.LiveData<java.util.List<com.example.castapp.database.entity.WindowLayoutEntity>> getAllLayouts() {
        return null;
    }
    
    /**
     * 获取指定布局的详细信息
     * @param layoutId 布局ID
     * @param callback 获取结果回调
     */
    public final void getLayoutDetails(long layoutId, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super java.lang.Boolean, ? super java.util.List<com.example.castapp.database.entity.WindowLayoutItemEntity>, ? super java.lang.String, kotlin.Unit> callback) {
    }
    
    /**
     * 更新布局名称
     * @param layoutId 布局ID
     * @param newLayoutName 新的布局名称
     * @param callback 更新结果回调
     */
    public final void updateLayoutName(long layoutId, @org.jetbrains.annotations.NotNull()
    java.lang.String newLayoutName, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Boolean, ? super java.lang.String, kotlin.Unit> callback) {
    }
    
    /**
     * 批量更新布局排序顺序
     * @param layouts 按新顺序排列的布局列表
     * @param callback 更新结果回调
     */
    public final void updateLayoutsOrder(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.castapp.database.entity.WindowLayoutEntity> layouts, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Boolean, ? super java.lang.String, kotlin.Unit> callback) {
    }
    
    /**
     * 删除指定布局
     * @param layoutId 布局ID
     * @param callback 删除结果回调
     */
    public final void deleteLayout(long layoutId, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Boolean, ? super java.lang.String, kotlin.Unit> callback) {
    }
    
    /**
     * 🐾 批量删除布局
     * @param layoutIds 布局ID列表
     * @param callback 删除结果回调
     */
    public final void deleteLayouts(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.Long> layoutIds, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Boolean, ? super java.lang.String, kotlin.Unit> callback) {
    }
    
    /**
     * 🐾 设置布局应用状态（新增）
     */
    public final void setLayoutAppliedStatus(long layoutId, boolean isApplied, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Boolean, ? super java.lang.String, kotlin.Unit> callback) {
    }
    
    /**
     * 🐾 获取当前应用的布局（新增）
     */
    public final void getCurrentAppliedLayout(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.castapp.database.entity.WindowLayoutEntity, kotlin.Unit> callback) {
    }
    
    /**
     * 更新布局参数（更新已有参数模式）
     * @param layoutId 布局ID
     * @param currentWindowInfoList 当前投屏窗口信息列表
     * @param context 上下文，用于获取媒体文件信息
     * @param callback 更新结果回调
     */
    public final void updateLayoutParameters(long layoutId, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.castapp.model.CastWindowInfo> currentWindowInfoList, @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Boolean, ? super java.lang.String, kotlin.Unit> callback) {
    }
    
    /**
     * 更新布局参数的核心方法：保留所有原有设备，更新当前投屏设备，插入新设备
     */
    private final java.lang.Object updateLayoutWithMergedDevices(com.example.castapp.database.dao.WindowLayoutDao dao, long layoutId, java.util.List<com.example.castapp.model.CastWindowInfo> currentWindowInfoList, java.util.List<com.example.castapp.database.entity.WindowLayoutItemEntity> existingItems, android.content.Context context, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 检查现有设备的层级顺序是否发生变化
     */
    private final boolean checkIfLayerOrderChanged(java.util.List<com.example.castapp.model.CastWindowInfo> currentWindowInfoList, java.util.Map<java.lang.String, com.example.castapp.database.entity.WindowLayoutItemEntity> existingItemsMap) {
        return false;
    }
    
    /**
     * 重新分配所有设备层级：保留原有设备，按当前投屏层级插入新设备
     * 示例：已保存布局：A(层级1)、B(层级2)、C(层级3)
     *     当前投屏：B(层级1)、D(层级2)、C(层级3)
     *     保存后布局：A(层级1)、B(层级2)、D(层级3)、C(层级4)
     */
    private final java.lang.Object reassignAllDeviceLayers(com.example.castapp.database.dao.WindowLayoutDao dao, long layoutId, java.util.List<com.example.castapp.model.CastWindowInfo> currentWindowInfoList, java.util.List<com.example.castapp.database.entity.WindowLayoutItemEntity> existingItems, java.util.Map<java.lang.String, com.example.castapp.database.entity.WindowLayoutItemEntity> existingItemsMap, android.content.Context context, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 替换布局参数（清空重新保存模式）
     * @param layoutId 布局ID
     * @param currentWindowInfoList 当前投屏窗口信息列表
     * @param context 上下文，用于获取媒体文件信息
     * @param callback 替换结果回调
     */
    public final void replaceLayoutParameters(long layoutId, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.castapp.model.CastWindowInfo> currentWindowInfoList, @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Boolean, ? super java.lang.String, kotlin.Unit> callback) {
    }
    
    /**
     * 显示保存布局对话框
     */
    public final void showSaveLayoutDialog(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.FragmentActivity activity, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.castapp.model.CastWindowInfo> windowInfoList) {
    }
    
    /**
     * 显示导播台BottomSheet对话框
     */
    public final void showDirectorDialog(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.FragmentActivity activity, @org.jetbrains.annotations.NotNull()
    com.example.castapp.manager.WindowSettingsManager windowSettingsManager, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.util.List<com.example.castapp.database.entity.WindowLayoutItemEntity>, kotlin.Unit> onRestoreLayout) {
    }
    
    /**
     * 清理资源
     */
    public final void cleanup() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0005\u001a\u00020\u0004R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0006"}, d2 = {"Lcom/example/castapp/manager/LayoutManager$Companion;", "", "()V", "INSTANCE", "Lcom/example/castapp/manager/LayoutManager;", "getInstance", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.manager.LayoutManager getInstance() {
            return null;
        }
    }
}