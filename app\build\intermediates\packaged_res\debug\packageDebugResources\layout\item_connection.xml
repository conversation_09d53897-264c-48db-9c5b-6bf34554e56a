<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="4dp"
    android:layout_marginEnd="4dp"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="3dp"
    app:cardBackgroundColor="#F8F8F8">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="4dp">

        <!-- 顶部：连接状态、地址、状态信息和删除按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginTop="4dp">

            <View
                android:id="@+id/connection_status_indicator"
                android:layout_width="8dp"
                android:layout_height="8dp"
                android:background="@drawable/connection_status_indicator"
                android:layout_marginEnd="12dp" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/connection_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="127.0.0.1:8888"
                    android:textSize="13sp"
                    android:textColor="#333333" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="2dp">

                    <TextView
                        android:id="@+id/connection_id_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="ID: 0.1_8888"
                        android:textSize="10sp"
                        android:textColor="#888888" />

                    <TextView
                        android:id="@+id/connection_status_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="未投屏"
                        android:textSize="10sp"
                        android:textColor="#888888"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

            </LinearLayout>

            <!-- 右上角按钮组：编辑和删除 -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_gravity="top">

                <!-- 编辑按钮 -->
                <ImageButton
                    android:id="@+id/edit_button"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:padding="4dp"
                    android:background="@drawable/ic_edit"
                    android:alpha="0.7"
                    android:scaleType="centerInside"
                    android:layout_marginEnd="8dp"
                    android:contentDescription="编辑连接" />

                <!-- 删除按钮 -->
                <ImageButton
                    android:id="@+id/disconnect_button"
                    android:layout_width="22dp"
                    android:layout_height="22dp"
                    android:padding="4dp"
                    android:background="@drawable/ic_delete"
                    android:alpha="0.7"
                    android:scaleType="centerInside"
                    android:contentDescription="删除连接" />

            </LinearLayout>

        </LinearLayout>

        <!-- 第二行：三个开关显示在右侧 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end|center_vertical"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="4dp">

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/cast_switch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="投屏"
                android:textSize="12sp"
                android:checked="false"
                android:scaleX="0.9"
                android:scaleY="0.9"/>

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/media_audio_switch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="媒体音频"
                android:textSize="12sp"
                android:checked="false"
                android:scaleX="0.9"
                android:scaleY="0.9"/>

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/mic_audio_switch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="麦克风"
                android:textSize="12sp"
                android:checked="false"
                android:scaleX="0.9"
                android:scaleY="0.9" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
