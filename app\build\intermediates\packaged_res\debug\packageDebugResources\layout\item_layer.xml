<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="#F5F5F5">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        android:gravity="center_vertical">

        <!-- 设备信息区域 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- 排序序号 -->
            <TextView
                android:id="@+id/tv_order_number"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="1."
                android:textStyle="bold"
                android:textSize="14sp"
                android:textColor="#FF9800"
                android:layout_marginEnd="2dp"
                android:minWidth="25dp" />

            <!-- 设备信息容器 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <!-- 设备名称和ID同一行 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/tv_device_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="realme RMX3366"
                        android:textStyle="bold"
                        android:textSize="13sp"
                        android:textColor="#2196F3"
                        android:maxLines="1"
                        android:ellipsize="end"
                        android:maxWidth="140dp" />

                    <TextView
                        android:id="@+id/tv_connection_id"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="（ID:d7s4e412）"
                        android:textSize="12sp"
                        android:textColor="#666666"
                        android:maxLines="1"
                        android:ellipsize="end" />

                </LinearLayout>

                <!-- 🏷️ 设备备注行 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="2dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="备注："
                        android:textSize="10sp"
                        android:textColor="#666666" />

                    <TextView
                        android:id="@+id/tv_device_note"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="无"
                        android:textSize="10sp"
                        android:textColor="#666666"
                        android:background="?android:attr/selectableItemBackground"
                        android:padding="2dp"
                        android:maxLines="2"
                        android:ellipsize="end"
                        android:lineSpacingExtra="1dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:contentDescription="点击编辑设备备注" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <!-- 层级拖动手柄图标 -->
        <ImageView
            android:id="@+id/drag_handle"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="center_vertical"
            android:src="@drawable/ic_drag_handle"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="8dp"
            android:contentDescription="拖动以调整层级" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
