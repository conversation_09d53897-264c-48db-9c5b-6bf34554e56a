<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:background="?android:attr/selectableItemBackground"
    android:padding="8dp"
    android:layout_marginBottom="2dp">

    <!-- 字体名称显示 -->
    <TextView
        android:id="@+id/tv_font_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="Roboto"
        android:textSize="14sp"
        android:textColor="#333333"
        android:gravity="start"
        android:layout_marginEnd="8dp" />

    <!-- 预设标识 -->
    <TextView
        android:id="@+id/tv_preset_tag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="预设"
        android:textSize="10sp"
        android:textColor="#4CAF50"
        android:background="#E8F5E8"
        android:padding="4dp"
        android:layout_marginEnd="8dp"
        android:visibility="gone" />

    <!-- 当前选中标识 -->
    <ImageView
        android:id="@+id/iv_current_indicator"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:src="@drawable/ic_check"
        android:layout_marginEnd="8dp"
        android:visibility="gone"
        app:tint="#4CAF50" />

    <!-- 修改按钮（仅自定义字体显示） -->
    <ImageView
        android:id="@+id/btn_edit"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:src="@drawable/ic_edit"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:padding="4dp"
        android:layout_marginEnd="4dp"
        android:visibility="gone"
        app:tint="#FF9800" />

</LinearLayout>
