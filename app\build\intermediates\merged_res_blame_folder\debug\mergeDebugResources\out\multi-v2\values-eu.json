{"logs": [{"outputFile": "com.example.castapp-mergeDebugResources-42:/values-eu/values-eu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f3713a0c8f8ac4a98c195ead755fc68d\\transformed\\appcompat-1.7.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,312,422,508,614,738,824,905,997,1091,1187,1281,1382,1476,1572,1669,1761,1854,1936,2045,2154,2253,2362,2469,2580,2751,2850", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "209,307,417,503,609,733,819,900,992,1086,1182,1276,1377,1471,1567,1664,1756,1849,1931,2040,2149,2248,2357,2464,2575,2746,2845,2928"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "325,434,532,642,728,834,958,1044,1125,1217,1311,1407,1501,1602,1696,1792,1889,1981,2074,2156,2265,2374,2473,2582,2689,2800,2971,9673", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "429,527,637,723,829,953,1039,1120,1212,1306,1402,1496,1597,1691,1787,1884,1976,2069,2151,2260,2369,2468,2577,2684,2795,2966,3065,9751"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b74c097ccea204c2309e4f5f5bb2f587\\transformed\\core-1.13.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,564,667,786", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "148,251,351,454,559,662,781,882"}, "to": {"startLines": "38,39,40,41,42,43,44,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3569,3667,3770,3870,3973,4078,4181,9995", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "3662,3765,3865,3968,4073,4176,4295,10091"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\dbc1af71e9c7a6d81d74e5415e27cca6\\transformed\\material-1.12.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,275,382,487,567,674,774,872,987,1070,1136,1203,1302,1370,1431,1519,1582,1648,1712,1783,1846,1900,2009,2068,2131,2185,2259,2384,2474,2552,2641,2724,2804,2949,3032,3114,3252,3343,3426,3478,3531,3597,3668,3748,3819,3899,3977,4055,4128,4203,4310,4397,4484,4575,4668,4740,4816,4908,4959,5041,5107,5191,5277,5339,5403,5466,5534,5641,5750,5846,5951,6007,6064,6147,6232,6309", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,106,104,79,106,99,97,114,82,65,66,98,67,60,87,62,65,63,70,62,53,108,58,62,53,73,124,89,77,88,82,79,144,82,81,137,90,82,51,52,65,70,79,70,79,77,77,72,74,106,86,86,90,92,71,75,91,50,81,65,83,85,61,63,62,67,106,108,95,104,55,56,82,84,76,76", "endOffsets": "270,377,482,562,669,769,867,982,1065,1131,1198,1297,1365,1426,1514,1577,1643,1707,1778,1841,1895,2004,2063,2126,2180,2254,2379,2469,2547,2636,2719,2799,2944,3027,3109,3247,3338,3421,3473,3526,3592,3663,3743,3814,3894,3972,4050,4123,4198,4305,4392,4479,4570,4663,4735,4811,4903,4954,5036,5102,5186,5272,5334,5398,5461,5529,5636,5745,5841,5946,6002,6059,6142,6227,6304,6381"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3070,3177,3282,3362,3469,4300,4398,4513,4596,4662,4729,4828,4896,4957,5045,5108,5174,5238,5309,5372,5426,5535,5594,5657,5711,5785,5910,6000,6078,6167,6250,6330,6475,6558,6640,6778,6869,6952,7004,7057,7123,7194,7274,7345,7425,7503,7581,7654,7729,7836,7923,8010,8101,8194,8266,8342,8434,8485,8567,8633,8717,8803,8865,8929,8992,9060,9167,9276,9372,9477,9533,9590,9756,9841,9918", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "endColumns": "12,106,104,79,106,99,97,114,82,65,66,98,67,60,87,62,65,63,70,62,53,108,58,62,53,73,124,89,77,88,82,79,144,82,81,137,90,82,51,52,65,70,79,70,79,77,77,72,74,106,86,86,90,92,71,75,91,50,81,65,83,85,61,63,62,67,106,108,95,104,55,56,82,84,76,76", "endOffsets": "320,3172,3277,3357,3464,3564,4393,4508,4591,4657,4724,4823,4891,4952,5040,5103,5169,5233,5304,5367,5421,5530,5589,5652,5706,5780,5905,5995,6073,6162,6245,6325,6470,6553,6635,6773,6864,6947,6999,7052,7118,7189,7269,7340,7420,7498,7576,7649,7724,7831,7918,8005,8096,8189,8261,8337,8429,8480,8562,8628,8712,8798,8860,8924,8987,9055,9162,9271,9367,9472,9528,9585,9668,9836,9913,9990"}}]}]}