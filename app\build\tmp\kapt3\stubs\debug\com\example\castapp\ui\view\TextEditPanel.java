package com.example.castapp.ui.view;

/**
 * 文本编辑控制面板
 * 提供文本内容编辑和格式化功能，支持拖动
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00dc\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010!\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\bH\n\u0002\u0018\u0002\n\u0002\b\u0011\u0018\u00002\u00020\u00012\u00020\u00022\u00020\u0003:\n\u00c1\u0001\u00c2\u0001\u00c3\u0001\u00c4\u0001\u00c5\u0001B\u0015\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u0006\u0010m\u001a\u00020IJ\u0010\u0010n\u001a\u00020\u00162\u0006\u0010o\u001a\u00020\u0016H\u0002J\u0010\u0010p\u001a\u00020I2\u0006\u0010q\u001a\u00020\u0016H\u0002J\u0010\u0010r\u001a\u00020I2\u0006\u0010s\u001a\u00020\u0018H\u0002J\u0010\u0010t\u001a\u00020I2\u0006\u0010u\u001a\u00020\u0018H\u0002J\u0006\u0010v\u001a\u00020IJ\b\u0010w\u001a\u00020IH\u0002J\u0016\u0010x\u001a\u00020I2\u0006\u0010y\u001a\u00020\u001c2\u0006\u0010z\u001a\u00020\u0016J\u0006\u0010{\u001a\u00020\u001cJ\b\u0010|\u001a\u00020IH\u0002J\b\u0010}\u001a\u00020IH\u0002J\b\u0010~\u001a\u00020IH\u0002J\b\u0010\u007f\u001a\u00020IH\u0002J\t\u0010\u0080\u0001\u001a\u00020IH\u0002J\u0011\u0010\u0081\u0001\u001a\u00020I2\u0006\u0010q\u001a\u00020\u0016H\u0016J\u0011\u0010\u0082\u0001\u001a\u00020I2\u0006\u0010q\u001a\u00020\u0016H\u0016J\t\u0010\u0083\u0001\u001a\u00020IH\u0016J\u0011\u0010\u0084\u0001\u001a\u00020I2\u0006\u0010s\u001a\u00020\u0018H\u0016J\u0011\u0010\u0085\u0001\u001a\u00020I2\u0006\u0010s\u001a\u00020\u0018H\u0016J\t\u0010\u0086\u0001\u001a\u00020IH\u0016J\u0011\u0010\u0087\u0001\u001a\u00020I2\u0006\u0010u\u001a\u00020\u0018H\u0016J\u0011\u0010\u0088\u0001\u001a\u00020I2\u0006\u0010u\u001a\u00020\u0018H\u0016J\t\u0010\u0089\u0001\u001a\u00020IH\u0016J\t\u0010\u008a\u0001\u001a\u00020IH\u0002J\t\u0010\u008b\u0001\u001a\u00020IH\u0002J\t\u0010\u008c\u0001\u001a\u00020IH\u0002J\u0016\u0010\u008d\u0001\u001a\u00020I2\r\u0010\u008e\u0001\u001a\b\u0012\u0004\u0012\u00020I0HJ\u0016\u0010\u008f\u0001\u001a\u00020I2\r\u0010\u008e\u0001\u001a\b\u0012\u0004\u0012\u00020I0HJ\u001c\u0010\u0090\u0001\u001a\u00020I2\u0013\u0010\u008e\u0001\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020I0LJ\u001c\u0010\u0091\u0001\u001a\u00020I2\u0013\u0010\u008e\u0001\u001a\u000e\u0012\u0004\u0012\u00020\u0014\u0012\u0004\u0012\u00020I0LJ\u001c\u0010\u0092\u0001\u001a\u00020I2\u0013\u0010\u008e\u0001\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020I0LJ\"\u0010\u0093\u0001\u001a\u00020I2\u0019\u0010\u008e\u0001\u001a\u0014\u0012\u0004\u0012\u00020\u001c\u0012\u0004\u0012\u00020\u001c\u0012\u0004\u0012\u00020I0PJ\u001c\u0010\u0094\u0001\u001a\u00020I2\u0013\u0010\u008e\u0001\u001a\u000e\u0012\u0004\u0012\u00020\u0018\u0012\u0004\u0012\u00020I0LJ\u001c\u0010\u0095\u0001\u001a\u00020I2\u0013\u0010\u008e\u0001\u001a\u000e\u0012\u0004\u0012\u00020\u0018\u0012\u0004\u0012\u00020I0LJ(\u0010\u0096\u0001\u001a\u00020I2\u001f\u0010\u008e\u0001\u001a\u001a\u0012\u0004\u0012\u00020\u001c\u0012\u0004\u0012\u00020\u0018\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020I0TJ\u001c\u0010\u0097\u0001\u001a\u00020I2\u0013\u0010\u008e\u0001\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020I0LJ\u0016\u0010\u0098\u0001\u001a\u00020I2\r\u0010\u008e\u0001\u001a\b\u0012\u0004\u0012\u00020I0HJ@\u0010\u0099\u0001\u001a\u00020I27\u0010\u008e\u0001\u001a2\u0012\u0013\u0012\u00110\u0018\u00a2\u0006\f\bX\u0012\b\bY\u0012\u0004\b\b(Z\u0012\u0013\u0012\u00110\u0018\u00a2\u0006\f\bX\u0012\b\bY\u0012\u0004\b\b([\u0012\u0004\u0012\u00020I0PJ\"\u0010\u009a\u0001\u001a\u00020I2\u0019\u0010\u008e\u0001\u001a\u0014\u0012\u0004\u0012\u00020\u001c\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020I0PJ\t\u0010\u009b\u0001\u001a\u00020IH\u0002J\t\u0010\u009c\u0001\u001a\u00020IH\u0002J\t\u0010\u009d\u0001\u001a\u00020IH\u0002J\t\u0010\u009e\u0001\u001a\u00020IH\u0002J\t\u0010\u009f\u0001\u001a\u00020IH\u0002J\t\u0010\u00a0\u0001\u001a\u00020IH\u0002J\t\u0010\u00a1\u0001\u001a\u00020IH\u0002J^\u0010\u00a2\u0001\u001a\u00020I2\t\b\u0002\u0010\u00a3\u0001\u001a\u00020*2\t\b\u0002\u0010\u00a4\u0001\u001a\u00020\u001c2\t\b\u0002\u0010\u00a5\u0001\u001a\u00020\u001c2\b\b\u0002\u0010q\u001a\u00020\u00162\u000b\b\u0002\u0010\u00a6\u0001\u001a\u0004\u0018\u00010\u00142\b\b\u0002\u0010s\u001a\u00020\u00182\b\b\u0002\u0010u\u001a\u00020\u00182\t\b\u0002\u0010\u00a7\u0001\u001a\u00020\u0016J\t\u0010\u00a8\u0001\u001a\u00020IH\u0002J\t\u0010\u00a9\u0001\u001a\u00020IH\u0002J\t\u0010\u00aa\u0001\u001a\u00020IH\u0002J\t\u0010\u00ab\u0001\u001a\u00020IH\u0002J\t\u0010\u00ac\u0001\u001a\u00020IH\u0002J\t\u0010\u00ad\u0001\u001a\u00020IH\u0002J\t\u0010\u00ae\u0001\u001a\u00020IH\u0002J\u001b\u0010\u00af\u0001\u001a\u00020I2\u0007\u0010\u00b0\u0001\u001a\u00020\n2\u0007\u0010\u00b1\u0001\u001a\u00020\u001cH\u0002J\u0086\u0001\u0010\u00b2\u0001\u001a\u00020I2\u0007\u0010\u00a4\u0001\u001a\u00020\u001c2\u0007\u0010\u00a5\u0001\u001a\u00020\u001c2\u0006\u0010q\u001a\u00020\u00162\u000b\b\u0002\u0010\u00b3\u0001\u001a\u0004\u0018\u00010\u00162\u001e\b\u0002\u0010\u00b4\u0001\u001a\u0017\u0012\u0004\u0012\u00020\u001c\u0012\u0004\u0012\u00020\u0018\u0012\u0004\u0012\u00020\u0016\u0018\u00010\u00b5\u00012\u000b\b\u0002\u0010\u00a6\u0001\u001a\u0004\u0018\u00010\u00142\n\b\u0002\u0010s\u001a\u0004\u0018\u00010\u00182\n\b\u0002\u0010u\u001a\u0004\u0018\u00010\u00182\u000b\b\u0002\u0010\u00a7\u0001\u001a\u0004\u0018\u00010\u0016\u00a2\u0006\u0003\u0010\u00b6\u0001J\u0011\u0010\u00b7\u0001\u001a\u00020I2\u0006\u0010z\u001a\u00020\u0016H\u0002J\u0014\u0010\u00b8\u0001\u001a\u00020I2\t\u0010\u00b9\u0001\u001a\u0004\u0018\u00010\u0014H\u0002J\u0011\u0010\u00ba\u0001\u001a\u00020I2\u0006\u0010s\u001a\u00020\u0018H\u0002J\u0011\u0010\u00bb\u0001\u001a\u00020I2\u0006\u0010u\u001a\u00020\u0018H\u0002J\u0011\u0010\u00bc\u0001\u001a\u00020I2\u0006\u0010q\u001a\u00020\u0016H\u0002J\u0019\u0010\u00bd\u0001\u001a\u00020I2\u0006\u0010y\u001a\u00020\u001c2\u0006\u0010z\u001a\u00020\u0016H\u0002J\u0012\u0010\u00be\u0001\u001a\u00020I2\u0007\u0010\u00bf\u0001\u001a\u00020\u0016H\u0002J\u0019\u0010\u00c0\u0001\u001a\u00020I2\u0006\u0010y\u001a\u00020\u001c2\u0006\u0010z\u001a\u00020\u0016H\u0002R\u000e\u0010\t\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0013\u001a\u0004\u0018\u00010\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001e\u001a\u00020\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0012\u0010\u001f\u001a\u0004\u0018\u00010\u0016X\u0082\u000e\u00a2\u0006\u0004\n\u0002\u0010 R\u000e\u0010!\u001a\u00020\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\"\u001a\u00020\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010#\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u0012\u0010$\u001a\u00060%R\u00020\u0000X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010&\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\'\u001a\b\u0012\u0004\u0012\u00020\u00140(X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010)\u001a\b\u0012\u0004\u0012\u00020*0(X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0012\u0010+\u001a\u00060,R\u00020\u0000X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010-\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010.\u001a\b\u0012\u0004\u0012\u00020*0(X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010/\u001a\u000200X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u00101\u001a\b\u0012\u0004\u0012\u00020\u00160(X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u00102\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00103\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00104\u001a\u00020\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00105\u001a\u00020\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00106\u001a\u00020\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00107\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00108\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0012\u00109\u001a\u00060:R\u00020\u0000X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010;\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010<\u001a\b\u0012\u0004\u0012\u00020*0(X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010=\u001a\u00020>X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010?\u001a\b\u0012\u0004\u0012\u00020\u00180(X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0012\u0010@\u001a\u00060AR\u00020\u0000X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010B\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010C\u001a\b\u0012\u0004\u0012\u00020*0(X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010D\u001a\u00020EX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010F\u001a\b\u0012\u0004\u0012\u00020\u00180(X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010G\u001a\n\u0012\u0004\u0012\u00020I\u0018\u00010HX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010J\u001a\n\u0012\u0004\u0012\u00020I\u0018\u00010HX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010K\u001a\u0010\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020I\u0018\u00010LX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010M\u001a\u0010\u0012\u0004\u0012\u00020\u0014\u0012\u0004\u0012\u00020I\u0018\u00010LX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010N\u001a\u0010\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020I\u0018\u00010LX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\"\u0010O\u001a\u0016\u0012\u0004\u0012\u00020\u001c\u0012\u0004\u0012\u00020\u001c\u0012\u0004\u0012\u00020I\u0018\u00010PX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010Q\u001a\u0010\u0012\u0004\u0012\u00020\u0018\u0012\u0004\u0012\u00020I\u0018\u00010LX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010R\u001a\u0010\u0012\u0004\u0012\u00020\u0018\u0012\u0004\u0012\u00020I\u0018\u00010LX\u0082\u000e\u00a2\u0006\u0002\n\u0000R(\u0010S\u001a\u001c\u0012\u0004\u0012\u00020\u001c\u0012\u0004\u0012\u00020\u0018\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020I\u0018\u00010TX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010U\u001a\u0010\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020I\u0018\u00010LX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010V\u001a\n\u0012\u0004\u0012\u00020I\u0018\u00010HX\u0082\u000e\u00a2\u0006\u0002\n\u0000R@\u0010W\u001a4\u0012\u0013\u0012\u00110\u0018\u00a2\u0006\f\bX\u0012\b\bY\u0012\u0004\b\b(Z\u0012\u0013\u0012\u00110\u0018\u00a2\u0006\f\bX\u0012\b\bY\u0012\u0004\b\b([\u0012\u0004\u0012\u00020I\u0018\u00010PX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\"\u0010\\\u001a\u0016\u0012\u0004\u0012\u00020\u001c\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020I\u0018\u00010PX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010]\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010^\u001a\u00020_X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010`\u001a\u00020_X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010a\u001a\u00020_X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010b\u001a\u00020_X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010c\u001a\u00020_X\u0082.\u00a2\u0006\u0002\n\u0000R\u0012\u0010d\u001a\u00060eR\u00020\u0000X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010f\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010g\u001a\b\u0012\u0004\u0012\u00020\u00160(X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010h\u001a\b\u0012\u0004\u0012\u00020*0(X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010i\u001a\b\u0012\u0004\u0012\u00020\u00160(X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010j\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010k\u001a\u00020lX\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u00c6\u0001"}, d2 = {"Lcom/example/castapp/ui/view/TextEditPanel;", "Lcom/example/castapp/utils/FontSizePresetManager$FontSizePresetListener;", "Lcom/example/castapp/utils/LetterSpacingPresetManager$LetterSpacingPresetListener;", "Lcom/example/castapp/utils/LineSpacingPresetManager$LineSpacingPresetListener;", "context", "Landroid/content/Context;", "parentContainer", "Landroid/view/ViewGroup;", "(Landroid/content/Context;Landroid/view/ViewGroup;)V", "btnBold", "Landroid/widget/LinearLayout;", "btnClearFormat", "btnClosePanel", "Landroid/widget/ImageView;", "btnColor", "btnDragWindow", "btnItalic", "btnStroke", "btnWindowColor", "currentFontFamily", "Lcom/example/castapp/utils/FontPresetManager$FontItem;", "currentFontSize", "", "currentLetterSpacing", "", "currentLineSpacing", "currentStrokeColor", "currentStrokeEnabled", "", "currentStrokeWidth", "currentTextAlignment", "currentTextColor", "Ljava/lang/Integer;", "currentWindowColor", "currentWindowColorEnabled", "dragHandle", "fontFamilyAdapter", "Lcom/example/castapp/ui/view/TextEditPanel$FontFamilyAdapter;", "fontFamilyContainer", "fontFamilyItems", "", "fontFamilyOptions", "", "fontSizeAdapter", "Lcom/example/castapp/ui/view/TextEditPanel$FontSizeAdapter;", "fontSizeContainer", "fontSizeOptions", "fontSizePresetManager", "Lcom/example/castapp/utils/FontSizePresetManager;", "fontSizeValues", "initialX", "initialY", "isBoldEnabled", "isDragging", "isItalicEnabled", "lastTouchX", "lastTouchY", "letterSpacingAdapter", "Lcom/example/castapp/ui/view/TextEditPanel$LetterSpacingAdapter;", "letterSpacingContainer", "letterSpacingOptions", "letterSpacingPresetManager", "Lcom/example/castapp/utils/LetterSpacingPresetManager;", "letterSpacingValues", "lineSpacingAdapter", "Lcom/example/castapp/ui/view/TextEditPanel$LineSpacingAdapter;", "lineSpacingContainer", "lineSpacingOptions", "lineSpacingPresetManager", "Lcom/example/castapp/utils/LineSpacingPresetManager;", "lineSpacingValues", "onClearFormatListener", "Lkotlin/Function0;", "", "onCloseListener", "onColorChangeListener", "Lkotlin/Function1;", "onFontFamilyChangeListener", "onFontSizeChangeListener", "onFormatChangeListener", "Lkotlin/Function2;", "onLetterSpacingChangeListener", "onLineSpacingChangeListener", "onStrokeChangeListener", "Lkotlin/Function3;", "onTextAlignmentChangeListener", "onTextWindowDragEndListener", "onTextWindowDragListener", "Lkotlin/ParameterName;", "name", "deltaX", "deltaY", "onWindowColorChangeListener", "panelView", "spinnerFontFamily", "Landroid/widget/Spinner;", "spinnerFontSize", "spinnerLetterSpacing", "spinnerLineSpacing", "spinnerTextAlignment", "textAlignmentAdapter", "Lcom/example/castapp/ui/view/TextEditPanel$TextAlignmentAdapter;", "textAlignmentContainer", "textAlignmentIcons", "textAlignmentOptions", "textAlignmentValues", "titleBar", "tvPanelTitle", "Landroid/widget/TextView;", "destroy", "findClosestPresetFontSize", "targetFontSize", "handleFontSizeSelection", "fontSize", "handleLetterSpacingSelection", "letterSpacing", "handleLineSpacingSelection", "lineSpacing", "hide", "initializePanel", "initializeWindowColorState", "enabled", "color", "isShowing", "loadFontFamilyList", "loadFontSizeListFromManager", "loadLetterSpacingListFromManager", "loadLineSpacingListFromManager", "loadTextAlignmentList", "onFontSizeAdded", "onFontSizeDeleted", "onFontSizeListReset", "onLetterSpacingAdded", "onLetterSpacingDeleted", "onLetterSpacingListReset", "onLineSpacingAdded", "onLineSpacingDeleted", "onLineSpacingListReset", "resetColorButtonState", "resetStrokeButtonState", "resetWindowColorButtonState", "setOnClearFormatListener", "listener", "setOnCloseListener", "setOnColorChangeListener", "setOnFontFamilyChangeListener", "setOnFontSizeChangeListener", "setOnFormatChangeListener", "setOnLetterSpacingChangeListener", "setOnLineSpacingChangeListener", "setOnStrokeChangeListener", "setOnTextAlignmentChangeListener", "setOnTextWindowDragEndListener", "setOnTextWindowDragListener", "setOnWindowColorChangeListener", "setupClickListeners", "setupDragFunctionality", "setupFontFamilySpinner", "setupFontSizeSpinner", "setupLetterSpacingSpinner", "setupLineSpacingSpinner", "setupTextAlignmentSpinner", "show", "currentText", "bold", "italic", "fontFamily", "textAlignment", "showColorPickerDialog", "showFontSettingsDialog", "showFontSizeSettingsDialog", "showLetterSpacingSettingsDialog", "showLineSpacingSettingsDialog", "showStrokeColorPickerDialog", "showWindowColorPickerDialog", "updateButtonState", "buttonLayout", "isEnabled", "updateButtonStatesFromSelection", "textColor", "strokeState", "Lkotlin/Triple;", "(ZZILjava/lang/Integer;Lkotlin/Triple;Lcom/example/castapp/utils/FontPresetManager$FontItem;Ljava/lang/Float;Ljava/lang/Float;Ljava/lang/Integer;)V", "updateColorButtonState", "updateFontFamilySelection", "fontItem", "updateLetterSpacingSpinnerSelection", "updateLineSpacingSpinnerSelection", "updateSpinnerSelection", "updateStrokeButtonState", "updateTextAlignmentSelection", "alignment", "updateWindowColorButtonState", "FontFamilyAdapter", "FontSizeAdapter", "LetterSpacingAdapter", "LineSpacingAdapter", "TextAlignmentAdapter", "app_debug"})
public final class TextEditPanel implements com.example.castapp.utils.FontSizePresetManager.FontSizePresetListener, com.example.castapp.utils.LetterSpacingPresetManager.LetterSpacingPresetListener, com.example.castapp.utils.LineSpacingPresetManager.LineSpacingPresetListener {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final android.view.ViewGroup parentContainer = null;
    private android.widget.LinearLayout panelView;
    private android.widget.LinearLayout titleBar;
    private android.widget.ImageView dragHandle;
    private android.widget.TextView tvPanelTitle;
    private android.widget.ImageView btnClosePanel;
    private android.widget.LinearLayout btnBold;
    private android.widget.LinearLayout btnItalic;
    private android.widget.LinearLayout fontSizeContainer;
    private android.widget.Spinner spinnerFontSize;
    private android.widget.LinearLayout letterSpacingContainer;
    private android.widget.Spinner spinnerLetterSpacing;
    private android.widget.LinearLayout lineSpacingContainer;
    private android.widget.Spinner spinnerLineSpacing;
    private android.widget.LinearLayout fontFamilyContainer;
    private android.widget.Spinner spinnerFontFamily;
    private android.widget.LinearLayout textAlignmentContainer;
    private android.widget.Spinner spinnerTextAlignment;
    private android.widget.LinearLayout btnColor;
    private android.widget.LinearLayout btnStroke;
    private android.widget.LinearLayout btnWindowColor;
    private android.widget.LinearLayout btnClearFormat;
    private android.widget.LinearLayout btnDragWindow;
    private boolean isDragging = false;
    private float lastTouchX = 0.0F;
    private float lastTouchY = 0.0F;
    private float initialX = 0.0F;
    private float initialY = 0.0F;
    private boolean isBoldEnabled = false;
    private boolean isItalicEnabled = false;
    private int currentFontSize = 13;
    @org.jetbrains.annotations.Nullable()
    private java.lang.Integer currentTextColor;
    private boolean currentStrokeEnabled = false;
    private float currentStrokeWidth = 4.0F;
    private int currentStrokeColor = -16777216;
    private boolean currentWindowColorEnabled = false;
    private int currentWindowColor = -1;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.utils.FontSizePresetManager fontSizePresetManager = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> fontSizeOptions = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.Integer> fontSizeValues = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.castapp.utils.FontPresetManager.FontItem> fontFamilyItems = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> fontFamilyOptions = null;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.utils.FontPresetManager.FontItem currentFontFamily;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.utils.LetterSpacingPresetManager letterSpacingPresetManager = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> letterSpacingOptions = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.Float> letterSpacingValues = null;
    private float currentLetterSpacing = 0.0F;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.utils.LineSpacingPresetManager lineSpacingPresetManager = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> lineSpacingOptions = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.Float> lineSpacingValues = null;
    private float currentLineSpacing = 0.0F;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> textAlignmentOptions = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.Integer> textAlignmentValues = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.Integer> textAlignmentIcons = null;
    private int currentTextAlignment = 17;
    private com.example.castapp.ui.view.TextEditPanel.FontSizeAdapter fontSizeAdapter;
    private com.example.castapp.ui.view.TextEditPanel.FontFamilyAdapter fontFamilyAdapter;
    private com.example.castapp.ui.view.TextEditPanel.TextAlignmentAdapter textAlignmentAdapter;
    private com.example.castapp.ui.view.TextEditPanel.LetterSpacingAdapter letterSpacingAdapter;
    private com.example.castapp.ui.view.TextEditPanel.LineSpacingAdapter lineSpacingAdapter;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function2<? super java.lang.Boolean, ? super java.lang.Boolean, kotlin.Unit> onFormatChangeListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onFontSizeChangeListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super com.example.castapp.utils.FontPresetManager.FontItem, kotlin.Unit> onFontFamilyChangeListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super java.lang.Float, kotlin.Unit> onLetterSpacingChangeListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super java.lang.Float, kotlin.Unit> onLineSpacingChangeListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onTextAlignmentChangeListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onColorChangeListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function3<? super java.lang.Boolean, ? super java.lang.Float, ? super java.lang.Integer, kotlin.Unit> onStrokeChangeListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function2<? super java.lang.Boolean, ? super java.lang.Integer, kotlin.Unit> onWindowColorChangeListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function0<kotlin.Unit> onClearFormatListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function0<kotlin.Unit> onCloseListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function2<? super java.lang.Float, ? super java.lang.Float, kotlin.Unit> onTextWindowDragListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function0<kotlin.Unit> onTextWindowDragEndListener;
    
    public TextEditPanel(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    android.view.ViewGroup parentContainer) {
        super();
    }
    
    /**
     * 从全局管理器加载字号列表
     */
    private final void loadFontSizeListFromManager() {
    }
    
    /**
     * 从全局管理器加载字间距列表
     */
    private final void loadLetterSpacingListFromManager() {
    }
    
    /**
     * 从全局管理器加载行间距列表
     */
    private final void loadLineSpacingListFromManager() {
    }
    
    /**
     * 加载字体列表
     */
    private final void loadFontFamilyList() {
    }
    
    /**
     * 加载对齐选项列表
     */
    private final void loadTextAlignmentList() {
    }
    
    /**
     * 初始化面板视图
     */
    private final void initializePanel() {
    }
    
    /**
     * 设置字号下拉框
     */
    private final void setupFontSizeSpinner() {
    }
    
    /**
     * 设置字间距下拉框
     */
    private final void setupLetterSpacingSpinner() {
    }
    
    /**
     * 设置行间距下拉框
     */
    private final void setupLineSpacingSpinner() {
    }
    
    /**
     * 设置字体下拉框
     */
    private final void setupFontFamilySpinner() {
    }
    
    /**
     * 设置对齐下拉框
     */
    private final void setupTextAlignmentSpinner() {
    }
    
    /**
     * 设置拖拽功能
     */
    private final void setupDragFunctionality() {
    }
    
    /**
     * 设置点击监听器
     */
    private final void setupClickListeners() {
    }
    
    /**
     * 更新按钮状态
     */
    private final void updateButtonState(android.widget.LinearLayout buttonLayout, boolean isEnabled) {
    }
    
    /**
     * 显示编辑面板
     */
    public final void show(@kotlin.Suppress(names = {"UNUSED_PARAMETER"})
    @org.jetbrains.annotations.NotNull()
    java.lang.String currentText, boolean bold, boolean italic, int fontSize, @org.jetbrains.annotations.Nullable()
    com.example.castapp.utils.FontPresetManager.FontItem fontFamily, float letterSpacing, float lineSpacing, int textAlignment) {
    }
    
    /**
     * 隐藏编辑面板
     */
    public final void hide() {
    }
    
    /**
     * 设置格式变化监听器（实时应用）
     */
    public final void setOnFormatChangeListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Boolean, ? super java.lang.Boolean, kotlin.Unit> listener) {
    }
    
    /**
     * 设置字号变化监听器
     */
    public final void setOnFontSizeChangeListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> listener) {
    }
    
    /**
     * 设置字体变化监听器
     */
    public final void setOnFontFamilyChangeListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.castapp.utils.FontPresetManager.FontItem, kotlin.Unit> listener) {
    }
    
    /**
     * 设置字间距变化监听器
     */
    public final void setOnLetterSpacingChangeListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Float, kotlin.Unit> listener) {
    }
    
    /**
     * 设置行间距变化监听器
     */
    public final void setOnLineSpacingChangeListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Float, kotlin.Unit> listener) {
    }
    
    /**
     * 设置对齐变化监听器
     */
    public final void setOnTextAlignmentChangeListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> listener) {
    }
    
    /**
     * 设置清除格式监听器
     */
    public final void setOnClearFormatListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> listener) {
    }
    
    /**
     * 设置关闭监听器
     */
    public final void setOnCloseListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> listener) {
    }
    
    /**
     * 根据选中文字的格式状态更新按钮状态（包含字号、颜色、描边、字体、字间距和行间距）
     */
    public final void updateButtonStatesFromSelection(boolean bold, boolean italic, int fontSize, @org.jetbrains.annotations.Nullable()
    java.lang.Integer textColor, @org.jetbrains.annotations.Nullable()
    kotlin.Triple<java.lang.Boolean, java.lang.Float, java.lang.Integer> strokeState, @org.jetbrains.annotations.Nullable()
    com.example.castapp.utils.FontPresetManager.FontItem fontFamily, @org.jetbrains.annotations.Nullable()
    java.lang.Float letterSpacing, @org.jetbrains.annotations.Nullable()
    java.lang.Float lineSpacing, @org.jetbrains.annotations.Nullable()
    java.lang.Integer textAlignment) {
    }
    
    /**
     * 设置文本窗口拖动监听器
     */
    public final void setOnTextWindowDragListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Float, ? super java.lang.Float, kotlin.Unit> listener) {
    }
    
    /**
     * 设置文本窗口拖动结束监听器
     */
    public final void setOnTextWindowDragEndListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> listener) {
    }
    
    /**
     * 检查面板是否显示
     */
    public final boolean isShowing() {
        return false;
    }
    
    /**
     * 显示字号设置对话框
     */
    private final void showFontSizeSettingsDialog() {
    }
    
    /**
     * 显示字间距设置对话框
     */
    private final void showLetterSpacingSettingsDialog() {
    }
    
    /**
     * 处理字间距选择
     */
    private final void handleLetterSpacingSelection(float letterSpacing) {
    }
    
    /**
     * 显示行间距设置对话框
     */
    private final void showLineSpacingSettingsDialog() {
    }
    
    /**
     * 处理行间距选择
     */
    private final void handleLineSpacingSelection(float lineSpacing) {
    }
    
    /**
     * 处理字号选择
     */
    private final void handleFontSizeSelection(int fontSize) {
    }
    
    /**
     * 更新Spinner选择到指定字号
     */
    private final void updateSpinnerSelection(int fontSize) {
    }
    
    /**
     * 更新字间距Spinner选择到指定字间距
     */
    private final void updateLetterSpacingSpinnerSelection(float letterSpacing) {
    }
    
    /**
     * 更新行间距Spinner选择到指定行间距
     */
    private final void updateLineSpacingSpinnerSelection(float lineSpacing) {
    }
    
    /**
     * 更新字体选择到指定字体
     */
    private final void updateFontFamilySelection(com.example.castapp.utils.FontPresetManager.FontItem fontItem) {
    }
    
    /**
     * 更新对齐选择到指定对齐方式
     */
    private final void updateTextAlignmentSelection(int alignment) {
    }
    
    /**
     * 显示字体设置对话框
     */
    private final void showFontSettingsDialog() {
    }
    
    /**
     * 查找最接近的预设字号
     */
    private final int findClosestPresetFontSize(int targetFontSize) {
        return 0;
    }
    
    /**
     * 字号被添加时的回调
     */
    @java.lang.Override()
    public void onFontSizeAdded(int fontSize) {
    }
    
    /**
     * 字号被删除时的回调
     */
    @java.lang.Override()
    public void onFontSizeDeleted(int fontSize) {
    }
    
    /**
     * 字号列表被重置时的回调
     */
    @java.lang.Override()
    public void onFontSizeListReset() {
    }
    
    /**
     * 字间距被添加时的回调
     */
    @java.lang.Override()
    public void onLetterSpacingAdded(float letterSpacing) {
    }
    
    /**
     * 字间距被删除时的回调
     */
    @java.lang.Override()
    public void onLetterSpacingDeleted(float letterSpacing) {
    }
    
    /**
     * 字间距列表被重置时的回调
     */
    @java.lang.Override()
    public void onLetterSpacingListReset() {
    }
    
    /**
     * 行间距被添加时的回调
     */
    @java.lang.Override()
    public void onLineSpacingAdded(float lineSpacing) {
    }
    
    /**
     * 行间距被删除时的回调
     */
    @java.lang.Override()
    public void onLineSpacingDeleted(float lineSpacing) {
    }
    
    /**
     * 行间距列表被重置时的回调
     */
    @java.lang.Override()
    public void onLineSpacingListReset() {
    }
    
    /**
     * 显示颜色选择器对话框
     */
    private final void showColorPickerDialog() {
    }
    
    /**
     * 更新颜色按钮状态，显示当前文字颜色
     */
    private final void updateColorButtonState(int color) {
    }
    
    /**
     * 重置颜色按钮状态为默认颜色
     */
    private final void resetColorButtonState() {
    }
    
    /**
     * 显示描边颜色选择器对话框
     */
    private final void showStrokeColorPickerDialog() {
    }
    
    /**
     * 更新描边按钮状态
     */
    private final void updateStrokeButtonState(boolean enabled, int color) {
    }
    
    /**
     * 重置描边按钮状态为默认状态
     */
    private final void resetStrokeButtonState() {
    }
    
    /**
     * 设置颜色变化监听器
     */
    public final void setOnColorChangeListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> listener) {
    }
    
    /**
     * 设置描边变化监听器
     */
    public final void setOnStrokeChangeListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super java.lang.Boolean, ? super java.lang.Float, ? super java.lang.Integer, kotlin.Unit> listener) {
    }
    
    /**
     * 显示窗口颜色选择器对话框
     */
    private final void showWindowColorPickerDialog() {
    }
    
    /**
     * 更新窗色按钮状态
     */
    private final void updateWindowColorButtonState(boolean enabled, int color) {
    }
    
    /**
     * 重置窗色按钮状态为默认状态
     */
    private final void resetWindowColorButtonState() {
    }
    
    /**
     * 设置窗口颜色变化监听器
     */
    public final void setOnWindowColorChangeListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Boolean, ? super java.lang.Integer, kotlin.Unit> listener) {
    }
    
    /**
     * 初始化窗口颜色状态（用于从TextWindowView恢复状态）
     */
    public final void initializeWindowColorState(boolean enabled, int color) {
    }
    
    /**
     * 销毁时移除监听器
     */
    public final void destroy() {
    }
    
    /**
     * 自定义字体适配器
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010!\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0082\u0004\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B#\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00020\b\u00a2\u0006\u0002\u0010\tJ\"\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\u00062\b\u0010\r\u001a\u0004\u0018\u00010\u000b2\u0006\u0010\u000e\u001a\u00020\u000fH\u0016J\"\u0010\u0010\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\u00062\b\u0010\r\u001a\u0004\u0018\u00010\u000b2\u0006\u0010\u000e\u001a\u00020\u000fH\u0016\u00a8\u0006\u0011"}, d2 = {"Lcom/example/castapp/ui/view/TextEditPanel$FontFamilyAdapter;", "Landroid/widget/ArrayAdapter;", "", "context", "Landroid/content/Context;", "resource", "", "objects", "", "(Lcom/example/castapp/ui/view/TextEditPanel;Landroid/content/Context;ILjava/util/List;)V", "getDropDownView", "Landroid/view/View;", "position", "convertView", "parent", "Landroid/view/ViewGroup;", "getView", "app_debug"})
    final class FontFamilyAdapter extends android.widget.ArrayAdapter<java.lang.String> {
        
        public FontFamilyAdapter(@org.jetbrains.annotations.NotNull()
        android.content.Context context, int resource, @org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.String> objects) {
            super(null, 0);
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public android.view.View getDropDownView(int position, @org.jetbrains.annotations.Nullable()
        android.view.View convertView, @org.jetbrains.annotations.NotNull()
        android.view.ViewGroup parent) {
            return null;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public android.view.View getView(int position, @org.jetbrains.annotations.Nullable()
        android.view.View convertView, @org.jetbrains.annotations.NotNull()
        android.view.ViewGroup parent) {
            return null;
        }
    }
    
    /**
     * 自定义字号适配器（暂时禁用长按功能，确保基本选择正常）
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010!\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\b\u0082\u0004\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B#\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00020\b\u00a2\u0006\u0002\u0010\tJ\"\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\u00062\b\u0010\r\u001a\u0004\u0018\u00010\u000b2\u0006\u0010\u000e\u001a\u00020\u000fH\u0016\u00a8\u0006\u0010"}, d2 = {"Lcom/example/castapp/ui/view/TextEditPanel$FontSizeAdapter;", "Landroid/widget/ArrayAdapter;", "", "context", "Landroid/content/Context;", "resource", "", "objects", "", "(Lcom/example/castapp/ui/view/TextEditPanel;Landroid/content/Context;ILjava/util/List;)V", "getDropDownView", "Landroid/view/View;", "position", "convertView", "parent", "Landroid/view/ViewGroup;", "app_debug"})
    final class FontSizeAdapter extends android.widget.ArrayAdapter<java.lang.String> {
        
        public FontSizeAdapter(@org.jetbrains.annotations.NotNull()
        android.content.Context context, int resource, @org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.String> objects) {
            super(null, 0);
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public android.view.View getDropDownView(int position, @org.jetbrains.annotations.Nullable()
        android.view.View convertView, @org.jetbrains.annotations.NotNull()
        android.view.ViewGroup parent) {
            return null;
        }
    }
    
    /**
     * 自定义字间距适配器
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010!\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\b\u0082\u0004\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B#\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00020\b\u00a2\u0006\u0002\u0010\tJ\"\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\u00062\b\u0010\r\u001a\u0004\u0018\u00010\u000b2\u0006\u0010\u000e\u001a\u00020\u000fH\u0016\u00a8\u0006\u0010"}, d2 = {"Lcom/example/castapp/ui/view/TextEditPanel$LetterSpacingAdapter;", "Landroid/widget/ArrayAdapter;", "", "context", "Landroid/content/Context;", "resource", "", "objects", "", "(Lcom/example/castapp/ui/view/TextEditPanel;Landroid/content/Context;ILjava/util/List;)V", "getDropDownView", "Landroid/view/View;", "position", "convertView", "parent", "Landroid/view/ViewGroup;", "app_debug"})
    final class LetterSpacingAdapter extends android.widget.ArrayAdapter<java.lang.String> {
        
        public LetterSpacingAdapter(@org.jetbrains.annotations.NotNull()
        android.content.Context context, int resource, @org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.String> objects) {
            super(null, 0);
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public android.view.View getDropDownView(int position, @org.jetbrains.annotations.Nullable()
        android.view.View convertView, @org.jetbrains.annotations.NotNull()
        android.view.ViewGroup parent) {
            return null;
        }
    }
    
    /**
     * 自定义行间距适配器
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010!\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\b\u0082\u0004\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B#\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00020\b\u00a2\u0006\u0002\u0010\tJ\"\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\u00062\b\u0010\r\u001a\u0004\u0018\u00010\u000b2\u0006\u0010\u000e\u001a\u00020\u000fH\u0016\u00a8\u0006\u0010"}, d2 = {"Lcom/example/castapp/ui/view/TextEditPanel$LineSpacingAdapter;", "Landroid/widget/ArrayAdapter;", "", "context", "Landroid/content/Context;", "resource", "", "objects", "", "(Lcom/example/castapp/ui/view/TextEditPanel;Landroid/content/Context;ILjava/util/List;)V", "getDropDownView", "Landroid/view/View;", "position", "convertView", "parent", "Landroid/view/ViewGroup;", "app_debug"})
    final class LineSpacingAdapter extends android.widget.ArrayAdapter<java.lang.String> {
        
        public LineSpacingAdapter(@org.jetbrains.annotations.NotNull()
        android.content.Context context, int resource, @org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.String> objects) {
            super(null, 0);
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public android.view.View getDropDownView(int position, @org.jetbrains.annotations.Nullable()
        android.view.View convertView, @org.jetbrains.annotations.NotNull()
        android.view.ViewGroup parent) {
            return null;
        }
    }
    
    /**
     * 自定义对齐适配器
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010!\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0082\u0004\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B1\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00020\b\u0012\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00060\b\u00a2\u0006\u0002\u0010\nJ\"\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u00062\b\u0010\u000e\u001a\u0004\u0018\u00010\f2\u0006\u0010\u000f\u001a\u00020\u0010H\u0016J\"\u0010\u0011\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u00062\b\u0010\u000e\u001a\u0004\u0018\u00010\f2\u0006\u0010\u000f\u001a\u00020\u0010H\u0016R\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00060\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0012"}, d2 = {"Lcom/example/castapp/ui/view/TextEditPanel$TextAlignmentAdapter;", "Landroid/widget/ArrayAdapter;", "", "context", "Landroid/content/Context;", "resource", "", "alignmentOptions", "", "alignmentIcons", "(Lcom/example/castapp/ui/view/TextEditPanel;Landroid/content/Context;ILjava/util/List;Ljava/util/List;)V", "getDropDownView", "Landroid/view/View;", "position", "convertView", "parent", "Landroid/view/ViewGroup;", "getView", "app_debug"})
    final class TextAlignmentAdapter extends android.widget.ArrayAdapter<java.lang.String> {
        @org.jetbrains.annotations.NotNull()
        private final java.util.List<java.lang.String> alignmentOptions = null;
        @org.jetbrains.annotations.NotNull()
        private final java.util.List<java.lang.Integer> alignmentIcons = null;
        
        public TextAlignmentAdapter(@org.jetbrains.annotations.NotNull()
        android.content.Context context, int resource, @org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.String> alignmentOptions, @org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.Integer> alignmentIcons) {
            super(null, 0);
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public android.view.View getView(int position, @org.jetbrains.annotations.Nullable()
        android.view.View convertView, @org.jetbrains.annotations.NotNull()
        android.view.ViewGroup parent) {
            return null;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public android.view.View getDropDownView(int position, @org.jetbrains.annotations.Nullable()
        android.view.View convertView, @org.jetbrains.annotations.NotNull()
        android.view.ViewGroup parent) {
            return null;
        }
    }
}