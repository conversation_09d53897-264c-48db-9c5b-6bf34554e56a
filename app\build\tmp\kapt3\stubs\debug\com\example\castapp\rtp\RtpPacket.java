package com.example.castapp.rtp;

/**
 * RTP数据包实现 - 零拷贝优化版本
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0016\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0010\t\n\u0002\b\f\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\u0018\u0000 72\u00020\u0001:\u00017B\u0005\u00a2\u0006\u0002\u0010\u0002J\u000e\u00101\u001a\u00020\u00062\u0006\u00102\u001a\u000203J\b\u00104\u001a\u0004\u0018\u00010\u001dJ\b\u00105\u001a\u000206H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0005\u001a\u00020\u0006X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0007\u0010\b\"\u0004\b\t\u0010\nR\u001a\u0010\u000b\u001a\u00020\u0006X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\f\u0010\b\"\u0004\b\r\u0010\nR\u001a\u0010\u000e\u001a\u00020\u0006X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000e\u0010\b\"\u0004\b\u000f\u0010\nR\u001a\u0010\u0010\u001a\u00020\u0006X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0011\u0010\b\"\u0004\b\u0012\u0010\nR\u001a\u0010\u0013\u001a\u00020\u0004X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0014\u0010\u0015\"\u0004\b\u0016\u0010\u0017R\u000e\u0010\u0018\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0019\u001a\u00020\u0004X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001a\u0010\u0015\"\u0004\b\u001b\u0010\u0017R\u001a\u0010\u001c\u001a\u00020\u001dX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001e\u0010\u001f\"\u0004\b \u0010!R\u001a\u0010\"\u001a\u00020\u0004X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b#\u0010\u0015\"\u0004\b$\u0010\u0017R\u001a\u0010%\u001a\u00020&X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\'\u0010(\"\u0004\b)\u0010*R\u001a\u0010+\u001a\u00020&X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b,\u0010(\"\u0004\b-\u0010*R\u001a\u0010.\u001a\u00020\u0004X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b/\u0010\u0015\"\u0004\b0\u0010\u0017\u00a8\u00068"}, d2 = {"Lcom/example/castapp/rtp/RtpPacket;", "", "()V", "csrcCount", "", "fuEnd", "", "getFuEnd", "()Z", "setFuEnd", "(Z)V", "fuStart", "getFuStart", "setFuStart", "isFuA", "setFuA", "marker", "getMarker", "setMarker", "originalNalType", "getOriginalNalType", "()I", "setOriginalNalType", "(I)V", "padding", "payloadType", "getPayloadType", "setPayloadType", "payloadView", "Lcom/example/castapp/rtp/PayloadView;", "getPayloadView", "()Lcom/example/castapp/rtp/PayloadView;", "setPayloadView", "(Lcom/example/castapp/rtp/PayloadView;)V", "sequenceNumber", "getSequenceNumber", "setSequenceNumber", "ssrc", "", "getSsrc", "()J", "setSsrc", "(J)V", "timestamp", "getTimestamp", "setTimestamp", "version", "getVersion", "setVersion", "fromDataView", "smartDataView", "Lcom/example/castapp/network/SmartDataView;", "getFuANalDataView", "parseFuAInfo", "", "Companion", "app_debug"})
public final class RtpPacket {
    public static final int RTP_HEADER_SIZE = 12;
    public static final int RTP_VERSION = 2;
    public static final int H264_PAYLOAD_TYPE = 96;
    public static final int FU_A_TYPE = 28;
    public static final int FU_INDICATOR_SIZE = 1;
    public static final int FU_HEADER_SIZE = 1;
    public static final int FU_HEADER_TOTAL_SIZE = 2;
    public static final int FU_START_BIT = 128;
    public static final int FU_END_BIT = 64;
    private int version = 2;
    private boolean padding = false;
    private int csrcCount = 0;
    private boolean marker = false;
    private int payloadType = 96;
    private int sequenceNumber = 0;
    private long timestamp = 0L;
    private long ssrc = 0L;
    @org.jetbrains.annotations.NotNull()
    private com.example.castapp.rtp.PayloadView payloadView;
    private boolean isFuA = false;
    private boolean fuStart = false;
    private boolean fuEnd = false;
    private int originalNalType = 0;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.rtp.RtpPacket.Companion Companion = null;
    
    public RtpPacket() {
        super();
    }
    
    public final int getVersion() {
        return 0;
    }
    
    public final void setVersion(int p0) {
    }
    
    public final boolean getMarker() {
        return false;
    }
    
    public final void setMarker(boolean p0) {
    }
    
    public final int getPayloadType() {
        return 0;
    }
    
    public final void setPayloadType(int p0) {
    }
    
    public final int getSequenceNumber() {
        return 0;
    }
    
    public final void setSequenceNumber(int p0) {
    }
    
    public final long getTimestamp() {
        return 0L;
    }
    
    public final void setTimestamp(long p0) {
    }
    
    public final long getSsrc() {
        return 0L;
    }
    
    public final void setSsrc(long p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.rtp.PayloadView getPayloadView() {
        return null;
    }
    
    public final void setPayloadView(@org.jetbrains.annotations.NotNull()
    com.example.castapp.rtp.PayloadView p0) {
    }
    
    public final boolean isFuA() {
        return false;
    }
    
    public final void setFuA(boolean p0) {
    }
    
    public final boolean getFuStart() {
        return false;
    }
    
    public final void setFuStart(boolean p0) {
    }
    
    public final boolean getFuEnd() {
        return false;
    }
    
    public final void setFuEnd(boolean p0) {
    }
    
    public final int getOriginalNalType() {
        return 0;
    }
    
    public final void setOriginalNalType(int p0) {
    }
    
    /**
     * 从SmartDataView解析RTP包（智能零拷贝）
     */
    public final boolean fromDataView(@org.jetbrains.annotations.NotNull()
    com.example.castapp.network.SmartDataView smartDataView) {
        return false;
    }
    
    /**
     * 解析FU-A信息（使用PayloadView零拷贝）
     */
    private final void parseFuAInfo() {
    }
    
    /**
     * 获取FU-A分片的NAL数据视图（不包含FU-A头部，零拷贝）
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.rtp.PayloadView getFuANalDataView() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\t\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/example/castapp/rtp/RtpPacket$Companion;", "", "()V", "FU_A_TYPE", "", "FU_END_BIT", "FU_HEADER_SIZE", "FU_HEADER_TOTAL_SIZE", "FU_INDICATOR_SIZE", "FU_START_BIT", "H264_PAYLOAD_TYPE", "RTP_HEADER_SIZE", "RTP_VERSION", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}