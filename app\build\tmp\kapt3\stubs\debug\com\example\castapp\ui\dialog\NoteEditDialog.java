package com.example.castapp.ui.dialog;

/**
 * 🏷️ 备注编辑对话框
 * 用于编辑备注信息
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\u0018\u00002\u00020\u0001B=\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\b\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\b\u0010\u0007\u001a\u0004\u0018\u00010\u0005\u0012\u0012\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\n0\t\u00a2\u0006\u0002\u0010\u000bJ\b\u0010\u000e\u001a\u00020\nH\u0002J\u0010\u0010\u000f\u001a\u00020\n2\u0006\u0010\u0010\u001a\u00020\u0005H\u0002J\u0006\u0010\u0011\u001a\u00020\nR\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0007\u001a\u0004\u0018\u00010\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0006\u001a\u0004\u0018\u00010\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\n0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0012"}, d2 = {"Lcom/example/castapp/ui/dialog/NoteEditDialog;", "", "context", "Landroid/content/Context;", "connectionId", "", "deviceName", "currentNote", "onNoteChanged", "Lkotlin/Function1;", "", "(Landroid/content/Context;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/jvm/functions/Function1;)V", "noteManager", "Lcom/example/castapp/utils/NoteManager;", "deleteNote", "saveNote", "note", "show", "app_debug"})
public final class NoteEditDialog {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String connectionId = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String deviceName = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String currentNote = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<java.lang.String, kotlin.Unit> onNoteChanged = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.utils.NoteManager noteManager = null;
    
    public NoteEditDialog(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.Nullable()
    java.lang.String deviceName, @org.jetbrains.annotations.Nullable()
    java.lang.String currentNote, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onNoteChanged) {
        super();
    }
    
    /**
     * 🏷️ 显示备注编辑对话框
     */
    public final void show() {
    }
    
    /**
     * 🏷️ 保存备注
     */
    private final void saveNote(java.lang.String note) {
    }
    
    /**
     * 🏷️ 删除备注
     */
    private final void deleteNote() {
    }
}