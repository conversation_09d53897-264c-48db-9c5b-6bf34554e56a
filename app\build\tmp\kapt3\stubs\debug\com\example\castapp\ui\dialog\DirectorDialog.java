package com.example.castapp.ui.dialog;

/**
 * 导播台BottomSheet对话框
 * 用于管理和恢复已保存的窗口布局
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00ac\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0013\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0004\u0018\u00002\u00020\u0001B\'\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0018\u0010\u0004\u001a\u0014\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u0006\u0012\u0004\u0012\u00020\b0\u0005\u00a2\u0006\u0002\u0010\tJ\u0010\u0010(\u001a\u00020\b2\u0006\u0010)\u001a\u00020\u000bH\u0002J\b\u0010*\u001a\u00020\bH\u0002J\b\u0010+\u001a\u00020\bH\u0002J\b\u0010,\u001a\u00020\bH\u0002J\u0016\u0010-\u001a\u00020\b2\f\u0010.\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0006H\u0002J\b\u0010/\u001a\u00020\bH\u0002J\b\u00100\u001a\u00020\bH\u0002J\b\u00101\u001a\u00020\bH\u0002J\u0010\u00102\u001a\u00020\b2\u0006\u00103\u001a\u000204H\u0002J\b\u00105\u001a\u00020\bH\u0002J\b\u00106\u001a\u00020\bH\u0002J\u0010\u00107\u001a\u00020\b2\u0006\u00108\u001a\u000209H\u0002J\b\u0010:\u001a\u00020\bH\u0002J&\u0010;\u001a\u0004\u0018\u0001042\u0006\u0010<\u001a\u00020=2\b\u0010>\u001a\u0004\u0018\u00010?2\b\u0010@\u001a\u0004\u0018\u00010AH\u0016J\u0010\u0010B\u001a\u00020\b2\u0006\u0010C\u001a\u00020DH\u0016J\u001a\u0010E\u001a\u00020\b2\u0006\u00103\u001a\u0002042\b\u0010@\u001a\u0004\u0018\u00010AH\u0016J\u0016\u0010F\u001a\u00020\b2\f\u0010.\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0006H\u0002J\u001e\u0010G\u001a\u00020\b2\u0006\u0010)\u001a\u00020\u000b2\f\u0010H\u001a\b\u0012\u0004\u0012\u00020I0\u0006H\u0002J\b\u0010J\u001a\u00020\bH\u0002J\b\u0010K\u001a\u00020\bH\u0002J\b\u0010L\u001a\u00020\bH\u0002J\u0016\u0010M\u001a\u00020\b2\f\u0010.\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0006H\u0002J\u0010\u0010N\u001a\u00020\b2\u0006\u0010O\u001a\u00020PH\u0002J\u0010\u0010Q\u001a\u00020\b2\u0006\u0010R\u001a\u00020SH\u0002J\u0010\u0010T\u001a\u00020\b2\u0006\u0010U\u001a\u00020PH\u0002J\u001e\u0010V\u001a\u00020\b2\u0006\u0010)\u001a\u00020\u000b2\f\u0010H\u001a\b\u0012\u0004\u0012\u00020I0\u0006H\u0002R\u0010\u0010\n\u001a\u0004\u0018\u00010\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0015X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0017X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0019X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u001bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\u001eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\"\u0010\u001f\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010 X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b!\u0010\"\"\u0004\b#\u0010$R \u0010\u0004\u001a\u0014\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u0006\u0012\u0004\u0012\u00020\b0\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010%\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010&\u001a\u0004\u0018\u00010\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\'\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006W"}, d2 = {"Lcom/example/castapp/ui/dialog/DirectorDialog;", "Lcom/google/android/material/bottomsheet/BottomSheetDialogFragment;", "windowSettingsManager", "Lcom/example/castapp/manager/WindowSettingsManager;", "onRestoreLayout", "Lkotlin/Function1;", "", "Lcom/example/castapp/database/entity/WindowLayoutItemEntity;", "", "(Lcom/example/castapp/manager/WindowSettingsManager;Lkotlin/jvm/functions/Function1;)V", "appliedLayout", "Lcom/example/castapp/database/entity/WindowLayoutEntity;", "applyButton", "Landroid/widget/Button;", "batchDeleteButton", "closeButton", "Landroid/widget/ImageButton;", "detailListRecyclerView", "Landroidx/recyclerview/widget/RecyclerView;", "editButton", "emptyView", "Landroid/widget/TextView;", "itemTouchHelper", "Landroidx/recyclerview/widget/ItemTouchHelper;", "layoutDetailAdapter", "Lcom/example/castapp/ui/adapter/LayoutDetailAdapter;", "layoutListAdapter", "Lcom/example/castapp/ui/adapter/LayoutListAdapter;", "layoutListRecyclerView", "layoutManager", "Lcom/example/castapp/manager/LayoutManager;", "onDialogDismissed", "Lkotlin/Function0;", "getOnDialogDismissed", "()Lkotlin/jvm/functions/Function0;", "setOnDialogDismissed", "(Lkotlin/jvm/functions/Function0;)V", "saveButton", "selectedLayout", "selectionButton", "handleApplyLayout", "layout", "handleBatchDelete", "handleCancelApply", "handleEditLayout", "handleLayoutOrderChanged", "layouts", "handleRestoreLayout", "handleSaveLayout", "handleSelectionModeToggle", "initViews", "view", "Landroid/view/View;", "initializeButtonStates", "loadCurrentAppliedStatus", "loadLayoutDetails", "layoutId", "", "observeLayoutData", "onCreateView", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "savedInstanceState", "Landroid/os/Bundle;", "onDismiss", "dialog", "Landroid/content/DialogInterface;", "onViewCreated", "performBatchDelete", "replaceLayoutParameters", "currentWindowInfoList", "Lcom/example/castapp/model/CastWindowInfo;", "setupClickListeners", "setupDragAndDrop", "setupRecyclerViews", "showBatchDeleteConfirmDialog", "showEmptyDetailView", "show", "", "updateBatchDeleteButtonState", "selectedCount", "", "updateButtonStates", "hasSelection", "updateLayoutParameters", "app_debug"})
public final class DirectorDialog extends com.google.android.material.bottomsheet.BottomSheetDialogFragment {
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.WindowSettingsManager windowSettingsManager = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<java.util.List<com.example.castapp.database.entity.WindowLayoutItemEntity>, kotlin.Unit> onRestoreLayout = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.LayoutManager layoutManager = null;
    private androidx.recyclerview.widget.RecyclerView layoutListRecyclerView;
    private androidx.recyclerview.widget.RecyclerView detailListRecyclerView;
    private android.widget.TextView emptyView;
    private android.widget.Button selectionButton;
    private android.widget.Button batchDeleteButton;
    private android.widget.Button saveButton;
    private android.widget.Button editButton;
    private android.widget.Button applyButton;
    private android.widget.ImageButton closeButton;
    private com.example.castapp.ui.adapter.LayoutListAdapter layoutListAdapter;
    private com.example.castapp.ui.adapter.LayoutDetailAdapter layoutDetailAdapter;
    private androidx.recyclerview.widget.ItemTouchHelper itemTouchHelper;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.database.entity.WindowLayoutEntity selectedLayout;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.database.entity.WindowLayoutEntity appliedLayout;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function0<kotlin.Unit> onDialogDismissed;
    
    public DirectorDialog(@org.jetbrains.annotations.NotNull()
    com.example.castapp.manager.WindowSettingsManager windowSettingsManager, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.util.List<com.example.castapp.database.entity.WindowLayoutItemEntity>, kotlin.Unit> onRestoreLayout) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlin.jvm.functions.Function0<kotlin.Unit> getOnDialogDismissed() {
        return null;
    }
    
    public final void setOnDialogDismissed(@org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> p0) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    /**
     * 初始化视图组件
     */
    private final void initViews(android.view.View view) {
    }
    
    /**
     * 🐾 初始化按钮状态
     */
    private final void initializeButtonStates() {
    }
    
    /**
     * 设置RecyclerView
     */
    private final void setupRecyclerViews() {
    }
    
    /**
     * 设置拖拽排序功能
     */
    private final void setupDragAndDrop() {
    }
    
    /**
     * 处理布局顺序改变
     */
    private final void handleLayoutOrderChanged(java.util.List<com.example.castapp.database.entity.WindowLayoutEntity> layouts) {
    }
    
    /**
     * 设置点击监听器
     */
    private final void setupClickListeners() {
    }
    
    /**
     * 🐾 加载当前应用状态（新增）
     */
    private final void loadCurrentAppliedStatus() {
    }
    
    /**
     * 观察布局数据变化
     */
    private final void observeLayoutData() {
    }
    
    /**
     * 加载布局详情
     */
    private final void loadLayoutDetails(long layoutId) {
    }
    
    /**
     * 🐾 处理应用布局或取消应用（重构版）
     */
    private final void handleRestoreLayout() {
    }
    
    /**
     * 🐾 处理应用布局（新增）
     */
    private final void handleApplyLayout(com.example.castapp.database.entity.WindowLayoutEntity layout) {
    }
    
    /**
     * 🐾 处理取消应用（新增）
     */
    private final void handleCancelApply() {
    }
    
    /**
     * 处理保存布局参数
     */
    private final void handleSaveLayout() {
    }
    
    /**
     * 更新已有布局参数
     */
    private final void updateLayoutParameters(com.example.castapp.database.entity.WindowLayoutEntity layout, java.util.List<com.example.castapp.model.CastWindowInfo> currentWindowInfoList) {
    }
    
    /**
     * 清空已有布局参数并重新保存
     */
    private final void replaceLayoutParameters(com.example.castapp.database.entity.WindowLayoutEntity layout, java.util.List<com.example.castapp.model.CastWindowInfo> currentWindowInfoList) {
    }
    
    /**
     * 处理编辑布局
     */
    private final void handleEditLayout() {
    }
    
    /**
     * 🐾 更新按钮状态（重构版，支持动态文字和样式）
     */
    private final void updateButtonStates(boolean hasSelection) {
    }
    
    /**
     * 显示/隐藏空详情视图
     */
    private final void showEmptyDetailView(boolean show) {
    }
    
    /**
     * 🐾 处理选择模式切换
     */
    private final void handleSelectionModeToggle() {
    }
    
    /**
     * 🐾 更新批量删除按钮状态
     */
    private final void updateBatchDeleteButtonState(int selectedCount) {
    }
    
    /**
     * 🐾 处理批量删除
     */
    private final void handleBatchDelete() {
    }
    
    /**
     * 🐾 显示批量删除确认对话框
     */
    private final void showBatchDeleteConfirmDialog(java.util.List<com.example.castapp.database.entity.WindowLayoutEntity> layouts) {
    }
    
    /**
     * 🐾 执行批量删除操作
     */
    private final void performBatchDelete(java.util.List<com.example.castapp.database.entity.WindowLayoutEntity> layouts) {
    }
    
    @java.lang.Override()
    public void onDismiss(@org.jetbrains.annotations.NotNull()
    android.content.DialogInterface dialog) {
    }
}