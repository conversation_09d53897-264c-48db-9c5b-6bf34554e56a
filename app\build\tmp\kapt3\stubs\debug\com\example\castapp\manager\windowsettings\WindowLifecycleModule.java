package com.example.castapp.manager.windowsettings;

/**
 * 窗口生命周期模块
 * 负责管理投屏窗口的生命周期和事件处理
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010#\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\u0007\u0018\u00002\u00020\u0001B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0006\u0010\u000e\u001a\u00020\tJ\u000e\u0010\u000f\u001a\u00020\t2\u0006\u0010\u0010\u001a\u00020\rJ\u001e\u0010\u0011\u001a\u00020\t2\u0006\u0010\u0010\u001a\u00020\r2\u0006\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u0013J\u0006\u0010\u0015\u001a\u00020\tJ\u000e\u0010\u0016\u001a\u00020\t2\u0006\u0010\u0010\u001a\u00020\rJ\u0014\u0010\u0017\u001a\u00020\t2\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\t0\bJ\u0014\u0010\u0019\u001a\u00020\t2\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\t0\bR\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\t\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010\n\u001a\n\u0012\u0004\u0012\u00020\t\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001a"}, d2 = {"Lcom/example/castapp/manager/windowsettings/WindowLifecycleModule;", "", "dataModule", "Lcom/example/castapp/manager/windowsettings/WindowDataModule;", "creationModule", "Lcom/example/castapp/manager/windowsettings/WindowCreationModule;", "(Lcom/example/castapp/manager/windowsettings/WindowDataModule;Lcom/example/castapp/manager/windowsettings/WindowCreationModule;)V", "dialogRefreshCallback", "Lkotlin/Function0;", "", "precisionControlUpdateCallback", "removingWindows", "", "", "cleanup", "handleNewConnection", "connectionId", "handleScreenResolution", "width", "", "height", "removeAllWindows", "removeWindowForConnection", "setDialogRefreshCallback", "callback", "setPrecisionControlUpdateCallback", "app_debug"})
public final class WindowLifecycleModule {
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.windowsettings.WindowDataModule dataModule = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.windowsettings.WindowCreationModule creationModule = null;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function0<kotlin.Unit> precisionControlUpdateCallback;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function0<kotlin.Unit> dialogRefreshCallback;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Set<java.lang.String> removingWindows = null;
    
    public WindowLifecycleModule(@org.jetbrains.annotations.NotNull()
    com.example.castapp.manager.windowsettings.WindowDataModule dataModule, @org.jetbrains.annotations.NotNull()
    com.example.castapp.manager.windowsettings.WindowCreationModule creationModule) {
        super();
    }
    
    /**
     * 设置精准控制面板更新回调
     */
    public final void setPrecisionControlUpdateCallback(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> callback) {
    }
    
    /**
     * 设置对话框刷新回调
     */
    public final void setDialogRefreshCallback(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> callback) {
    }
    
    /**
     * 处理新连接事件
     */
    public final void handleNewConnection(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 处理屏幕分辨率事件
     */
    public final void handleScreenResolution(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, int width, int height) {
    }
    
    /**
     * 移除指定连接的投屏窗口 - 优化版本
     * 添加状态检查，避免重复移除
     */
    public final void removeWindowForConnection(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 移除所有投屏窗口
     */
    public final void removeAllWindows() {
    }
    
    /**
     * 清理生命周期模块
     */
    public final void cleanup() {
    }
}