package com.example.castapp.manager;

/**
 * 消息接收管理器
 * 负责处理来自发送端的各种控制消息
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0010\u0018\u00002\u00020\u0001:\u0001\u001dB%\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u00a2\u0006\u0002\u0010\nJ\u0010\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0002J\u0010\u0010\u000f\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0002J\u0010\u0010\u0010\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0002J\u0010\u0010\u0011\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0002J\u0010\u0010\u0012\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0002J\u0010\u0010\u0013\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0002J\u0010\u0010\u0014\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0002J\u0010\u0010\u0015\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0002J\u000e\u0010\u0016\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eJ\u0010\u0010\u0017\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0002J\u0010\u0010\u0018\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0002J\u0010\u0010\u0019\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0002J\u0010\u0010\u001a\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0002J\u0010\u0010\u001b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0002J\u0010\u0010\u001c\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0002R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001e"}, d2 = {"Lcom/example/castapp/manager/MessageReceivingManager;", "", "audioReceivingManager", "Lcom/example/castapp/manager/AudioReceivingManager;", "videoReceivingManager", "Lcom/example/castapp/manager/VideoReceivingManager;", "stateManager", "Lcom/example/castapp/manager/StateManager;", "callback", "Lcom/example/castapp/manager/MessageReceivingManager$MessageReceivingCallback;", "(Lcom/example/castapp/manager/AudioReceivingManager;Lcom/example/castapp/manager/VideoReceivingManager;Lcom/example/castapp/manager/StateManager;Lcom/example/castapp/manager/MessageReceivingManager$MessageReceivingCallback;)V", "handleAacConfig", "", "controlMessage", "Lcom/example/castapp/websocket/ControlMessage;", "handleBitrateControl", "handleCastingState", "handleConnectionRequest", "handleDisconnect", "handleFunctionControl", "handleH264Config", "handleMediaAudioControl", "handleMessage", "handleMicAudioControl", "handleRemoteControlRequest", "handleResolutionChange", "handleScreenResolution", "handleSsrcMapping", "handleVideoStreamStop", "MessageReceivingCallback", "app_debug"})
public final class MessageReceivingManager {
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.AudioReceivingManager audioReceivingManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.VideoReceivingManager videoReceivingManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.StateManager stateManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.MessageReceivingManager.MessageReceivingCallback callback = null;
    
    public MessageReceivingManager(@org.jetbrains.annotations.NotNull()
    com.example.castapp.manager.AudioReceivingManager audioReceivingManager, @org.jetbrains.annotations.NotNull()
    com.example.castapp.manager.VideoReceivingManager videoReceivingManager, @org.jetbrains.annotations.NotNull()
    com.example.castapp.manager.StateManager stateManager, @org.jetbrains.annotations.NotNull()
    com.example.castapp.manager.MessageReceivingManager.MessageReceivingCallback callback) {
        super();
    }
    
    /**
     * 处理WebSocket控制消息
     */
    public final void handleMessage(@org.jetbrains.annotations.NotNull()
    com.example.castapp.websocket.ControlMessage controlMessage) {
    }
    
    /**
     * 处理连接请求
     */
    private final void handleConnectionRequest(com.example.castapp.websocket.ControlMessage controlMessage) {
    }
    
    /**
     * 处理屏幕分辨率信息
     */
    private final void handleScreenResolution(com.example.castapp.websocket.ControlMessage controlMessage) {
    }
    
    /**
     * 处理H.264配置数据
     */
    private final void handleH264Config(com.example.castapp.websocket.ControlMessage controlMessage) {
    }
    
    /**
     * 处理AAC配置数据
     */
    private final void handleAacConfig(com.example.castapp.websocket.ControlMessage controlMessage) {
    }
    
    /**
     * 处理SSRC映射
     */
    private final void handleSsrcMapping(com.example.castapp.websocket.ControlMessage controlMessage) {
    }
    
    /**
     * 处理投屏状态同步消息
     */
    private final void handleCastingState(com.example.castapp.websocket.ControlMessage controlMessage) {
    }
    
    /**
     * 处理码率控制消息
     */
    private final void handleBitrateControl(com.example.castapp.websocket.ControlMessage controlMessage) {
    }
    
    /**
     * 处理媒体音频控制消息
     */
    private final void handleMediaAudioControl(com.example.castapp.websocket.ControlMessage controlMessage) {
    }
    
    /**
     * 处理麦克风音频控制消息
     */
    private final void handleMicAudioControl(com.example.castapp.websocket.ControlMessage controlMessage) {
    }
    
    /**
     * 处理视频流停止消息
     */
    private final void handleVideoStreamStop(com.example.castapp.websocket.ControlMessage controlMessage) {
    }
    
    /**
     * 处理断开连接消息
     */
    private final void handleDisconnect(com.example.castapp.websocket.ControlMessage controlMessage) {
    }
    
    /**
     * 处理功能控制消息
     */
    private final void handleFunctionControl(com.example.castapp.websocket.ControlMessage controlMessage) {
    }
    
    /**
     * 处理分辨率变化消息
     */
    private final void handleResolutionChange(com.example.castapp.websocket.ControlMessage controlMessage) {
    }
    
    /**
     * 处理远程控制请求消息
     */
    private final void handleRemoteControlRequest(com.example.castapp.websocket.ControlMessage controlMessage) {
    }
    
    /**
     * 消息接收回调接口
     * 用于与ReceivingService进行交互
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0004\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\n\u0010\u0006\u001a\u0004\u0018\u00010\u0007H&J\u0010\u0010\b\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\u0010\u0010\t\u001a\u00020\n2\u0006\u0010\u0004\u001a\u00020\u0005H&J\u0010\u0010\u000b\u001a\u00020\n2\u0006\u0010\u0004\u001a\u00020\u0005H&J\u0010\u0010\f\u001a\u00020\n2\u0006\u0010\u0004\u001a\u00020\u0005H&J \u0010\r\u001a\u00020\n2\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u000fH&J\u0010\u0010\u0011\u001a\u00020\n2\u0006\u0010\u0004\u001a\u00020\u0005H&J\u0010\u0010\u0012\u001a\u00020\n2\u0006\u0010\u0004\u001a\u00020\u0005H&\u00a8\u0006\u0013"}, d2 = {"Lcom/example/castapp/manager/MessageReceivingManager$MessageReceivingCallback;", "", "addProcessedDisconnection", "", "connectionId", "", "getWebSocketServer", "Lcom/example/castapp/websocket/WebSocketServer;", "isRemoteControlConnection", "markAsRemoteControlConnection", "", "onConnectionDisconnected", "onNewConnection", "onScreenResolution", "width", "", "height", "onVideoWindowRemoved", "sendScreenResolutionToRemoteController", "app_debug"})
    public static abstract interface MessageReceivingCallback {
        
        public abstract void onNewConnection(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId);
        
        public abstract void onConnectionDisconnected(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId);
        
        public abstract void onVideoWindowRemoved(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId);
        
        public abstract void onScreenResolution(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, int width, int height);
        
        public abstract void markAsRemoteControlConnection(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId);
        
        public abstract boolean isRemoteControlConnection(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId);
        
        public abstract void sendScreenResolutionToRemoteController(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId);
        
        public abstract boolean addProcessedDisconnection(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId);
        
        @org.jetbrains.annotations.Nullable()
        public abstract com.example.castapp.websocket.WebSocketServer getWebSocketServer();
    }
}