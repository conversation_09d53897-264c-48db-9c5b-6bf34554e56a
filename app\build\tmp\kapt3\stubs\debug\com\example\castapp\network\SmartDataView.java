package com.example.castapp.network;

/**
 * 智能DataView - 支持引用计数的零拷贝数据视图
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\b\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0012\n\u0002\b\u0002\n\u0002\u0010\u0005\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0003\u0018\u00002\u00020\u0001B\u001d\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0007J \u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00052\u0006\u0010\u0012\u001a\u00020\u0005H\u0016J \u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u00052\u0006\u0010\u0012\u001a\u00020\u0005H\u0016J(\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u00052\u0006\u0010\u0011\u001a\u00020\u00052\u0006\u0010\u0012\u001a\u00020\u0005H\u0016J\u0010\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u0005H\u0016J\b\u0010\u0018\u001a\u0004\u0018\u00010\u0013J\b\u0010\u0019\u001a\u0004\u0018\u00010\u0010J\u0006\u0010\u001a\u001a\u00020\u001bJ\u0006\u0010\u001c\u001a\u00020\u000eJ\b\u0010\u001d\u001a\u00020\u0013H\u0016R\u0014\u0010\u0004\u001a\u00020\u0005X\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0014\u0010\u0006\u001a\u00020\u0005X\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\tR\u0014\u0010\u0002\u001a\u00020\u0003X\u0080\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\f\u00a8\u0006\u001e"}, d2 = {"Lcom/example/castapp/network/SmartDataView;", "Lcom/example/castapp/network/DataView;", "smartBuffer", "Lcom/example/castapp/network/SmartBufferManager$SmartBuffer;", "offset", "", "size", "(Lcom/example/castapp/network/SmartBufferManager$SmartBuffer;II)V", "getOffset", "()I", "getSize", "getSmartBuffer$app_debug", "()Lcom/example/castapp/network/SmartBufferManager$SmartBuffer;", "copyTo", "", "dest", "Ljava/nio/ByteBuffer;", "srcOffset", "length", "", "destOffset", "getByte", "", "index", "getDirectByteArray", "getDirectByteBuffer", "isValid", "", "release", "toByteArray", "app_debug"})
public final class SmartDataView implements com.example.castapp.network.DataView {
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.network.SmartBufferManager.SmartBuffer smartBuffer = null;
    private final int offset = 0;
    private final int size = 0;
    
    public SmartDataView(@org.jetbrains.annotations.NotNull()
    com.example.castapp.network.SmartBufferManager.SmartBuffer smartBuffer, int offset, int size) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.network.SmartBufferManager.SmartBuffer getSmartBuffer$app_debug() {
        return null;
    }
    
    @java.lang.Override()
    public int getOffset() {
        return 0;
    }
    
    @java.lang.Override()
    public int getSize() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public byte[] toByteArray() {
        return null;
    }
    
    @java.lang.Override()
    public void copyTo(@org.jetbrains.annotations.NotNull()
    byte[] dest, int destOffset, int length) {
    }
    
    @java.lang.Override()
    public byte getByte(int index) {
        return 0;
    }
    
    @java.lang.Override()
    public void copyTo(@org.jetbrains.annotations.NotNull()
    byte[] dest, int destOffset, int srcOffset, int length) {
    }
    
    @java.lang.Override()
    public void copyTo(@org.jetbrains.annotations.NotNull()
    java.nio.ByteBuffer dest, int srcOffset, int length) {
    }
    
    /**
     * 释放对底层缓冲区的引用
     */
    public final void release() {
    }
    
    /**
     * 检查底层缓冲区是否仍然有效
     */
    public final boolean isValid() {
        return false;
    }
    
    /**
     * 🚀 零拷贝优化：获取直接ByteBuffer视图（如果可能）
     * 用于高效的批量数据传输
     */
    @org.jetbrains.annotations.Nullable()
    public final java.nio.ByteBuffer getDirectByteBuffer() {
        return null;
    }
    
    /**
     * 🚀 零拷贝优化：获取直接数组访问（用于回退方案）
     * 返回底层数组的引用，调用者需要使用offset和size
     */
    @org.jetbrains.annotations.Nullable()
    public final byte[] getDirectByteArray() {
        return null;
    }
}