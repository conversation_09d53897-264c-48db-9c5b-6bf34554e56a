package com.example.castapp.ui.windowsettings;

/**
 * 变换状态管理器
 * 负责所有变换状态的管理和计算
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0006\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0010\u000e\n\u0002\b\u001f\u0018\u0000 S2\u00020\u0001:\u0001SB\u001d\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0007J\u0006\u0010)\u001a\u00020\nJ\u0006\u0010*\u001a\u00020\u0010J\u0006\u0010+\u001a\u00020\u0010J\u0006\u0010,\u001a\u00020\u0010J\u0006\u0010-\u001a\u00020\u0010J\u001c\u0010.\u001a\u0018\u0012\u0012\u0012\u0010\u0012\u0004\u0012\u00020\r\u0012\u0006\u0012\u0004\u0018\u00010\u000e0\f\u0018\u00010\tJ\u0006\u0010/\u001a\u00020\u0010J\u0006\u00100\u001a\u00020\u0010J\u0006\u00101\u001a\u00020\u0010J\u0006\u00102\u001a\u00020\u0010J\u0018\u00103\u001a\u00020\n2\u0006\u00104\u001a\u0002052\b\b\u0002\u00106\u001a\u00020\rJ\u000e\u00107\u001a\u00020\n2\u0006\u00104\u001a\u000205J\u0006\u00108\u001a\u00020\nJ\u0006\u00109\u001a\u00020\nJ\u0014\u0010:\u001a\u0010\u0012\u0004\u0012\u00020\r\u0012\u0006\u0012\u0004\u0018\u00010\u000e0\fJ\u0018\u0010;\u001a\u00020\n2\u0006\u0010<\u001a\u00020\r2\b\u0010=\u001a\u0004\u0018\u00010\u000eJ\u0016\u0010>\u001a\u00020\n2\u000e\u0010?\u001a\n\u0012\u0004\u0012\u00020\n\u0018\u00010\tJ$\u0010@\u001a\u00020\n2\u001c\u0010A\u001a\u0018\u0012\u0012\u0012\u0010\u0012\u0004\u0012\u00020\r\u0012\u0006\u0012\u0004\u0018\u00010\u000e0\f\u0018\u00010\tJ\u0016\u0010B\u001a\u00020\n2\u0006\u0010C\u001a\u00020\u00102\u0006\u0010D\u001a\u00020\u0010J&\u0010E\u001a\u00020\n2\u0006\u0010F\u001a\u00020\u00102\u0006\u0010G\u001a\u00020\u00102\u0006\u0010H\u001a\u00020\u00102\u0006\u0010I\u001a\u00020\u0010J\u0010\u0010J\u001a\u00020\n2\b\u0010K\u001a\u0004\u0018\u00010(J\u000e\u0010L\u001a\u00020\n2\u0006\u0010M\u001a\u00020\u0010J\u000e\u0010N\u001a\u00020\r2\u0006\u0010O\u001a\u00020\u0010J\u0016\u0010P\u001a\u00020\n2\u0006\u0010Q\u001a\u00020\u00102\u0006\u0010R\u001a\u00020\u0010R\u0016\u0010\b\u001a\n\u0012\u0004\u0012\u00020\n\u0018\u00010\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R$\u0010\u000b\u001a\u0018\u0012\u0012\u0012\u0010\u0012\u0004\u0012\u00020\r\u0012\u0006\u0012\u0004\u0018\u00010\u000e0\f\u0018\u00010\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0017X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0017X\u0082D\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u0019\u001a\u0004\u0018\u00010\u001aX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001b\u0010\u001c\"\u0004\b\u001d\u0010\u001eR\u0010\u0010\u001f\u001a\u0004\u0018\u00010\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010 \u001a\u00020\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010!\u001a\u00020\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\"\u001a\u00020\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010#\u001a\u00020\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010$\u001a\u00020\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010%\u001a\u00020\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010&\u001a\u00020\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\'\u001a\u0004\u0018\u00010(X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006T"}, d2 = {"Lcom/example/castapp/ui/windowsettings/TransformManager;", "", "container", "Landroid/widget/FrameLayout;", "windowWidth", "", "windowHeight", "(Landroid/widget/FrameLayout;II)V", "borderPositionUpdateCallback", "Lkotlin/Function0;", "", "cropStateProvider", "Lkotlin/Pair;", "", "Landroid/graphics/RectF;", "currentPivotX", "", "currentPivotY", "currentRotation", "currentScaleFactor", "currentTranslationX", "currentTranslationY", "lastNotifyTime", "", "notifyIntervalMs", "positionManager", "Lcom/example/castapp/ui/windowsettings/WindowPositionManager;", "getPositionManager", "()Lcom/example/castapp/ui/windowsettings/WindowPositionManager;", "setPositionManager", "(Lcom/example/castapp/ui/windowsettings/WindowPositionManager;)V", "savedCropRectRatio", "savedIsCroppedWindow", "savedPivotX", "savedPivotY", "savedRotation", "savedScaleFactor", "savedTranslationX", "savedTranslationY", "transformStateListener", "Lcom/example/castapp/ui/windowsettings/interfaces/TransformStateListener;", "cleanup", "getActualDisplayX", "getActualDisplayY", "getContainerDisplayX", "getContainerDisplayY", "getCropStateProvider", "getCurrentPivotX", "getCurrentPivotY", "getCurrentRotation", "getCurrentScaleFactor", "notifyTransformChange", "connectionId", "", "forceNotify", "notifyTransformChangeImmediate", "resetPivotToTopLeft", "resetTransform", "restoreSavedTransformState", "saveCurrentTransformState", "isCroppedWindow", "cropRectRatio", "setBorderPositionUpdateCallback", "callback", "setCropStateProvider", "provider", "setPivot", "pivotX", "pivotY", "setPrecisionTransform", "x", "y", "scale", "rotation", "setTransformStateListener", "listener", "updateRotation", "rotationDelta", "updateScale", "scaleFactor", "updateTranslation", "deltaX", "deltaY", "Companion", "app_debug"})
public final class TransformManager {
    @org.jetbrains.annotations.NotNull()
    private final android.widget.FrameLayout container = null;
    private final int windowWidth = 0;
    private final int windowHeight = 0;
    public static final float MIN_SCALE_FACTOR = 0.2F;
    public static final float MAX_SCALE_FACTOR = 8.0F;
    private float currentTranslationX = 0.0F;
    private float currentTranslationY = 0.0F;
    private float currentScaleFactor = 1.0F;
    private float currentRotation = 0.0F;
    private float currentPivotX = 0.0F;
    private float currentPivotY = 0.0F;
    private float savedTranslationX = 0.0F;
    private float savedTranslationY = 0.0F;
    private float savedScaleFactor = 1.0F;
    private float savedRotation = 0.0F;
    private float savedPivotX = 0.0F;
    private float savedPivotY = 0.0F;
    private boolean savedIsCroppedWindow = false;
    @org.jetbrains.annotations.Nullable()
    private android.graphics.RectF savedCropRectRatio;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.windowsettings.interfaces.TransformStateListener transformStateListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function0<kotlin.Unit> borderPositionUpdateCallback;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.windowsettings.WindowPositionManager positionManager;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function0<? extends kotlin.Pair<java.lang.Boolean, ? extends android.graphics.RectF>> cropStateProvider;
    private long lastNotifyTime = 0L;
    private final long notifyIntervalMs = 100L;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.ui.windowsettings.TransformManager.Companion Companion = null;
    
    public TransformManager(@org.jetbrains.annotations.NotNull()
    android.widget.FrameLayout container, int windowWidth, int windowHeight) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.ui.windowsettings.WindowPositionManager getPositionManager() {
        return null;
    }
    
    public final void setPositionManager(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.windowsettings.WindowPositionManager p0) {
    }
    
    /**
     * 设置变换状态监听器
     */
    public final void setTransformStateListener(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.windowsettings.interfaces.TransformStateListener listener) {
    }
    
    /**
     * 🎯 统一边框法：设置边框位置更新回调
     */
    public final void setBorderPositionUpdateCallback(@org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> callback) {
    }
    
    /**
     * 🎯 新增：设置裁剪状态提供者
     */
    public final void setCropStateProvider(@org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<? extends kotlin.Pair<java.lang.Boolean, ? extends android.graphics.RectF>> provider) {
    }
    
    /**
     * 🎯 获取裁剪状态提供者（供TransformHandler使用）
     */
    @org.jetbrains.annotations.Nullable()
    public final kotlin.jvm.functions.Function0<kotlin.Pair<java.lang.Boolean, android.graphics.RectF>> getCropStateProvider() {
        return null;
    }
    
    /**
     * 获取当前缩放倍数
     */
    public final float getCurrentScaleFactor() {
        return 0.0F;
    }
    
    /**
     * 获取当前旋转角度
     */
    public final float getCurrentRotation() {
        return 0.0F;
    }
    
    /**
     * 获取当前变换中心点X
     */
    public final float getCurrentPivotX() {
        return 0.0F;
    }
    
    /**
     * 获取当前变换中心点Y
     */
    public final float getCurrentPivotY() {
        return 0.0F;
    }
    
    /**
     * 更新位移（集中化版本：通过WindowPositionManager处理）
     */
    public final void updateTranslation(float deltaX, float deltaY) {
    }
    
    /**
     * 更新缩放（带范围限制）
     */
    public final boolean updateScale(float scaleFactor) {
        return false;
    }
    
    /**
     * 更新旋转角度
     */
    public final void updateRotation(float rotationDelta) {
    }
    
    /**
     * 设置变换中心点
     */
    public final void setPivot(float pivotX, float pivotY) {
    }
    
    /**
     * 🎯 旋转坐标修复：重置变换中心点为容器左上角
     */
    public final void resetPivotToTopLeft() {
    }
    
    /**
     * 获取实际显示的左上角X坐标（裁剪可见区域适配版）
     */
    public final float getActualDisplayX() {
        return 0.0F;
    }
    
    /**
     * 获取实际显示的左上角Y坐标（裁剪可见区域适配版）
     */
    public final float getActualDisplayY() {
        return 0.0F;
    }
    
    /**
     * 获取容器的左上角X坐标（用于遥控端可视化）
     * 🎯 新增：专门为遥控端提供容器位置，不考虑裁剪偏移
     */
    public final float getContainerDisplayX() {
        return 0.0F;
    }
    
    /**
     * 获取容器的左上角Y坐标（用于遥控端可视化）
     * 🎯 新增：专门为遥控端提供容器位置，不考虑裁剪偏移
     */
    public final float getContainerDisplayY() {
        return 0.0F;
    }
    
    /**
     * 设置精准变换（坐标系修复版：正确处理裁剪窗口的坐标转换）
     * @param x 期望的实际显示X坐标（左上角，以屏幕左上角为原点）
     * @param y 期望的实际显示Y坐标（左上角，以屏幕左上角为原点）
     * @param scale 缩放倍数
     * @param rotation 旋转角度
     */
    public final void setPrecisionTransform(float x, float y, float scale, float rotation) {
    }
    
    /**
     * 重置变换（集中化版本：通过WindowPositionManager处理位置重置）
     */
    public final void resetTransform() {
    }
    
    /**
     * 保存当前变换状态（集中化版本：通过WindowPositionManager处理位置保存）
     */
    public final void saveCurrentTransformState(boolean isCroppedWindow, @org.jetbrains.annotations.Nullable()
    android.graphics.RectF cropRectRatio) {
    }
    
    /**
     * 恢复保存的变换状态
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlin.Pair<java.lang.Boolean, android.graphics.RectF> restoreSavedTransformState() {
        return null;
    }
    
    /**
     * 通知变换变化（带频率控制）
     */
    public final void notifyTransformChange(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean forceNotify) {
    }
    
    /**
     * 立即通知变换变化（不受频率限制）
     */
    public final void notifyTransformChangeImmediate(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 清理资源
     */
    public final void cleanup() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0006"}, d2 = {"Lcom/example/castapp/ui/windowsettings/TransformManager$Companion;", "", "()V", "MAX_SCALE_FACTOR", "", "MIN_SCALE_FACTOR", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}