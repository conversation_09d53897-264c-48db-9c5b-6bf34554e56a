package com.example.castapp.model;

/**
 * 投屏窗口信息数据类
 * 用于窗口管理器显示窗口详细信息
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u0007\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\bU\b\u0086\b\u0018\u00002\u00020\u0001B\u00b1\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u0007\u001a\u00020\b\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0002\u0010\u000b\u001a\u00020\f\u0012\b\b\u0002\u0010\r\u001a\u00020\f\u0012\b\b\u0002\u0010\u000e\u001a\u00020\f\u0012\b\b\u0002\u0010\u000f\u001a\u00020\f\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u0011\u001a\u00020\b\u0012\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0013\u0012\b\b\u0002\u0010\u0014\u001a\u00020\b\u0012\b\b\u0002\u0010\u0015\u001a\u00020\b\u0012\b\b\u0002\u0010\u0016\u001a\u00020\b\u0012\b\b\u0002\u0010\u0017\u001a\u00020\b\u0012\b\b\u0002\u0010\u0018\u001a\u00020\b\u0012\b\b\u0002\u0010\u0019\u001a\u00020\f\u0012\b\b\u0002\u0010\u001a\u001a\u00020\f\u0012\b\b\u0002\u0010\u001b\u001a\u00020\b\u0012\b\b\u0002\u0010\u001c\u001a\u00020\b\u0012\b\b\u0002\u0010\u001d\u001a\u00020\b\u0012\b\b\u0002\u0010\u001e\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u001f\u001a\u00020\f\u0012\b\b\u0002\u0010 \u001a\u00020\u0006\u0012\b\b\u0002\u0010!\u001a\u00020\u0006\u0012\b\b\u0002\u0010\"\u001a\u00020\b\u0012\b\b\u0002\u0010#\u001a\u00020\u0006\u0012\b\b\u0002\u0010$\u001a\u00020\b\u00a2\u0006\u0002\u0010%J\t\u0010>\u001a\u00020\u0003H\u00c6\u0003J\t\u0010?\u001a\u00020\fH\u00c6\u0003J\t\u0010@\u001a\u00020\u0006H\u00c6\u0003J\t\u0010A\u001a\u00020\bH\u00c6\u0003J\u000b\u0010B\u001a\u0004\u0018\u00010\u0013H\u00c6\u0003J\t\u0010C\u001a\u00020\bH\u00c6\u0003J\t\u0010D\u001a\u00020\bH\u00c6\u0003J\t\u0010E\u001a\u00020\bH\u00c6\u0003J\t\u0010F\u001a\u00020\bH\u00c6\u0003J\t\u0010G\u001a\u00020\bH\u00c6\u0003J\t\u0010H\u001a\u00020\fH\u00c6\u0003J\t\u0010I\u001a\u00020\u0003H\u00c6\u0003J\t\u0010J\u001a\u00020\fH\u00c6\u0003J\t\u0010K\u001a\u00020\bH\u00c6\u0003J\t\u0010L\u001a\u00020\bH\u00c6\u0003J\t\u0010M\u001a\u00020\bH\u00c6\u0003J\t\u0010N\u001a\u00020\u0006H\u00c6\u0003J\t\u0010O\u001a\u00020\fH\u00c6\u0003J\t\u0010P\u001a\u00020\u0006H\u00c6\u0003J\t\u0010Q\u001a\u00020\u0006H\u00c6\u0003J\t\u0010R\u001a\u00020\bH\u00c6\u0003J\t\u0010S\u001a\u00020\u0006H\u00c6\u0003J\t\u0010T\u001a\u00020\u0006H\u00c6\u0003J\t\u0010U\u001a\u00020\bH\u00c6\u0003J\t\u0010V\u001a\u00020\bH\u00c6\u0003J\u000b\u0010W\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010X\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010Y\u001a\u00020\fH\u00c6\u0003J\t\u0010Z\u001a\u00020\fH\u00c6\u0003J\t\u0010[\u001a\u00020\fH\u00c6\u0003J\u00bb\u0002\u0010\\\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\f2\b\b\u0002\u0010\u000e\u001a\u00020\f2\b\b\u0002\u0010\u000f\u001a\u00020\f2\b\b\u0002\u0010\u0010\u001a\u00020\u00062\b\b\u0002\u0010\u0011\u001a\u00020\b2\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u00132\b\b\u0002\u0010\u0014\u001a\u00020\b2\b\b\u0002\u0010\u0015\u001a\u00020\b2\b\b\u0002\u0010\u0016\u001a\u00020\b2\b\b\u0002\u0010\u0017\u001a\u00020\b2\b\b\u0002\u0010\u0018\u001a\u00020\b2\b\b\u0002\u0010\u0019\u001a\u00020\f2\b\b\u0002\u0010\u001a\u001a\u00020\f2\b\b\u0002\u0010\u001b\u001a\u00020\b2\b\b\u0002\u0010\u001c\u001a\u00020\b2\b\b\u0002\u0010\u001d\u001a\u00020\b2\b\b\u0002\u0010\u001e\u001a\u00020\u00062\b\b\u0002\u0010\u001f\u001a\u00020\f2\b\b\u0002\u0010 \u001a\u00020\u00062\b\b\u0002\u0010!\u001a\u00020\u00062\b\b\u0002\u0010\"\u001a\u00020\b2\b\b\u0002\u0010#\u001a\u00020\u00062\b\b\u0002\u0010$\u001a\u00020\bH\u00c6\u0001J\u0013\u0010]\u001a\u00020\b2\b\u0010^\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u0006\u0010_\u001a\u00020\u0003J\u0006\u0010`\u001a\u00020\u0003J\u0006\u0010a\u001a\u00020\u0003J\u0006\u0010b\u001a\u00020\u0003J\u0006\u0010c\u001a\u00020\u0003J\u0006\u0010d\u001a\u00020\u0003J\u0006\u0010e\u001a\u00020\u0003J\t\u0010f\u001a\u00020\u0006H\u00d6\u0001J\t\u0010g\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u001a\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\'R\u0011\u0010!\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010)R\u0011\u0010 \u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010)R\u0011\u0010\u001e\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010)R\u0011\u0010\u001f\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010\'R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b-\u0010.R\u0011\u0010\u0019\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u0010\'R\u0013\u0010\u0012\u001a\u0004\u0018\u00010\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b0\u00101R\u0013\u0010\t\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b2\u0010.R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b3\u0010.R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u00104R\u0011\u0010\u001d\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u00104R\u0011\u0010\u001b\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u00104R\u0011\u0010\u0011\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u00104R\u0011\u0010\u0014\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u00104R\u0011\u0010\u001c\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u00104R\u0011\u0010$\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u00104R\u0011\u0010\u0018\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u00104R\u0011\u0010\u0016\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u00104R\u0011\u0010\u0015\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u00104R\u0011\u0010\u0017\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u00104R\u0013\u0010\n\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b5\u0010.R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b6\u0010)R\u0011\u0010\u000b\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b7\u0010\'R\u0011\u0010\r\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b8\u0010\'R\u0011\u0010\u000f\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b9\u0010\'R\u0011\u0010\u000e\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b:\u0010\'R\u0011\u0010#\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b;\u0010)R\u0011\u0010\"\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b<\u00104R\u0011\u0010\u0010\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b=\u0010)\u00a8\u0006h"}, d2 = {"Lcom/example/castapp/model/CastWindowInfo;", "", "connectionId", "", "ipAddress", "port", "", "isActive", "", "deviceName", "note", "positionX", "", "positionY", "scaleFactor", "rotationAngle", "zOrder", "isCropping", "cropRectRatio", "Landroid/graphics/RectF;", "isDragEnabled", "isScaleEnabled", "isRotationEnabled", "isVisible", "isMirrored", "cornerRadius", "alpha", "isControlEnabled", "isEditEnabled", "isBorderEnabled", "borderColor", "borderWidth", "baseWindowWidth", "baseWindowHeight", "windowColorEnabled", "windowBackgroundColor", "isLandscapeModeEnabled", "(Ljava/lang/String;Ljava/lang/String;IZLjava/lang/String;Ljava/lang/String;FFFFIZLandroid/graphics/RectF;ZZZZZFFZZZIFIIZIZ)V", "getAlpha", "()F", "getBaseWindowHeight", "()I", "getBaseWindowWidth", "getBorderColor", "getBorderWidth", "getConnectionId", "()Ljava/lang/String;", "getCornerRadius", "getCropRectRatio", "()Landroid/graphics/RectF;", "getDeviceName", "getIpAddress", "()Z", "getNote", "getPort", "getPositionX", "getPositionY", "getRotationAngle", "getScaleFactor", "getWindowBackgroundColor", "getWindowColorEnabled", "getZOrder", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component20", "component21", "component22", "component23", "component24", "component25", "component26", "component27", "component28", "component29", "component3", "component30", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "getDisplayAddress", "getDisplayText", "getDisplayTextWithDevice", "getNoteDisplayText", "getShortConnectionId", "getTransformInfo", "getWindowSizeInfo", "hashCode", "toString", "app_debug"})
public final class CastWindowInfo {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String connectionId = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String ipAddress = null;
    private final int port = 0;
    private final boolean isActive = false;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String deviceName = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String note = null;
    private final float positionX = 0.0F;
    private final float positionY = 0.0F;
    private final float scaleFactor = 0.0F;
    private final float rotationAngle = 0.0F;
    private final int zOrder = 0;
    private final boolean isCropping = false;
    @org.jetbrains.annotations.Nullable()
    private final android.graphics.RectF cropRectRatio = null;
    private final boolean isDragEnabled = false;
    private final boolean isScaleEnabled = false;
    private final boolean isRotationEnabled = false;
    private final boolean isVisible = false;
    private final boolean isMirrored = false;
    private final float cornerRadius = 0.0F;
    private final float alpha = 0.0F;
    private final boolean isControlEnabled = false;
    private final boolean isEditEnabled = false;
    private final boolean isBorderEnabled = false;
    private final int borderColor = 0;
    private final float borderWidth = 0.0F;
    private final int baseWindowWidth = 0;
    private final int baseWindowHeight = 0;
    private final boolean windowColorEnabled = false;
    private final int windowBackgroundColor = 0;
    private final boolean isLandscapeModeEnabled = false;
    
    public CastWindowInfo(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    java.lang.String ipAddress, int port, boolean isActive, @org.jetbrains.annotations.Nullable()
    java.lang.String deviceName, @org.jetbrains.annotations.Nullable()
    java.lang.String note, float positionX, float positionY, float scaleFactor, float rotationAngle, int zOrder, boolean isCropping, @org.jetbrains.annotations.Nullable()
    android.graphics.RectF cropRectRatio, boolean isDragEnabled, boolean isScaleEnabled, boolean isRotationEnabled, boolean isVisible, boolean isMirrored, float cornerRadius, float alpha, boolean isControlEnabled, boolean isEditEnabled, boolean isBorderEnabled, int borderColor, float borderWidth, int baseWindowWidth, int baseWindowHeight, boolean windowColorEnabled, int windowBackgroundColor, boolean isLandscapeModeEnabled) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getConnectionId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getIpAddress() {
        return null;
    }
    
    public final int getPort() {
        return 0;
    }
    
    public final boolean isActive() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getDeviceName() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getNote() {
        return null;
    }
    
    public final float getPositionX() {
        return 0.0F;
    }
    
    public final float getPositionY() {
        return 0.0F;
    }
    
    public final float getScaleFactor() {
        return 0.0F;
    }
    
    public final float getRotationAngle() {
        return 0.0F;
    }
    
    public final int getZOrder() {
        return 0;
    }
    
    public final boolean isCropping() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final android.graphics.RectF getCropRectRatio() {
        return null;
    }
    
    public final boolean isDragEnabled() {
        return false;
    }
    
    public final boolean isScaleEnabled() {
        return false;
    }
    
    public final boolean isRotationEnabled() {
        return false;
    }
    
    public final boolean isVisible() {
        return false;
    }
    
    public final boolean isMirrored() {
        return false;
    }
    
    public final float getCornerRadius() {
        return 0.0F;
    }
    
    public final float getAlpha() {
        return 0.0F;
    }
    
    public final boolean isControlEnabled() {
        return false;
    }
    
    public final boolean isEditEnabled() {
        return false;
    }
    
    public final boolean isBorderEnabled() {
        return false;
    }
    
    public final int getBorderColor() {
        return 0;
    }
    
    public final float getBorderWidth() {
        return 0.0F;
    }
    
    public final int getBaseWindowWidth() {
        return 0;
    }
    
    public final int getBaseWindowHeight() {
        return 0;
    }
    
    public final boolean getWindowColorEnabled() {
        return false;
    }
    
    public final int getWindowBackgroundColor() {
        return 0;
    }
    
    public final boolean isLandscapeModeEnabled() {
        return false;
    }
    
    /**
     * 获取显示用的连接地址
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDisplayAddress() {
        return null;
    }
    
    /**
     * 获取简化的连接ID（显示规则）
     * - 摄像头窗口：显示完整ID
     * - 媒体窗口：显示文件名前8位
     * - 文本窗口：显示UUID后8位
     * - 投屏窗口：显示后8位
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getShortConnectionId() {
        return null;
    }
    
    /**
     * 获取完整的显示文本
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDisplayText() {
        return null;
    }
    
    /**
     * 获取带设备信息的显示文本
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDisplayTextWithDevice() {
        return null;
    }
    
    /**
     * 🏷️ 获取备注显示文本
     * @return 备注内容，如"无"或"客厅电视"
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNoteDisplayText() {
        return null;
    }
    
    /**
     * 获取格式化的变换信息
     * 格式：位置（X,Y） 缩放：1.5  旋转：75° 层级：1
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getTransformInfo() {
        return null;
    }
    
    /**
     * 获取当前实际窗口尺寸（经过裁剪和缩放后的尺寸）
     * 格式：窗口尺寸:108×240
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getWindowSizeInfo() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    public final float component10() {
        return 0.0F;
    }
    
    public final int component11() {
        return 0;
    }
    
    public final boolean component12() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final android.graphics.RectF component13() {
        return null;
    }
    
    public final boolean component14() {
        return false;
    }
    
    public final boolean component15() {
        return false;
    }
    
    public final boolean component16() {
        return false;
    }
    
    public final boolean component17() {
        return false;
    }
    
    public final boolean component18() {
        return false;
    }
    
    public final float component19() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    public final float component20() {
        return 0.0F;
    }
    
    public final boolean component21() {
        return false;
    }
    
    public final boolean component22() {
        return false;
    }
    
    public final boolean component23() {
        return false;
    }
    
    public final int component24() {
        return 0;
    }
    
    public final float component25() {
        return 0.0F;
    }
    
    public final int component26() {
        return 0;
    }
    
    public final int component27() {
        return 0;
    }
    
    public final boolean component28() {
        return false;
    }
    
    public final int component29() {
        return 0;
    }
    
    public final int component3() {
        return 0;
    }
    
    public final boolean component30() {
        return false;
    }
    
    public final boolean component4() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component6() {
        return null;
    }
    
    public final float component7() {
        return 0.0F;
    }
    
    public final float component8() {
        return 0.0F;
    }
    
    public final float component9() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.model.CastWindowInfo copy(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    java.lang.String ipAddress, int port, boolean isActive, @org.jetbrains.annotations.Nullable()
    java.lang.String deviceName, @org.jetbrains.annotations.Nullable()
    java.lang.String note, float positionX, float positionY, float scaleFactor, float rotationAngle, int zOrder, boolean isCropping, @org.jetbrains.annotations.Nullable()
    android.graphics.RectF cropRectRatio, boolean isDragEnabled, boolean isScaleEnabled, boolean isRotationEnabled, boolean isVisible, boolean isMirrored, float cornerRadius, float alpha, boolean isControlEnabled, boolean isEditEnabled, boolean isBorderEnabled, int borderColor, float borderWidth, int baseWindowWidth, int baseWindowHeight, boolean windowColorEnabled, int windowBackgroundColor, boolean isLandscapeModeEnabled) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}