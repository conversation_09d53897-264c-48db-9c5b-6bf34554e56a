package com.example.castapp.manager;

/**
 * 多摄像头管理器
 * 支持同时管理前置和后置摄像头预览
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000h\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0011\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\f\u0018\u0000 -2\u00020\u0001:\u0002,-B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u0014H\u0002J\u001f\u0010\u0015\u001a\u0004\u0018\u00010\u00162\u000e\u0010\u0017\u001a\n\u0012\u0004\u0012\u00020\u0016\u0018\u00010\u0018H\u0002\u00a2\u0006\u0002\u0010\u0019J\u0006\u0010\u001a\u001a\u00020\u001bJ\u0010\u0010\u001c\u001a\u00020\u001b2\u0006\u0010\u001d\u001a\u00020\u000bH\u0002J,\u0010\u001e\u001a\u00020\u001b2\u0006\u0010\u001d\u001a\u00020\u000b2\u0006\u0010\u001f\u001a\u00020 2\u0012\u0010!\u001a\u000e\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u001b0\"H\u0002J\u0012\u0010#\u001a\u0004\u0018\u00010\u000b2\u0006\u0010$\u001a\u00020\u0012H\u0002J\u0010\u0010%\u001a\u00020\u001b2\u0006\u0010\u0013\u001a\u00020\u0014H\u0002J<\u0010&\u001a\u00020\u001b2\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u001d\u001a\u00020\u000b2\u0006\u0010\'\u001a\u00020\u000b2\u0006\u0010\u001f\u001a\u00020 2\u0012\u0010!\u001a\u000e\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u001b0\"H\u0002J\b\u0010(\u001a\u00020\u001bH\u0002J:\u0010)\u001a\u00020\u001b2\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u001d\u001a\u00020\u000b2\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010$\u001a\u00020\u00122\u0012\u0010!\u001a\u000e\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u001b0\"J\b\u0010*\u001a\u00020\u001bH\u0002J\u000e\u0010+\u001a\u00020\u001b2\u0006\u0010\u001d\u001a\u00020\u000bR\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0007\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\f0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\r\u001a\u0004\u0018\u00010\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006."}, d2 = {"Lcom/example/castapp/manager/MultiCameraManager;", "", "()V", "backgroundExecutor", "Ljava/util/concurrent/Executor;", "backgroundHandler", "Landroid/os/Handler;", "backgroundThread", "Landroid/os/HandlerThread;", "cameraInstances", "Ljava/util/concurrent/ConcurrentHashMap;", "", "Lcom/example/castapp/manager/MultiCameraManager$CameraInstance;", "cameraManager", "Landroid/hardware/camera2/CameraManager;", "cameraOpenCloseLock", "Ljava/util/concurrent/Semaphore;", "checkCameraPermission", "", "context", "Landroid/content/Context;", "chooseOptimalSize", "Landroid/util/Size;", "choices", "", "([Landroid/util/Size;)Landroid/util/Size;", "cleanup", "", "closeCamera", "windowId", "createCameraPreviewSession", "surfaceTexture", "Landroid/graphics/SurfaceTexture;", "callback", "Lkotlin/Function1;", "getCameraId", "isFrontCamera", "initializeCameraManager", "openCamera", "cameraId", "startBackgroundThread", "startCameraPreview", "stopBackgroundThread", "stopCameraPreview", "CameraInstance", "Companion", "app_debug"})
public final class MultiCameraManager {
    @org.jetbrains.annotations.Nullable()
    private android.hardware.camera2.CameraManager cameraManager;
    @org.jetbrains.annotations.Nullable()
    private android.os.HandlerThread backgroundThread;
    @org.jetbrains.annotations.Nullable()
    private android.os.Handler backgroundHandler;
    @org.jetbrains.annotations.Nullable()
    private java.util.concurrent.Executor backgroundExecutor;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, com.example.castapp.manager.MultiCameraManager.CameraInstance> cameraInstances = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.Semaphore cameraOpenCloseLock = null;
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.example.castapp.manager.MultiCameraManager INSTANCE;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.manager.MultiCameraManager.Companion Companion = null;
    
    private MultiCameraManager() {
        super();
    }
    
    /**
     * 初始化摄像头管理器
     */
    private final void initializeCameraManager(android.content.Context context) {
    }
    
    /**
     * 启动摄像头预览
     */
    public final void startCameraPreview(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String windowId, @org.jetbrains.annotations.NotNull()
    android.graphics.SurfaceTexture surfaceTexture, boolean isFrontCamera, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> callback) {
    }
    
    /**
     * 停止摄像头预览
     */
    public final void stopCameraPreview(@org.jetbrains.annotations.NotNull()
    java.lang.String windowId) {
    }
    
    /**
     * 检查摄像头权限
     */
    private final boolean checkCameraPermission(android.content.Context context) {
        return false;
    }
    
    /**
     * 获取摄像头ID
     */
    private final java.lang.String getCameraId(boolean isFrontCamera) {
        return null;
    }
    
    /**
     * 选择最佳预览尺寸
     */
    private final android.util.Size chooseOptimalSize(android.util.Size[] choices) {
        return null;
    }
    
    /**
     * 打开摄像头
     */
    private final void openCamera(android.content.Context context, java.lang.String windowId, java.lang.String cameraId, android.graphics.SurfaceTexture surfaceTexture, kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> callback) {
    }
    
    /**
     * 创建摄像头预览会话
     */
    private final void createCameraPreviewSession(java.lang.String windowId, android.graphics.SurfaceTexture surfaceTexture, kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> callback) {
    }
    
    /**
     * 关闭摄像头
     */
    private final void closeCamera(java.lang.String windowId) {
    }
    
    /**
     * 启动后台线程
     */
    private final void startBackgroundThread() {
    }
    
    /**
     * 停止后台线程
     */
    private final void stopBackgroundThread() {
    }
    
    /**
     * 清理资源
     */
    public final void cleanup() {
    }
    
    /**
     * 摄像头实例数据类
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u001d\n\u0002\u0010\b\n\u0002\b\u0002\b\u0082\b\u0018\u00002\u00020\u0001BC\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\u0006\u0010\f\u001a\u00020\t\u00a2\u0006\u0002\u0010\rJ\u000b\u0010\u001f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010 \u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010!\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J\t\u0010\"\u001a\u00020\tH\u00c6\u0003J\t\u0010#\u001a\u00020\u000bH\u00c6\u0003J\t\u0010$\u001a\u00020\tH\u00c6\u0003JK\u0010%\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\tH\u00c6\u0001J\u0013\u0010&\u001a\u00020\t2\b\u0010\'\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010(\u001a\u00020)H\u00d6\u0001J\t\u0010*\u001a\u00020\u000bH\u00d6\u0001R\u001c\u0010\u0002\u001a\u0004\u0018\u00010\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000e\u0010\u000f\"\u0004\b\u0010\u0010\u0011R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u001c\u0010\u0004\u001a\u0004\u0018\u00010\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0014\u0010\u0015\"\u0004\b\u0016\u0010\u0017R\u001a\u0010\b\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\b\u0010\u0018\"\u0004\b\u0019\u0010\u001aR\u0011\u0010\f\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u0018R\u001c\u0010\u0006\u001a\u0004\u0018\u00010\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001b\u0010\u001c\"\u0004\b\u001d\u0010\u001e\u00a8\u0006+"}, d2 = {"Lcom/example/castapp/manager/MultiCameraManager$CameraInstance;", "", "cameraDevice", "Landroid/hardware/camera2/CameraDevice;", "captureSession", "Landroid/hardware/camera2/CameraCaptureSession;", "previewSize", "Landroid/util/Size;", "isActive", "", "cameraId", "", "isFrontCamera", "(Landroid/hardware/camera2/CameraDevice;Landroid/hardware/camera2/CameraCaptureSession;Landroid/util/Size;ZLjava/lang/String;Z)V", "getCameraDevice", "()Landroid/hardware/camera2/CameraDevice;", "setCameraDevice", "(Landroid/hardware/camera2/CameraDevice;)V", "getCameraId", "()Ljava/lang/String;", "getCaptureSession", "()Landroid/hardware/camera2/CameraCaptureSession;", "setCaptureSession", "(Landroid/hardware/camera2/CameraCaptureSession;)V", "()Z", "setActive", "(Z)V", "getPreviewSize", "()Landroid/util/Size;", "setPreviewSize", "(Landroid/util/Size;)V", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
    static final class CameraInstance {
        @org.jetbrains.annotations.Nullable()
        private android.hardware.camera2.CameraDevice cameraDevice;
        @org.jetbrains.annotations.Nullable()
        private android.hardware.camera2.CameraCaptureSession captureSession;
        @org.jetbrains.annotations.Nullable()
        private android.util.Size previewSize;
        private boolean isActive;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String cameraId = null;
        private final boolean isFrontCamera = false;
        
        public CameraInstance(@org.jetbrains.annotations.Nullable()
        android.hardware.camera2.CameraDevice cameraDevice, @org.jetbrains.annotations.Nullable()
        android.hardware.camera2.CameraCaptureSession captureSession, @org.jetbrains.annotations.Nullable()
        android.util.Size previewSize, boolean isActive, @org.jetbrains.annotations.NotNull()
        java.lang.String cameraId, boolean isFrontCamera) {
            super();
        }
        
        @org.jetbrains.annotations.Nullable()
        public final android.hardware.camera2.CameraDevice getCameraDevice() {
            return null;
        }
        
        public final void setCameraDevice(@org.jetbrains.annotations.Nullable()
        android.hardware.camera2.CameraDevice p0) {
        }
        
        @org.jetbrains.annotations.Nullable()
        public final android.hardware.camera2.CameraCaptureSession getCaptureSession() {
            return null;
        }
        
        public final void setCaptureSession(@org.jetbrains.annotations.Nullable()
        android.hardware.camera2.CameraCaptureSession p0) {
        }
        
        @org.jetbrains.annotations.Nullable()
        public final android.util.Size getPreviewSize() {
            return null;
        }
        
        public final void setPreviewSize(@org.jetbrains.annotations.Nullable()
        android.util.Size p0) {
        }
        
        public final boolean isActive() {
            return false;
        }
        
        public final void setActive(boolean p0) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getCameraId() {
            return null;
        }
        
        public final boolean isFrontCamera() {
            return false;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final android.hardware.camera2.CameraDevice component1() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final android.hardware.camera2.CameraCaptureSession component2() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final android.util.Size component3() {
            return null;
        }
        
        public final boolean component4() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component5() {
            return null;
        }
        
        public final boolean component6() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.manager.MultiCameraManager.CameraInstance copy(@org.jetbrains.annotations.Nullable()
        android.hardware.camera2.CameraDevice cameraDevice, @org.jetbrains.annotations.Nullable()
        android.hardware.camera2.CameraCaptureSession captureSession, @org.jetbrains.annotations.Nullable()
        android.util.Size previewSize, boolean isActive, @org.jetbrains.annotations.NotNull()
        java.lang.String cameraId, boolean isFrontCamera) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0005\u001a\u00020\u0004R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0006"}, d2 = {"Lcom/example/castapp/manager/MultiCameraManager$Companion;", "", "()V", "INSTANCE", "Lcom/example/castapp/manager/MultiCameraManager;", "getInstance", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.manager.MultiCameraManager getInstance() {
            return null;
        }
    }
}