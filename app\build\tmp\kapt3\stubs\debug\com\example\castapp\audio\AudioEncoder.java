package com.example.castapp.audio;

/**
 * AAC音频编码器 - 异步模式
 * 🚀 基于MediaCodec.Callback的高性能异步编码实现
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000p\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0012\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\t\u0018\u0000 92\u00020\u0001:\u000389:Be\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0003\u0012\u001e\u0010\u0006\u001a\u001a\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n0\u0007\u0012\u0016\b\u0002\u0010\u000b\u001a\u0010\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\n\u0018\u00010\f\u0012\b\b\u0002\u0010\u000e\u001a\u00020\t\u00a2\u0006\u0002\u0010\u000fJ\u0010\u0010!\u001a\u00020\n2\u0006\u0010\"\u001a\u00020\rH\u0002J\u000e\u0010#\u001a\u00020\n2\u0006\u0010$\u001a\u00020\u0015J\u000e\u0010%\u001a\u00020\n2\u0006\u0010\"\u001a\u00020\rJ \u0010&\u001a\u00020\n2\u0006\u0010\'\u001a\u00020\u001b2\u0006\u0010(\u001a\u00020\u00032\u0006\u0010)\u001a\u00020*H\u0002J \u0010+\u001a\u00020\n2\u0006\u0010\'\u001a\u00020\u001b2\u0006\u0010(\u001a\u00020\u00032\u0006\u0010)\u001a\u00020*H\u0002J\u0006\u0010\u0018\u001a\u00020\tJ\u0018\u0010,\u001a\u00020\t2\u0006\u0010\'\u001a\u00020\u001b2\u0006\u0010-\u001a\u00020\u0003H\u0002J\"\u0010.\u001a\u00020\n2\u0006\u0010$\u001a\u00020\u00152\u0012\u0010/\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\n0\fJ\u0010\u00100\u001a\u00020\n2\u0006\u00101\u001a\u000202H\u0002J\u0006\u00103\u001a\u00020\tJ\u0006\u00104\u001a\u00020\nJ\b\u00105\u001a\u00020\nH\u0002J\u000e\u00106\u001a\u00020\n2\u0006\u00107\u001a\u00020 R\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00030\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0012\u001a\u0004\u0018\u00010\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020\t0\u0014X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\r0\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0019X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001a\u001a\u0004\u0018\u00010\u001bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u001c\u001a\n \u001e*\u0004\u0018\u00010\u001d0\u001dX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u000b\u001a\u0010\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\n\u0018\u00010\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R&\u0010\u0006\u001a\u001a\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001f\u001a\u00020 X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006;"}, d2 = {"Lcom/example/castapp/audio/AudioEncoder;", "", "sampleRate", "", "channelCount", "bitRate", "onEncodedData", "Lkotlin/Function3;", "Lcom/example/castapp/network/DataView;", "", "", "onConfigurationDataViaWebSocket", "Lkotlin/Function1;", "", "lowLatencyMode", "(IIILkotlin/jvm/functions/Function3;Lkotlin/jvm/functions/Function1;Z)V", "availableInputBuffers", "Ljava/util/concurrent/ConcurrentLinkedQueue;", "cachedConfigData", "configDataSentToConnections", "Ljava/util/concurrent/ConcurrentHashMap;", "", "configurationSent", "inputQueue", "isRunning", "Ljava/util/concurrent/atomic/AtomicBoolean;", "mediaCodec", "Landroid/media/MediaCodec;", "networkExecutor", "Ljava/util/concurrent/ExecutorService;", "kotlin.jvm.PlatformType", "volumeMultiplier", "", "applyVolumeAdjustmentInPlace", "pcmData", "clearConnectionConfigState", "connectionId", "encode", "handleConfigurationData", "codec", "index", "info", "Landroid/media/MediaCodec$BufferInfo;", "handleEncodedData", "processInputData", "bufferIndex", "sendCachedConfigurationDataViaWebSocket", "onConfigData", "sendConfigurationData", "format", "Landroid/media/MediaFormat;", "start", "stop", "tryProcessPendingData", "updateVolume", "volume", "AudioBufferReference", "Companion", "MediaCodecCallback", "app_debug"})
public final class AudioEncoder {
    private final int sampleRate = 0;
    private final int channelCount = 0;
    private final int bitRate = 0;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function3<com.example.castapp.network.DataView, java.lang.Integer, java.lang.Boolean, kotlin.Unit> onEncodedData = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlin.jvm.functions.Function1<byte[], kotlin.Unit> onConfigurationDataViaWebSocket = null;
    private final boolean lowLatencyMode = false;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String MIME_TYPE = "audio/mp4a-latm";
    @org.jetbrains.annotations.Nullable()
    private android.media.MediaCodec mediaCodec;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.atomic.AtomicBoolean isRunning = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentLinkedQueue<byte[]> inputQueue = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentLinkedQueue<java.lang.Integer> availableInputBuffers = null;
    @org.jetbrains.annotations.Nullable()
    private byte[] cachedConfigData;
    private boolean configurationSent = false;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, java.lang.Boolean> configDataSentToConnections = null;
    @kotlin.jvm.Volatile()
    private volatile float volumeMultiplier = 1.0F;
    private final java.util.concurrent.ExecutorService networkExecutor = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.audio.AudioEncoder.Companion Companion = null;
    
    public AudioEncoder(int sampleRate, int channelCount, int bitRate, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super com.example.castapp.network.DataView, ? super java.lang.Integer, ? super java.lang.Boolean, kotlin.Unit> onEncodedData, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super byte[], kotlin.Unit> onConfigurationDataViaWebSocket, boolean lowLatencyMode) {
        super();
    }
    
    /**
     * 启动异步编码器
     */
    public final boolean start() {
        return false;
    }
    
    /**
     * 停止异步编码器
     */
    public final void stop() {
    }
    
    /**
     * 输入PCM音频数据进行编码 - 异步模式
     */
    public final void encode(@org.jetbrains.annotations.NotNull()
    byte[] pcmData) {
    }
    
    /**
     * 🚀 关键方法：尝试处理待处理的数据
     * 当新数据加入队列时，检查是否有可用的输入缓冲区并立即处理
     */
    private final void tryProcessPendingData() {
    }
    
    /**
     * 处理输入数据
     * @return true 如果成功处理了数据，false 如果没有数据可处理
     */
    private final boolean processInputData(android.media.MediaCodec codec, int bufferIndex) {
        return false;
    }
    
    /**
     * 处理配置数据
     */
    private final void handleConfigurationData(android.media.MediaCodec codec, int index, android.media.MediaCodec.BufferInfo info) {
    }
    
    /**
     * 处理编码后的数据 - 🚀 零拷贝优化版本
     */
    private final void handleEncodedData(android.media.MediaCodec codec, int index, android.media.MediaCodec.BufferInfo info) {
    }
    
    /**
     * 发送配置数据（从输出格式变更中提取）
     */
    private final void sendConfigurationData(android.media.MediaFormat format) {
    }
    
    /**
     * 🐾 根源修复：为新连接通过WebSocket发送缓存的配置数据，带去重检查
     */
    public final void sendCachedConfigurationDataViaWebSocket(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super byte[], kotlin.Unit> onConfigData) {
    }
    
    /**
     * 获取编码器状态
     */
    public final boolean isRunning() {
        return false;
    }
    
    /**
     * 🐾 根源修复：清理连接的配置数据发送状态
     */
    public final void clearConnectionConfigState(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 更新音量（0.0-1.0）
     */
    public final void updateVolume(float volume) {
    }
    
    /**
     * 就地应用音量调节到PCM数据 - 零拷贝版本
     */
    private final void applyVolumeAdjustmentInPlace(byte[] pcmData) {
    }
    
    /**
     * 🚀 零拷贝核心：音频缓冲区引用管理
     * 延迟释放MediaCodec缓冲区直到网络发送完成
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\b\u0002\u0018\u00002\u00020\u0001B%\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\tJ\u0006\u0010\u0012\u001a\u00020\u0013R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0014"}, d2 = {"Lcom/example/castapp/audio/AudioEncoder$AudioBufferReference;", "", "outputBuffer", "Ljava/nio/ByteBuffer;", "codec", "Landroid/media/MediaCodec;", "bufferIndex", "", "dataSize", "(Ljava/nio/ByteBuffer;Landroid/media/MediaCodec;II)V", "dataView", "Lcom/example/castapp/network/DataView;", "getDataView", "()Lcom/example/castapp/network/DataView;", "isReleased", "", "refCount", "Ljava/util/concurrent/atomic/AtomicInteger;", "release", "", "app_debug"})
    static final class AudioBufferReference {
        @org.jetbrains.annotations.NotNull()
        private final java.nio.ByteBuffer outputBuffer = null;
        @org.jetbrains.annotations.NotNull()
        private final android.media.MediaCodec codec = null;
        private final int bufferIndex = 0;
        private final int dataSize = 0;
        @org.jetbrains.annotations.NotNull()
        private final java.util.concurrent.atomic.AtomicInteger refCount = null;
        private boolean isReleased = false;
        
        /**
         * 获取只读数据视图，完全避免数据拷贝
         */
        @org.jetbrains.annotations.NotNull()
        private final com.example.castapp.network.DataView dataView = null;
        
        public AudioBufferReference(@org.jetbrains.annotations.NotNull()
        java.nio.ByteBuffer outputBuffer, @org.jetbrains.annotations.NotNull()
        android.media.MediaCodec codec, int bufferIndex, int dataSize) {
            super();
        }
        
        /**
         * 获取只读数据视图，完全避免数据拷贝
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.network.DataView getDataView() {
            return null;
        }
        
        /**
         * 减少引用计数，当计数为0时释放MediaCodec缓冲区
         */
        public final void release() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/example/castapp/audio/AudioEncoder$Companion;", "", "()V", "MIME_TYPE", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
    
    /**
     * MediaCodec异步回调处理器
     * 🚀 异步模式：系统驱动的高效编码处理
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0082\u0004\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0018\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0016J\u0018\u0010\t\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u000bH\u0016J \u0010\f\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\r\u001a\u00020\u000eH\u0016J\u0018\u0010\u000f\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0010\u001a\u00020\u0011H\u0016\u00a8\u0006\u0012"}, d2 = {"Lcom/example/castapp/audio/AudioEncoder$MediaCodecCallback;", "Landroid/media/MediaCodec$Callback;", "(Lcom/example/castapp/audio/AudioEncoder;)V", "onError", "", "codec", "Landroid/media/MediaCodec;", "e", "Landroid/media/MediaCodec$CodecException;", "onInputBufferAvailable", "index", "", "onOutputBufferAvailable", "info", "Landroid/media/MediaCodec$BufferInfo;", "onOutputFormatChanged", "format", "Landroid/media/MediaFormat;", "app_debug"})
    final class MediaCodecCallback extends android.media.MediaCodec.Callback {
        
        public MediaCodecCallback() {
            super();
        }
        
        @java.lang.Override()
        public void onInputBufferAvailable(@org.jetbrains.annotations.NotNull()
        android.media.MediaCodec codec, int index) {
        }
        
        @java.lang.Override()
        public void onOutputBufferAvailable(@org.jetbrains.annotations.NotNull()
        android.media.MediaCodec codec, int index, @org.jetbrains.annotations.NotNull()
        android.media.MediaCodec.BufferInfo info) {
        }
        
        @java.lang.Override()
        public void onError(@org.jetbrains.annotations.NotNull()
        android.media.MediaCodec codec, @org.jetbrains.annotations.NotNull()
        android.media.MediaCodec.CodecException e) {
        }
        
        @java.lang.Override()
        public void onOutputFormatChanged(@org.jetbrains.annotations.NotNull()
        android.media.MediaCodec codec, @org.jetbrains.annotations.NotNull()
        android.media.MediaFormat format) {
        }
    }
}