package com.example.castapp.utils;

/**
 * 全局字间距预设管理器
 * 负责管理所有文本窗口共享的字间距预设列表
 * 提供持久化存储和实时同步功能
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010#\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\r\u0018\u0000 !2\u00020\u0001:\u0002!\"B\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000fJ\u000e\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\tJ\u000e\u0010\u0013\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000fJ\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00160\u0015J\f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0015J\u000e\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0015H\u0002J\u000e\u0010\u0019\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000fJ\u0010\u0010\u001a\u001a\u00020\u00112\u0006\u0010\u000e\u001a\u00020\u000fH\u0002J\u0010\u0010\u001b\u001a\u00020\u00112\u0006\u0010\u000e\u001a\u00020\u000fH\u0002J\b\u0010\u001c\u001a\u00020\u0011H\u0002J\u000e\u0010\u001d\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\tJ\u0006\u0010\u001e\u001a\u00020\u0011J\u0016\u0010\u001f\u001a\u00020\u00112\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u000f0\u0015H\u0002R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006#"}, d2 = {"Lcom/example/castapp/utils/LetterSpacingPresetManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "gson", "Lcom/google/gson/Gson;", "listeners", "", "Lcom/example/castapp/utils/LetterSpacingPresetManager$LetterSpacingPresetListener;", "sharedPreferences", "Landroid/content/SharedPreferences;", "addCustomLetterSpacing", "", "letterSpacing", "", "addListener", "", "listener", "deleteCustomLetterSpacing", "getAllLetterSpacingOptions", "", "", "getAllLetterSpacings", "getCustomLetterSpacings", "isPresetLetterSpacing", "notifyLetterSpacingAdded", "notifyLetterSpacingDeleted", "notifyLetterSpacingListReset", "removeListener", "resetToDefault", "saveCustomLetterSpacings", "letterSpacings", "Companion", "LetterSpacingPresetListener", "app_debug"})
public final class LetterSpacingPresetManager {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String PREF_NAME = "letter_spacing_preset_preferences";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_CUSTOM_LETTER_SPACINGS = "custom_letter_spacings";
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<java.lang.Float> PRESET_LETTER_SPACINGS = null;
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.example.castapp.utils.LetterSpacingPresetManager INSTANCE;
    @org.jetbrains.annotations.NotNull()
    private final android.content.SharedPreferences sharedPreferences = null;
    @org.jetbrains.annotations.NotNull()
    private final com.google.gson.Gson gson = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Set<com.example.castapp.utils.LetterSpacingPresetManager.LetterSpacingPresetListener> listeners = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.utils.LetterSpacingPresetManager.Companion Companion = null;
    
    private LetterSpacingPresetManager(android.content.Context context) {
        super();
    }
    
    /**
     * 获取完整的字间距列表（预设 + 自定义）
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.Float> getAllLetterSpacings() {
        return null;
    }
    
    /**
     * 获取字间距选项字符串列表（用于Spinner显示）
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getAllLetterSpacingOptions() {
        return null;
    }
    
    /**
     * 获取自定义字间距列表
     */
    private final java.util.List<java.lang.Float> getCustomLetterSpacings() {
        return null;
    }
    
    /**
     * 保存自定义字间距列表
     */
    private final void saveCustomLetterSpacings(java.util.List<java.lang.Float> letterSpacings) {
    }
    
    /**
     * 添加自定义字间距
     */
    public final boolean addCustomLetterSpacing(float letterSpacing) {
        return false;
    }
    
    /**
     * 删除自定义字间距
     */
    public final boolean deleteCustomLetterSpacing(float letterSpacing) {
        return false;
    }
    
    /**
     * 重置为默认字间距设置（清除所有自定义字间距）
     */
    public final void resetToDefault() {
    }
    
    /**
     * 检查字间距是否为预设字间距
     */
    public final boolean isPresetLetterSpacing(float letterSpacing) {
        return false;
    }
    
    /**
     * 添加字间距列表变化监听器
     */
    public final void addListener(@org.jetbrains.annotations.NotNull()
    com.example.castapp.utils.LetterSpacingPresetManager.LetterSpacingPresetListener listener) {
    }
    
    /**
     * 移除字间距列表变化监听器
     */
    public final void removeListener(@org.jetbrains.annotations.NotNull()
    com.example.castapp.utils.LetterSpacingPresetManager.LetterSpacingPresetListener listener) {
    }
    
    private final void notifyLetterSpacingAdded(float letterSpacing) {
    }
    
    private final void notifyLetterSpacingDeleted(float letterSpacing) {
    }
    
    private final void notifyLetterSpacingListReset() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0010\u0007\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\r\u001a\u00020\u00042\u0006\u0010\u000e\u001a\u00020\u000fR\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\f\u00a8\u0006\u0010"}, d2 = {"Lcom/example/castapp/utils/LetterSpacingPresetManager$Companion;", "", "()V", "INSTANCE", "Lcom/example/castapp/utils/LetterSpacingPresetManager;", "KEY_CUSTOM_LETTER_SPACINGS", "", "PREF_NAME", "PRESET_LETTER_SPACINGS", "", "", "getPRESET_LETTER_SPACINGS", "()Ljava/util/List;", "getInstance", "context", "Landroid/content/Context;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.lang.Float> getPRESET_LETTER_SPACINGS() {
            return null;
        }
        
        /**
         * 获取单例实例
         * 使用 applicationContext 避免内存泄漏
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.utils.LetterSpacingPresetManager getInstance(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
            return null;
        }
    }
    
    /**
     * 字间距预设列表变化监听器接口
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0003\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\u0010\u0010\u0006\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\b\u0010\u0007\u001a\u00020\u0003H&\u00a8\u0006\b"}, d2 = {"Lcom/example/castapp/utils/LetterSpacingPresetManager$LetterSpacingPresetListener;", "", "onLetterSpacingAdded", "", "letterSpacing", "", "onLetterSpacingDeleted", "onLetterSpacingListReset", "app_debug"})
    public static abstract interface LetterSpacingPresetListener {
        
        /**
         * 字间距被添加时调用
         */
        public abstract void onLetterSpacingAdded(float letterSpacing);
        
        /**
         * 字间距被删除时调用
         */
        public abstract void onLetterSpacingDeleted(float letterSpacing);
        
        /**
         * 字间距列表被重置时调用
         */
        public abstract void onLetterSpacingListReset();
    }
}