package com.example.castapp.rtp;

/**
 * RTP接收器 - 简化版
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000h\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\t\u0018\u0000 02\u00020\u0001:\u00010B+\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0014\b\u0002\u0010\u0006\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\b0\u0007\u00a2\u0006\u0002\u0010\nJ\u0018\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u001e\u001a\u00020\u000f2\u0006\u0010\u001f\u001a\u00020\fH\u0002J\u0012\u0010 \u001a\u0004\u0018\u00010\u000f2\u0006\u0010!\u001a\u00020\fH\u0002J\u0010\u0010\"\u001a\u00020\u001d2\u0006\u0010#\u001a\u00020$H\u0002J\u0018\u0010%\u001a\u00020\u001d2\u0006\u0010&\u001a\u00020\u00102\u0006\u0010\u001e\u001a\u00020\u000fH\u0002J&\u0010\'\u001a\u0004\u0018\u00010(2\u0012\u0010)\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00100\u000e2\u0006\u0010*\u001a\u00020\u0010H\u0002J\u001e\u0010+\u001a\u0004\u0018\u00010(2\u0012\u0010)\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00100\u000eH\u0002J$\u0010,\u001a\u00020\u00152\u0012\u0010)\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00100\u000e2\u0006\u0010-\u001a\u00020\fH\u0002J\u0006\u0010.\u001a\u00020\u0015J\u0006\u0010/\u001a\u00020\u001dR\u000e\u0010\u000b\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R2\u0010\r\u001a&\u0012\u0004\u0012\u00020\u000f\u0012\u001c\u0012\u001a\u0012\u0004\u0012\u00020\f\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00100\u000e0\u000e0\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0006\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\b0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000RN\u0010\u0012\u001aB\u0012\f\u0012\n \u0014*\u0004\u0018\u00010\u000f0\u000f\u0012\f\u0012\n \u0014*\u0004\u0018\u00010\u00150\u0015 \u0014* \u0012\f\u0012\n \u0014*\u0004\u0018\u00010\u000f0\u000f\u0012\f\u0012\n \u0014*\u0004\u0018\u00010\u00150\u0015\u0018\u00010\u00130\u0013X\u0082\u0004\u00a2\u0006\u0002\n\u0000RN\u0010\u0016\u001aB\u0012\f\u0012\n \u0014*\u0004\u0018\u00010\u000f0\u000f\u0012\f\u0012\n \u0014*\u0004\u0018\u00010\u00150\u0015 \u0014* \u0012\f\u0012\n \u0014*\u0004\u0018\u00010\u000f0\u000f\u0012\f\u0012\n \u0014*\u0004\u0018\u00010\u00150\u0015\u0018\u00010\u00130\u0013X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R&\u0010\u0018\u001a\u001a\u0012\u0004\u0012\u00020\u000f\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\f0\u000e0\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u001bX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00061"}, d2 = {"Lcom/example/castapp/rtp/RtpReceiver;", "", "port", "", "multiConnectionManager", "Lcom/example/castapp/rtp/MultiConnectionManager;", "getConnections", "Lkotlin/Function0;", "", "Lcom/example/castapp/model/Connection;", "(ILcom/example/castapp/rtp/MultiConnectionManager;Lkotlin/jvm/functions/Function0;)V", "failedReassembles", "", "fragmentCache", "Ljava/util/concurrent/ConcurrentHashMap;", "", "Lcom/example/castapp/rtp/RtpPacket;", "lastStatsTime", "processedDisconnects", "Ljava/util/concurrent/ConcurrentHashMap$KeySetView;", "kotlin.jvm.PlatformType", "", "processedScreenResolutions", "successfulReassembles", "timestampLastSeen", "totalFragmentsReceived", "udpReceiver", "Lcom/example/castapp/network/UdpReceiver;", "cleanupExpiredFragments", "", "connectionId", "currentTime", "findConnectionIdBySSRC", "ssrc", "handleRtpDataZeroCopy", "dataView", "Lcom/example/castapp/network/DataView;", "processH264Packet", "packet", "reassembleFragmentsZeroCopy", "Lcom/example/castapp/rtp/PayloadView;", "fragments", "lastPacket", "reassembleFuAFragmentsZeroCopy", "shouldForceReassemble", "waitTime", "start", "stop", "Companion", "app_debug"})
public final class RtpReceiver {
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.rtp.MultiConnectionManager multiConnectionManager = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function0<java.util.List<com.example.castapp.model.Connection>> getConnections = null;
    private static final long PACKET_TIMEOUT_MS = 200L;
    private static final int MAX_FRAGMENTS_PER_FRAME = 600;
    private static final int AGGRESSIVE_CLEANUP_THRESHOLD = 60;
    private static final int MIN_FRAGMENTS_FOR_FORCE_REASSEMBLE = 2;
    private static final long FORCE_REASSEMBLE_TIMEOUT_MS = 120L;
    private static final int STATS_REPORT_INTERVAL = 180000;
    private static final int PARTIAL_REASSEMBLE_MIN_FRAGMENTS = 3;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.network.UdpReceiver udpReceiver = null;
    private long successfulReassembles = 0L;
    private long failedReassembles = 0L;
    private long totalFragmentsReceived = 0L;
    private long lastStatsTime;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, java.util.concurrent.ConcurrentHashMap<java.lang.Long, java.util.concurrent.ConcurrentHashMap<java.lang.Integer, com.example.castapp.rtp.RtpPacket>>> fragmentCache = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, java.util.concurrent.ConcurrentHashMap<java.lang.Long, java.lang.Long>> timestampLastSeen = null;
    private final java.util.concurrent.ConcurrentHashMap.KeySetView<java.lang.String, java.lang.Boolean> processedDisconnects = null;
    private final java.util.concurrent.ConcurrentHashMap.KeySetView<java.lang.String, java.lang.Boolean> processedScreenResolutions = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.rtp.RtpReceiver.Companion Companion = null;
    
    public RtpReceiver(int port, @org.jetbrains.annotations.NotNull()
    com.example.castapp.rtp.MultiConnectionManager multiConnectionManager, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<? extends java.util.List<com.example.castapp.model.Connection>> getConnections) {
        super();
    }
    
    /**
     * 启动RTP接收器
     */
    public final boolean start() {
        return false;
    }
    
    /**
     * 停止RTP接收器
     */
    public final void stop() {
    }
    
    /**
     * 处理接收到的RTP数据 - 智能零拷贝优化（完全零拷贝）
     */
    private final void handleRtpDataZeroCopy(com.example.castapp.network.DataView dataView) {
    }
    
    /**
     * 通过SSRC查找connectionId - 简化版
     */
    private final java.lang.String findConnectionIdBySSRC(long ssrc) {
        return null;
    }
    
    /**
     * 处理H.264 RTP包（统一ID架构）
     */
    private final void processH264Packet(com.example.castapp.rtp.RtpPacket packet, java.lang.String connectionId) {
    }
    
    /**
     * 清理过期的分片（统一ID架构）
     */
    private final void cleanupExpiredFragments(java.lang.String connectionId, long currentTime) {
    }
    
    /**
     * 🎯 简化强制重组判断：基于时间和分片数量的简单策略
     */
    private final boolean shouldForceReassemble(java.util.concurrent.ConcurrentHashMap<java.lang.Integer, com.example.castapp.rtp.RtpPacket> fragments, long waitTime) {
        return false;
    }
    
    /**
     * 🚀 零拷贝FU-A分片重组：直接重组到SmartBuffer，完全避免临时ByteArray
     * 这是延迟优化的核心方法，可减少10-15ms的数据拷贝延迟
     */
    private final com.example.castapp.rtp.PayloadView reassembleFuAFragmentsZeroCopy(java.util.concurrent.ConcurrentHashMap<java.lang.Integer, com.example.castapp.rtp.RtpPacket> fragments) {
        return null;
    }
    
    /**
     * 🚀 零拷贝普通分片重组：直接重组到SmartBuffer，避免临时ByteArray
     */
    private final com.example.castapp.rtp.PayloadView reassembleFragmentsZeroCopy(java.util.concurrent.ConcurrentHashMap<java.lang.Integer, com.example.castapp.rtp.RtpPacket> fragments, com.example.castapp.rtp.RtpPacket lastPacket) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0002\b\u0006\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/example/castapp/rtp/RtpReceiver$Companion;", "", "()V", "AGGRESSIVE_CLEANUP_THRESHOLD", "", "FORCE_REASSEMBLE_TIMEOUT_MS", "", "MAX_FRAGMENTS_PER_FRAME", "MIN_FRAGMENTS_FOR_FORCE_REASSEMBLE", "PACKET_TIMEOUT_MS", "PARTIAL_REASSEMBLE_MIN_FRAGMENTS", "STATS_REPORT_INTERVAL", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}