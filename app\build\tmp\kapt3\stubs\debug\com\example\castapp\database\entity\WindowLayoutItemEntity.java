package com.example.castapp.database.entity;

/**
 * 窗口布局项实体类
 * 用于存储布局中每个窗口的详细参数信息
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\u0007\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0003\b\u008c\u0001\b\u0087\b\u0018\u0000 \u009d\u00012\u00020\u0001:\u0002\u009d\u0001B\u00b5\u0003\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\b\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\u0006\u0010\t\u001a\u00020\b\u0012\u0006\u0010\n\u001a\u00020\b\u0012\u0006\u0010\u000b\u001a\u00020\u0006\u0012\u0006\u0010\f\u001a\u00020\r\u0012\u0006\u0010\u000e\u001a\u00020\r\u0012\u0006\u0010\u000f\u001a\u00020\r\u0012\u0006\u0010\u0010\u001a\u00020\r\u0012\u0006\u0010\u0011\u001a\u00020\u0012\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0014\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0012\u0012\u0006\u0010\u0016\u001a\u00020\u0012\u0012\u0006\u0010\u0017\u001a\u00020\u0012\u0012\u0006\u0010\u0018\u001a\u00020\r\u0012\u0006\u0010\u0019\u001a\u00020\r\u0012\u0006\u0010\u001a\u001a\u00020\u0012\u0012\b\b\u0002\u0010\u001b\u001a\u00020\u0012\u0012\b\b\u0002\u0010\u001c\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u001d\u001a\u00020\r\u0012\n\b\u0002\u0010\u001e\u001a\u0004\u0018\u00010\b\u0012\b\b\u0002\u0010\u001f\u001a\u00020\u0006\u0012\b\b\u0002\u0010 \u001a\u00020\u0006\u0012\n\b\u0002\u0010!\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\"\u001a\u0004\u0018\u00010\b\u0012\b\b\u0002\u0010#\u001a\u00020\u0012\u0012\b\b\u0002\u0010$\u001a\u00020\u0012\u0012\b\b\u0002\u0010%\u001a\u00020\u0006\u0012\n\b\u0002\u0010&\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\'\u001a\u0004\u0018\u00010\b\u0012\b\b\u0002\u0010(\u001a\u00020\r\u0012\b\b\u0002\u0010)\u001a\u00020\u0006\u0012\n\b\u0002\u0010*\u001a\u0004\u0018\u00010\b\u0012\b\b\u0002\u0010+\u001a\u00020\u0012\u0012\b\b\u0002\u0010,\u001a\u00020\u0006\u0012\n\b\u0002\u0010-\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010.\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010/\u001a\u0004\u0018\u00010\b\u0012\b\b\u0002\u00100\u001a\u00020\u0012\u0012\b\b\u0002\u00101\u001a\u00020\u0006\u0012\b\b\u0002\u00102\u001a\u00020\u0006\u0012\b\b\u0002\u00103\u001a\u00020\u0012\u00a2\u0006\u0002\u00104J\t\u0010^\u001a\u00020\u0003H\u00c6\u0003J\t\u0010_\u001a\u00020\rH\u00c6\u0003J\t\u0010`\u001a\u00020\rH\u00c6\u0003J\t\u0010a\u001a\u00020\u0012H\u00c6\u0003J\t\u0010b\u001a\u00020\u0012H\u00c6\u0003J\t\u0010c\u001a\u00020\u0012H\u00c6\u0003J\t\u0010d\u001a\u00020\u0012H\u00c6\u0003J\t\u0010e\u001a\u00020\u0012H\u00c6\u0003J\t\u0010f\u001a\u00020\u0012H\u00c6\u0003J\t\u0010g\u001a\u00020\rH\u00c6\u0003J\t\u0010h\u001a\u00020\rH\u00c6\u0003J\t\u0010i\u001a\u00020\u0003H\u00c6\u0003J\t\u0010j\u001a\u00020\u0012H\u00c6\u0003J\t\u0010k\u001a\u00020\u0012H\u00c6\u0003J\t\u0010l\u001a\u00020\u0006H\u00c6\u0003J\t\u0010m\u001a\u00020\rH\u00c6\u0003J\u000b\u0010n\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\t\u0010o\u001a\u00020\u0006H\u00c6\u0003J\t\u0010p\u001a\u00020\u0006H\u00c6\u0003J\u000b\u0010q\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\u000b\u0010r\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\t\u0010s\u001a\u00020\u0012H\u00c6\u0003J\t\u0010t\u001a\u00020\u0006H\u00c6\u0003J\t\u0010u\u001a\u00020\u0012H\u00c6\u0003J\t\u0010v\u001a\u00020\u0006H\u00c6\u0003J\u000b\u0010w\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\u000b\u0010x\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\t\u0010y\u001a\u00020\rH\u00c6\u0003J\t\u0010z\u001a\u00020\u0006H\u00c6\u0003J\u000b\u0010{\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\t\u0010|\u001a\u00020\u0012H\u00c6\u0003J\t\u0010}\u001a\u00020\u0006H\u00c6\u0003J\u000b\u0010~\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\u000b\u0010\u007f\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\f\u0010\u0080\u0001\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\f\u0010\u0081\u0001\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\n\u0010\u0082\u0001\u001a\u00020\u0012H\u00c6\u0003J\n\u0010\u0083\u0001\u001a\u00020\u0006H\u00c6\u0003J\n\u0010\u0084\u0001\u001a\u00020\u0006H\u00c6\u0003J\n\u0010\u0085\u0001\u001a\u00020\u0012H\u00c6\u0003J\n\u0010\u0086\u0001\u001a\u00020\bH\u00c6\u0003J\n\u0010\u0087\u0001\u001a\u00020\bH\u00c6\u0003J\n\u0010\u0088\u0001\u001a\u00020\u0006H\u00c6\u0003J\n\u0010\u0089\u0001\u001a\u00020\rH\u00c6\u0003J\n\u0010\u008a\u0001\u001a\u00020\rH\u00c6\u0003J\u00e0\u0003\u0010\u008b\u0001\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\b\b\u0002\u0010\t\u001a\u00020\b2\b\b\u0002\u0010\n\u001a\u00020\b2\b\b\u0002\u0010\u000b\u001a\u00020\u00062\b\b\u0002\u0010\f\u001a\u00020\r2\b\b\u0002\u0010\u000e\u001a\u00020\r2\b\b\u0002\u0010\u000f\u001a\u00020\r2\b\b\u0002\u0010\u0010\u001a\u00020\r2\b\b\u0002\u0010\u0011\u001a\u00020\u00122\b\b\u0002\u0010\u0013\u001a\u00020\u00122\b\b\u0002\u0010\u0014\u001a\u00020\u00122\b\b\u0002\u0010\u0015\u001a\u00020\u00122\b\b\u0002\u0010\u0016\u001a\u00020\u00122\b\b\u0002\u0010\u0017\u001a\u00020\u00122\b\b\u0002\u0010\u0018\u001a\u00020\r2\b\b\u0002\u0010\u0019\u001a\u00020\r2\b\b\u0002\u0010\u001a\u001a\u00020\u00122\b\b\u0002\u0010\u001b\u001a\u00020\u00122\b\b\u0002\u0010\u001c\u001a\u00020\u00062\b\b\u0002\u0010\u001d\u001a\u00020\r2\n\b\u0002\u0010\u001e\u001a\u0004\u0018\u00010\b2\b\b\u0002\u0010\u001f\u001a\u00020\u00062\b\b\u0002\u0010 \u001a\u00020\u00062\n\b\u0002\u0010!\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\"\u001a\u0004\u0018\u00010\b2\b\b\u0002\u0010#\u001a\u00020\u00122\b\b\u0002\u0010$\u001a\u00020\u00122\b\b\u0002\u0010%\u001a\u00020\u00062\n\b\u0002\u0010&\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\'\u001a\u0004\u0018\u00010\b2\b\b\u0002\u0010(\u001a\u00020\r2\b\b\u0002\u0010)\u001a\u00020\u00062\n\b\u0002\u0010*\u001a\u0004\u0018\u00010\b2\b\b\u0002\u0010+\u001a\u00020\u00122\b\b\u0002\u0010,\u001a\u00020\u00062\n\b\u0002\u0010-\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010.\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010/\u001a\u0004\u0018\u00010\b2\b\b\u0002\u00100\u001a\u00020\u00122\b\b\u0002\u00101\u001a\u00020\u00062\b\b\u0002\u00102\u001a\u00020\u00062\b\b\u0002\u00103\u001a\u00020\u0012H\u00c6\u0001J\u0015\u0010\u008c\u0001\u001a\u00020\u00122\t\u0010\u008d\u0001\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u0007\u0010\u008e\u0001\u001a\u00020\bJ\u0007\u0010\u008f\u0001\u001a\u00020\bJ\u0007\u0010\u0090\u0001\u001a\u00020\bJ\u0007\u0010\u0091\u0001\u001a\u00020\bJ\u0007\u0010\u0092\u0001\u001a\u00020\bJ\u0007\u0010\u0093\u0001\u001a\u00020\bJ\u0007\u0010\u0094\u0001\u001a\u00020\bJ\u0007\u0010\u0095\u0001\u001a\u00020\bJ\u0007\u0010\u0096\u0001\u001a\u00020\bJ\u0007\u0010\u0097\u0001\u001a\u00020\bJ\u0007\u0010\u0098\u0001\u001a\u00020\bJ\u0007\u0010\u0099\u0001\u001a\u00020\bJ\u0007\u0010\u009a\u0001\u001a\u00020\bJ\n\u0010\u009b\u0001\u001a\u00020\u0006H\u00d6\u0001J\n\u0010\u009c\u0001\u001a\u00020\bH\u00d6\u0001R\u0011\u0010\u0019\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b5\u00106R\u0011\u0010 \u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b7\u00108R\u0011\u0010\u001f\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b9\u00108R\u0011\u0010\u001c\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b:\u00108R\u0011\u0010\u001d\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b;\u00106R\u0011\u0010\u0018\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b<\u00106R\u0013\u0010\u001e\u001a\u0004\u0018\u00010\b\u00a2\u0006\b\n\u0000\u001a\u0004\b=\u0010>R\u0011\u0010\t\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b?\u0010>R\u0013\u0010\u0007\u001a\u0004\u0018\u00010\b\u00a2\u0006\b\n\u0000\u001a\u0004\b@\u0010>R\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\bA\u0010BR\u0011\u0010\n\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\bC\u0010>R\u0011\u0010\u001b\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010DR\u0011\u0010\u001a\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010DR\u0011\u0010\u0011\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010DR\u0011\u0010\u0013\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010DR\u0011\u00103\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b3\u0010DR\u0011\u0010\u0017\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010DR\u0011\u0010\u0015\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010DR\u0011\u0010\u0014\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010DR\u0011\u0010\u0016\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010DR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bE\u0010BR\u0013\u0010/\u001a\u0004\u0018\u00010\b\u00a2\u0006\b\n\u0000\u001a\u0004\bF\u0010>R\u0013\u0010.\u001a\u0004\u0018\u00010\b\u00a2\u0006\b\n\u0000\u001a\u0004\bG\u0010>R\u0013\u0010-\u001a\u0004\u0018\u00010\b\u00a2\u0006\b\n\u0000\u001a\u0004\bH\u0010>R\u0013\u0010!\u001a\u0004\u0018\u00010\b\u00a2\u0006\b\n\u0000\u001a\u0004\bI\u0010>R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\bJ\u00108R\u0011\u0010\u000b\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\bK\u00108R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\bL\u00106R\u0011\u0010\u000e\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\bM\u00106R\u0013\u0010*\u001a\u0004\u0018\u00010\b\u00a2\u0006\b\n\u0000\u001a\u0004\bN\u0010>R\u0011\u0010\u0010\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\bO\u00106R\u0011\u0010\u000f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\bP\u00106R\u0011\u0010)\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\bQ\u00108R\u0013\u0010\"\u001a\u0004\u0018\u00010\b\u00a2\u0006\b\n\u0000\u001a\u0004\bR\u0010>R\u0013\u0010\'\u001a\u0004\u0018\u00010\b\u00a2\u0006\b\n\u0000\u001a\u0004\bS\u0010>R\u0013\u0010&\u001a\u0004\u0018\u00010\b\u00a2\u0006\b\n\u0000\u001a\u0004\bT\u0010>R\u0011\u0010%\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\bU\u00108R\u0011\u0010#\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\bV\u0010DR\u0011\u0010$\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\bW\u0010DR\u0011\u0010(\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\bX\u00106R\u0011\u00101\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\bY\u00108R\u0011\u00100\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\bZ\u0010DR\u0011\u00102\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b[\u00108R\u0011\u0010,\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\\\u00108R\u0011\u0010+\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b]\u0010D\u00a8\u0006\u009e\u0001"}, d2 = {"Lcom/example/castapp/database/entity/WindowLayoutItemEntity;", "", "id", "", "layoutId", "orderIndex", "", "deviceName", "", "deviceId", "ipAddress", "port", "positionX", "", "positionY", "scaleFactor", "rotationAngle", "isCropping", "", "isDragEnabled", "isScaleEnabled", "isRotationEnabled", "isVisible", "isMirrored", "cornerRadius", "alpha", "isControlEnabled", "isBorderEnabled", "borderColor", "borderWidth", "cropRect", "baseWindowWidth", "baseWindowHeight", "note", "textContent", "textIsBold", "textIsItalic", "textFontSize", "textFontName", "textFontFamily", "textLineSpacing", "textAlignment", "richTextData", "windowColorEnabled", "windowBackgroundColor", "mediaFileUri", "mediaFileName", "mediaContentType", "videoPlayEnabled", "videoLoopCount", "videoVolume", "isLandscapeModeEnabled", "(JJILjava/lang/String;Ljava/lang/String;Ljava/lang/String;IFFFFZZZZZZFFZZIFLjava/lang/String;IILjava/lang/String;Ljava/lang/String;ZZILjava/lang/String;Ljava/lang/String;FILjava/lang/String;ZILjava/lang/String;Ljava/lang/String;Ljava/lang/String;ZIIZ)V", "getAlpha", "()F", "getBaseWindowHeight", "()I", "getBaseWindowWidth", "getBorderColor", "getBorderWidth", "getCornerRadius", "getCropRect", "()Ljava/lang/String;", "getDeviceId", "getDeviceName", "getId", "()J", "getIpAddress", "()Z", "getLayoutId", "getMediaContentType", "getMediaFileName", "getMediaFileUri", "getNote", "getOrderIndex", "getPort", "getPositionX", "getPositionY", "getRichTextData", "getRotationAngle", "getScaleFactor", "getTextAlignment", "getTextContent", "getTextFontFamily", "getTextFontName", "getTextFontSize", "getTextIsBold", "getTextIsItalic", "getTextLineSpacing", "getVideoLoopCount", "getVideoPlayEnabled", "getVideoVolume", "getWindowBackgroundColor", "getWindowColorEnabled", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component20", "component21", "component22", "component23", "component24", "component25", "component26", "component27", "component28", "component29", "component3", "component30", "component31", "component32", "component33", "component34", "component35", "component36", "component37", "component38", "component39", "component4", "component40", "component41", "component42", "component43", "component44", "component45", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "getAlphaDisplayText", "getBorderColorDisplayText", "getBorderStatusDisplayText", "getBorderWidthDisplayText", "getCornerRadiusDisplayText", "getCropDisplayText", "getDisplayDeviceInfo", "getLandscapeModeDisplayText", "getMirrorDisplayText", "getNoteDisplayText", "getShortConnectionId", "getVideoPlayStatusDisplayText", "getVisibilityDisplayText", "hashCode", "toString", "Companion", "app_debug"})
@androidx.room.Entity(tableName = "window_layout_items", foreignKeys = {@androidx.room.ForeignKey(entity = com.example.castapp.database.entity.WindowLayoutEntity.class, parentColumns = {"id"}, childColumns = {"layoutId"}, onDelete = 5)}, indices = {@androidx.room.Index(value = {"layoutId"})})
public final class WindowLayoutItemEntity {
    @androidx.room.PrimaryKey(autoGenerate = true)
    private final long id = 0L;
    
    /**
     * 所属布局的ID
     */
    private final long layoutId = 0L;
    
    /**
     * 窗口在布局中的顺序（用于恢复时的层级顺序）
     */
    private final int orderIndex = 0;
    
    /**
     * 设备名称
     */
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String deviceName = null;
    
    /**
     * 设备ID（连接ID）
     */
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String deviceId = null;
    
    /**
     * IP地址
     */
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String ipAddress = null;
    
    /**
     * 端口号
     */
    private final int port = 0;
    
    /**
     * 窗口X坐标
     */
    private final float positionX = 0.0F;
    
    /**
     * 窗口Y坐标
     */
    private final float positionY = 0.0F;
    
    /**
     * 缩放倍数
     */
    private final float scaleFactor = 0.0F;
    
    /**
     * 旋转角度
     */
    private final float rotationAngle = 0.0F;
    
    /**
     * 是否正在裁剪
     */
    private final boolean isCropping = false;
    
    /**
     * 拖动功能是否启用
     */
    private final boolean isDragEnabled = false;
    
    /**
     * 缩放功能是否启用
     */
    private final boolean isScaleEnabled = false;
    
    /**
     * 旋转功能是否启用
     */
    private final boolean isRotationEnabled = false;
    
    /**
     * 窗口是否可见
     */
    private final boolean isVisible = false;
    
    /**
     * 镜像功能是否启用
     */
    private final boolean isMirrored = false;
    
    /**
     * 圆角半径
     */
    private final float cornerRadius = 0.0F;
    
    /**
     * 透明度
     */
    private final float alpha = 0.0F;
    
    /**
     * 精准调控功能是否启用
     */
    private final boolean isControlEnabled = false;
    
    /**
     * 边框显示是否启用
     */
    private final boolean isBorderEnabled = false;
    
    /**
     * 边框颜色
     */
    private final int borderColor = 0;
    
    /**
     * 边框宽度（dp）
     */
    private final float borderWidth = 0.0F;
    
    /**
     * 裁剪区域参数（JSON格式存储，包含left, top, right, bottom）
     */
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String cropRect = null;
    
    /**
     * 基础窗口宽度（未缩放）
     */
    private final int baseWindowWidth = 0;
    
    /**
     * 基础窗口高度（未缩放）
     */
    private final int baseWindowHeight = 0;
    
    /**
     * 🏷️ 备注信息
     */
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String note = null;
    
    /**
     * 📝 文本格式信息 - 文本内容
     */
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String textContent = null;
    
    /**
     * 📝 文本格式信息 - 是否加粗
     */
    private final boolean textIsBold = false;
    
    /**
     * 📝 文本格式信息 - 是否倾斜
     */
    private final boolean textIsItalic = false;
    
    /**
     * 📝 文本格式信息 - 字号大小
     */
    private final int textFontSize = 0;
    
    /**
     * 📝 文本格式信息 - 字体名称
     */
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String textFontName = null;
    
    /**
     * 📝 文本格式信息 - 字体族
     */
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String textFontFamily = null;
    
    /**
     * 📝 文本格式信息 - 行间距值（dp）
     */
    private final float textLineSpacing = 0.0F;
    
    /**
     * 📝 文本格式信息 - 文本对齐方式
     */
    private final int textAlignment = 0;
    
    /**
     * 📝 富文本格式数据 - JSON格式的完整格式信息
     */
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String richTextData = null;
    
    /**
     * 🎨 窗口背景颜色 - 是否启用
     */
    private final boolean windowColorEnabled = false;
    
    /**
     * 🎨 窗口背景颜色 - 颜色值
     */
    private final int windowBackgroundColor = 0;
    
    /**
     * 📁 媒体文件URI（用于重新创建媒体窗口）
     */
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String mediaFileUri = null;
    
    /**
     * 📁 媒体文件名（用于重新创建媒体窗口）
     */
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String mediaFileName = null;
    
    /**
     * 📁 媒体内容类型（video/image，用于重新创建媒体窗口）
     */
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String mediaContentType = null;
    
    /**
     * 🎬 视频播放开关状态（仅对视频窗口有效）
     */
    private final boolean videoPlayEnabled = false;
    
    /**
     * 🎬 视频播放次数（-1表示无限循环，0表示不循环，>0表示循环指定次数）
     */
    private final int videoLoopCount = 0;
    
    /**
     * 🎬 视频播放音量（0-100的百分比值）
     */
    private final int videoVolume = 0;
    
    /**
     * 🎯 横屏模式检测是否启用
     */
    private final boolean isLandscapeModeEnabled = false;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.database.entity.WindowLayoutItemEntity.Companion Companion = null;
    
    public WindowLayoutItemEntity(long id, long layoutId, int orderIndex, @org.jetbrains.annotations.Nullable()
    java.lang.String deviceName, @org.jetbrains.annotations.NotNull()
    java.lang.String deviceId, @org.jetbrains.annotations.NotNull()
    java.lang.String ipAddress, int port, float positionX, float positionY, float scaleFactor, float rotationAngle, boolean isCropping, boolean isDragEnabled, boolean isScaleEnabled, boolean isRotationEnabled, boolean isVisible, boolean isMirrored, float cornerRadius, float alpha, boolean isControlEnabled, boolean isBorderEnabled, int borderColor, float borderWidth, @org.jetbrains.annotations.Nullable()
    java.lang.String cropRect, int baseWindowWidth, int baseWindowHeight, @org.jetbrains.annotations.Nullable()
    java.lang.String note, @org.jetbrains.annotations.Nullable()
    java.lang.String textContent, boolean textIsBold, boolean textIsItalic, int textFontSize, @org.jetbrains.annotations.Nullable()
    java.lang.String textFontName, @org.jetbrains.annotations.Nullable()
    java.lang.String textFontFamily, float textLineSpacing, int textAlignment, @org.jetbrains.annotations.Nullable()
    java.lang.String richTextData, boolean windowColorEnabled, int windowBackgroundColor, @org.jetbrains.annotations.Nullable()
    java.lang.String mediaFileUri, @org.jetbrains.annotations.Nullable()
    java.lang.String mediaFileName, @org.jetbrains.annotations.Nullable()
    java.lang.String mediaContentType, boolean videoPlayEnabled, int videoLoopCount, int videoVolume, boolean isLandscapeModeEnabled) {
        super();
    }
    
    public final long getId() {
        return 0L;
    }
    
    /**
     * 所属布局的ID
     */
    public final long getLayoutId() {
        return 0L;
    }
    
    /**
     * 窗口在布局中的顺序（用于恢复时的层级顺序）
     */
    public final int getOrderIndex() {
        return 0;
    }
    
    /**
     * 设备名称
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getDeviceName() {
        return null;
    }
    
    /**
     * 设备ID（连接ID）
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDeviceId() {
        return null;
    }
    
    /**
     * IP地址
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getIpAddress() {
        return null;
    }
    
    /**
     * 端口号
     */
    public final int getPort() {
        return 0;
    }
    
    /**
     * 窗口X坐标
     */
    public final float getPositionX() {
        return 0.0F;
    }
    
    /**
     * 窗口Y坐标
     */
    public final float getPositionY() {
        return 0.0F;
    }
    
    /**
     * 缩放倍数
     */
    public final float getScaleFactor() {
        return 0.0F;
    }
    
    /**
     * 旋转角度
     */
    public final float getRotationAngle() {
        return 0.0F;
    }
    
    /**
     * 是否正在裁剪
     */
    public final boolean isCropping() {
        return false;
    }
    
    /**
     * 拖动功能是否启用
     */
    public final boolean isDragEnabled() {
        return false;
    }
    
    /**
     * 缩放功能是否启用
     */
    public final boolean isScaleEnabled() {
        return false;
    }
    
    /**
     * 旋转功能是否启用
     */
    public final boolean isRotationEnabled() {
        return false;
    }
    
    /**
     * 窗口是否可见
     */
    public final boolean isVisible() {
        return false;
    }
    
    /**
     * 镜像功能是否启用
     */
    public final boolean isMirrored() {
        return false;
    }
    
    /**
     * 圆角半径
     */
    public final float getCornerRadius() {
        return 0.0F;
    }
    
    /**
     * 透明度
     */
    public final float getAlpha() {
        return 0.0F;
    }
    
    /**
     * 精准调控功能是否启用
     */
    public final boolean isControlEnabled() {
        return false;
    }
    
    /**
     * 边框显示是否启用
     */
    public final boolean isBorderEnabled() {
        return false;
    }
    
    /**
     * 边框颜色
     */
    public final int getBorderColor() {
        return 0;
    }
    
    /**
     * 边框宽度（dp）
     */
    public final float getBorderWidth() {
        return 0.0F;
    }
    
    /**
     * 裁剪区域参数（JSON格式存储，包含left, top, right, bottom）
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCropRect() {
        return null;
    }
    
    /**
     * 基础窗口宽度（未缩放）
     */
    public final int getBaseWindowWidth() {
        return 0;
    }
    
    /**
     * 基础窗口高度（未缩放）
     */
    public final int getBaseWindowHeight() {
        return 0;
    }
    
    /**
     * 🏷️ 备注信息
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getNote() {
        return null;
    }
    
    /**
     * 📝 文本格式信息 - 文本内容
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getTextContent() {
        return null;
    }
    
    /**
     * 📝 文本格式信息 - 是否加粗
     */
    public final boolean getTextIsBold() {
        return false;
    }
    
    /**
     * 📝 文本格式信息 - 是否倾斜
     */
    public final boolean getTextIsItalic() {
        return false;
    }
    
    /**
     * 📝 文本格式信息 - 字号大小
     */
    public final int getTextFontSize() {
        return 0;
    }
    
    /**
     * 📝 文本格式信息 - 字体名称
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getTextFontName() {
        return null;
    }
    
    /**
     * 📝 文本格式信息 - 字体族
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getTextFontFamily() {
        return null;
    }
    
    /**
     * 📝 文本格式信息 - 行间距值（dp）
     */
    public final float getTextLineSpacing() {
        return 0.0F;
    }
    
    /**
     * 📝 文本格式信息 - 文本对齐方式
     */
    public final int getTextAlignment() {
        return 0;
    }
    
    /**
     * 📝 富文本格式数据 - JSON格式的完整格式信息
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getRichTextData() {
        return null;
    }
    
    /**
     * 🎨 窗口背景颜色 - 是否启用
     */
    public final boolean getWindowColorEnabled() {
        return false;
    }
    
    /**
     * 🎨 窗口背景颜色 - 颜色值
     */
    public final int getWindowBackgroundColor() {
        return 0;
    }
    
    /**
     * 📁 媒体文件URI（用于重新创建媒体窗口）
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getMediaFileUri() {
        return null;
    }
    
    /**
     * 📁 媒体文件名（用于重新创建媒体窗口）
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getMediaFileName() {
        return null;
    }
    
    /**
     * 📁 媒体内容类型（video/image，用于重新创建媒体窗口）
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getMediaContentType() {
        return null;
    }
    
    /**
     * 🎬 视频播放开关状态（仅对视频窗口有效）
     */
    public final boolean getVideoPlayEnabled() {
        return false;
    }
    
    /**
     * 🎬 视频播放次数（-1表示无限循环，0表示不循环，>0表示循环指定次数）
     */
    public final int getVideoLoopCount() {
        return 0;
    }
    
    /**
     * 🎬 视频播放音量（0-100的百分比值）
     */
    public final int getVideoVolume() {
        return 0;
    }
    
    /**
     * 🎯 横屏模式检测是否启用
     */
    public final boolean isLandscapeModeEnabled() {
        return false;
    }
    
    /**
     * 获取显示用的设备信息
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDisplayDeviceInfo() {
        return null;
    }
    
    /**
     * 🏷️ 获取备注显示文本
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNoteDisplayText() {
        return null;
    }
    
    /**
     * 获取简化的连接ID（显示规则）
     * - 摄像头窗口：显示完整ID
     * - 媒体窗口：显示文件名前8位
     * - 文本窗口：显示UUID后8位
     * - 投屏窗口：显示后8位
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getShortConnectionId() {
        return null;
    }
    
    /**
     * 获取裁剪状态显示文本
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCropDisplayText() {
        return null;
    }
    
    /**
     * 获取可见性显示文本
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getVisibilityDisplayText() {
        return null;
    }
    
    /**
     * 获取镜像状态显示文本
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getMirrorDisplayText() {
        return null;
    }
    
    /**
     * 🎯 获取横屏模式状态显示文本
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getLandscapeModeDisplayText() {
        return null;
    }
    
    /**
     * 获取圆角显示文本
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCornerRadiusDisplayText() {
        return null;
    }
    
    /**
     * 获取透明度显示文本
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getAlphaDisplayText() {
        return null;
    }
    
    /**
     * 获取边框状态显示文本
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getBorderStatusDisplayText() {
        return null;
    }
    
    /**
     * 获取边框宽度显示文本
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getBorderWidthDisplayText() {
        return null;
    }
    
    /**
     * 获取边框颜色显示文本
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getBorderColorDisplayText() {
        return null;
    }
    
    /**
     * 🎬 获取视频播放状态显示文本
     * 格式：播放:开启、次数:2、音量:36%
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getVideoPlayStatusDisplayText() {
        return null;
    }
    
    public final long component1() {
        return 0L;
    }
    
    public final float component10() {
        return 0.0F;
    }
    
    public final float component11() {
        return 0.0F;
    }
    
    public final boolean component12() {
        return false;
    }
    
    public final boolean component13() {
        return false;
    }
    
    public final boolean component14() {
        return false;
    }
    
    public final boolean component15() {
        return false;
    }
    
    public final boolean component16() {
        return false;
    }
    
    public final boolean component17() {
        return false;
    }
    
    public final float component18() {
        return 0.0F;
    }
    
    public final float component19() {
        return 0.0F;
    }
    
    public final long component2() {
        return 0L;
    }
    
    public final boolean component20() {
        return false;
    }
    
    public final boolean component21() {
        return false;
    }
    
    public final int component22() {
        return 0;
    }
    
    public final float component23() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component24() {
        return null;
    }
    
    public final int component25() {
        return 0;
    }
    
    public final int component26() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component27() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component28() {
        return null;
    }
    
    public final boolean component29() {
        return false;
    }
    
    public final int component3() {
        return 0;
    }
    
    public final boolean component30() {
        return false;
    }
    
    public final int component31() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component32() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component33() {
        return null;
    }
    
    public final float component34() {
        return 0.0F;
    }
    
    public final int component35() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component36() {
        return null;
    }
    
    public final boolean component37() {
        return false;
    }
    
    public final int component38() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component39() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component40() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component41() {
        return null;
    }
    
    public final boolean component42() {
        return false;
    }
    
    public final int component43() {
        return 0;
    }
    
    public final int component44() {
        return 0;
    }
    
    public final boolean component45() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component6() {
        return null;
    }
    
    public final int component7() {
        return 0;
    }
    
    public final float component8() {
        return 0.0F;
    }
    
    public final float component9() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.database.entity.WindowLayoutItemEntity copy(long id, long layoutId, int orderIndex, @org.jetbrains.annotations.Nullable()
    java.lang.String deviceName, @org.jetbrains.annotations.NotNull()
    java.lang.String deviceId, @org.jetbrains.annotations.NotNull()
    java.lang.String ipAddress, int port, float positionX, float positionY, float scaleFactor, float rotationAngle, boolean isCropping, boolean isDragEnabled, boolean isScaleEnabled, boolean isRotationEnabled, boolean isVisible, boolean isMirrored, float cornerRadius, float alpha, boolean isControlEnabled, boolean isBorderEnabled, int borderColor, float borderWidth, @org.jetbrains.annotations.Nullable()
    java.lang.String cropRect, int baseWindowWidth, int baseWindowHeight, @org.jetbrains.annotations.Nullable()
    java.lang.String note, @org.jetbrains.annotations.Nullable()
    java.lang.String textContent, boolean textIsBold, boolean textIsItalic, int textFontSize, @org.jetbrains.annotations.Nullable()
    java.lang.String textFontName, @org.jetbrains.annotations.Nullable()
    java.lang.String textFontFamily, float textLineSpacing, int textAlignment, @org.jetbrains.annotations.Nullable()
    java.lang.String richTextData, boolean windowColorEnabled, int windowBackgroundColor, @org.jetbrains.annotations.Nullable()
    java.lang.String mediaFileUri, @org.jetbrains.annotations.Nullable()
    java.lang.String mediaFileName, @org.jetbrains.annotations.Nullable()
    java.lang.String mediaContentType, boolean videoPlayEnabled, int videoLoopCount, int videoVolume, boolean isLandscapeModeEnabled) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J*\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f\u00a8\u0006\r"}, d2 = {"Lcom/example/castapp/database/entity/WindowLayoutItemEntity$Companion;", "", "()V", "fromCastWindowInfo", "Lcom/example/castapp/database/entity/WindowLayoutItemEntity;", "layoutId", "", "orderIndex", "", "windowInfo", "Lcom/example/castapp/model/CastWindowInfo;", "context", "Landroid/content/Context;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * 从CastWindowInfo创建WindowLayoutItemEntity
         * @param layoutId 布局ID
         * @param orderIndex 在布局中的顺序索引（用于层级恢复）
         * @param windowInfo 窗口信息（包含层级参数）
         * @param context 上下文，用于获取文本格式信息（可选）
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.database.entity.WindowLayoutItemEntity fromCastWindowInfo(long layoutId, int orderIndex, @org.jetbrains.annotations.NotNull()
        com.example.castapp.model.CastWindowInfo windowInfo, @org.jetbrains.annotations.Nullable()
        android.content.Context context) {
            return null;
        }
    }
}