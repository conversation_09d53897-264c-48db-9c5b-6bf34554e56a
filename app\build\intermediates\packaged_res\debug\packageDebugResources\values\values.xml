<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#FF000000</color>
    <color name="gray_dark">#757575</color>
    <color name="gray_light">#F5F5F5</color>
    <color name="gray_medium">#E0E0E0</color>
    <color name="primary_blue">#2196F3</color>
    <color name="primary_blue_dark">#1976D2</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="selected_gray">#E0E0E0</color>
    <color name="selected_gray_dark">#BDBDBD</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="text_primary">#212121</color>
    <color name="text_secondary">#757575</color>
    <color name="white">#FFFFFFFF</color>
    <string name="add_receiver">添加接收端</string>
    <string name="add_remote_connection">添加远程连接</string>
    <string name="alpha_percentage">%d%%</string>
    <string name="app_name">投屏助手</string>
    <string name="bitrate_format">%d Mbps</string>
    <string name="cancel">取消</string>
    <string name="casting">投屏中</string>
    <string name="close">关闭</string>
    <string name="confirm">确定</string>
    <string name="connect">连接</string>
    <string name="connected">已连接</string>
    <string name="connection_count_format">%d个连接</string>
    <string name="connection_id_format">ID: %s</string>
    <string name="connection_id_parentheses_format">(%s)</string>
    <string name="connection_list">连接列表</string>
    <string name="control">控制</string>
    <string name="device_count_format">%d个设备</string>
    <string name="device_name">设备名称</string>
    <string name="disconnect">断开</string>
    <string name="disconnected">未连接</string>
    <string name="floating_stopwatch">悬浮秒表</string>
    <string name="invalid_ip">IP地址格式不正确</string>
    <string name="invalid_port">端口号必须在1024-65535之间</string>
    <string name="ip_address">IP地址</string>
    <string name="layer_format">层级：%d</string>
    <string name="media_audio_volume">媒体音量</string>
    <string name="mic_audio_volume">麦克风音量</string>
    <string name="network_error">网络错误</string>
    <string name="order_number_format">%d. </string>
    <string name="overlay_permission_required">需要悬浮窗权限才能使用秒表功能</string>
    <string name="permission_denied">权限被拒绝</string>
    <string name="port">端口号</string>
    <string formatted="false" name="position_format">位置（%d,%d）</string>
    <string name="receive_button">接收</string>
    <string name="receiver_settings">接收端设置</string>
    <string name="remote_control">远程被控</string>
    <string name="remote_control_address_format">%s:9999</string>
    <string name="remote_control_manager">遥控管理</string>
    <string name="remote_control_server_start_failed">远程被控服务器启动失败</string>
    <string name="remote_control_server_started">远程被控服务器已启动</string>
    <string name="remote_control_server_stopped">远程被控服务器已停止</string>
    <string name="remote_device_list">远程设备列表</string>
    <string name="resolution_info_format">原始: %1$d×%2$d | 当前: %3$d×%4$d</string>
    <string name="resolution_retry_format">分辨率调整失败，正在重试... (%1$d/%2$d)</string>
    <string name="rotation_format">旋转：%d°</string>
    <string name="scale_format">缩放：%.1f</string>
    <string name="send_button">发送</string>
    <string name="sender_settings">发送端设置</string>
    <string name="server_running">服务器运行中</string>
    <string name="start_server">启动服务器</string>
    <string name="stop_server">停止服务器</string>
    <string name="stopwatch">秒表</string>
    <string name="stopwatch_running">秒表正在运行中...</string>
    <string name="stopwatch_start_failed">启动悬浮秒表失败</string>
    <string name="stopwatch_started">悬浮秒表已启动</string>
    <string name="volume_format">%d%%</string>
    <string name="window_count_format">（%d个窗口）</string>
    <style name="DialogTheme" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowBackground">@android:color/white</item>
        <item name="android:windowMinWidthMajor">90%</item>
        <item name="android:windowMinWidthMinor">90%</item>
    </style>
    <style name="SwitchTheme" parent="Theme.AppCompat.Light">
        <item name="colorAccent">#4CAF50</item>
        <item name="android:colorForeground">#42A5F5</item>
    </style>
    <style name="TabTextStyle" parent="TextAppearance.Design.Tab">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
    </style>
</resources>